// API文件: 画面管理相关接口
import request from '@/config/axios';

/**
 * 创建工艺流程图
 * @param data 画面数据，包含：
 *  - name: 画面名称
 *  - factoryId: 水厂ID
 *  - svgContent: SVG内容（包含监测点）
 *  - svgWidth: SVG图宽度
 *  - svgHeight: SVG图高度
 *  - points: 监测点数组，直接传递，每个点位包含:
 *      - screenId: 关联画面ID (必需，由后端处理)
 *      - name: 点位名称 (文本类型必传)
 *      - showType: 点位类型 ('circle', 'square', 'triangle')
 *      - indicatorCode: 关联监测指标code
 *      - positionX: 点位X坐标
 *      - positionY: 点位Y坐标
 *      - color: 点位（文本）颜色
 *      - showDisplay: 文本监测点是否显示边框（0-不显示，1-显示）
 *  - extraJson: 其他扩展信息的JSON字符串
 */
export function createScreen(data: any) {
  console.log("提交的数据--------",data);

  // 使用upload方法处理FormData
  return request.postOriginal({ url: '/monitor/screenManage/create', data})
    .then(res => res.data)
}

/**
 * 更新工艺流程图
 * @param data 画面数据，包含：
 *  - id: 画面ID
 *  - name: 画面名称
 *  - svgContent: SVG内容（包含监测点）
 *  - svgWidth: SVG图宽度
 *  - svgHeight: SVG图高度
 *  - points: 监测点数组，直接传递，每个点位包含:
 *      - screenId: 关联画面ID (必需，由后端处理)
 *      - name: 点位名称 (文本类型必传)
 *      - showType: 点位类型 ('circle', 'square', 'triangle')
 *      - indicatorCode: 关联监测指标code
 *      - positionX: 点位X坐标
 *      - positionY: 点位Y坐标
 *      - color: 点位（文本）颜色
 *      - showDisplay: 文本监测点是否显示边框（0-不显示，1-显示）
 *  - extraJson: 其他扩展信息的JSON字符串
 */
export function updateScreen(data: any) {
  console.log("更新的数据--------",data);

  return request.postOriginal({ url: '/monitor/screenManage/update', data})
    .then(res => res.data)
}

// 获取工艺流程图列表
export function getScreenListByFactoryId( factoryId?: number | string) {
  const params: any = {}
  params.factoryId = factoryId

  return request.getOriginal({
    url: '/monitor/screenManage/list',
    params
  }).then(res => res.data)
}


// 获取工艺流程图列表
export function getScreenById(screenId?: number | string) {
  const params: any = {
    screenId: screenId
  }
  return request.getOriginal({
    url: '/monitor/screenManage/getFlowByScreenId',
    params
  }).then(res => res.data)
}

// 删除工艺流程图
export function deleteScreen(id: string | number) {
  return request.delete({
    url: '/monitor/screenManage/deleteScreen',
    params: { id }
  });
}

// // 使用FormData直接上传
// export function createScreenWithFormData(data: FormData) {
//   console.log("使用FormData上传----------------", data);

//   // 直接使用axios发送FormData
//   return axios.post(config.base_url + '/monitor/screenManage/create', data, {
//     headers: {
//       'Content-Type': 'multipart/form-data'
//     }
//   })
// }

// // 获取工艺流程图详情
// export function getScreenDetail(id: string | number) {
//   return request({
//     url: `/monitor/screenManage/detail/${id}`,
//     method: 'get'
//   });
// }

// 获取监测指标列表
export function getIndicatorList(factoryId: number | string) {
  return request.getOriginal({
    url: '/monitor/ods/allMapping',
    params: { factoryId }
  })
}


/**
 * 批量获取历史数据
 * @param data 查询参数
 *  - processId: 工艺段ID
 *  - startTime: 开始时间的ISO字符串
 *  - endTime: 结束时间的ISO字符串
 *  - indicators: 指标代码数组
 *  - interval: 数据间隔（分钟）
 * @returns 历史数据响应
 */
export function getBatchHistoryData(data: {
  processId: string | number;
  startTime: string;
  endTime: string;
  indicators?: string[];
  interval?: number;
}) {
  return request.postOriginal({
    url: '/monitor/history/batchQuery',
    data
  }).then(res => res.data)
}

/**
 * 获取画面实时监测点数据
 * @param data 请求数据
 *  - screenId: 画面ID
 *  - factoryCode: 水厂编码
 * @returns 实时数据响应
 */
export function getScreenRealTimeData(data: {
  screenId: string | number;
  factoryCode: string;
}) {
  return request.postOriginal({
    url: '/monitor/flow-point/get-indicator-last-data-by-screen',
    data
  }).then(res => res.data)
}

/**
 * 获取指定时间段的监测数据
 * @param data 请求数据
 *  - screenId: 画面ID
 *  - factoryCode: 水厂编码
 *  - startTime: 开始时间 yyyy-MM-dd HH:mm:ss
 *  - endTime: 结束时间 yyyy-MM-dd HH:mm:ss
 * @returns 指定时间段的监测数据响应
 */
export function getIndicatorDataByScreenAndTime(data: {
  screenId: string | number;
  factoryCode: string;
  startTime: string;
  endTime: string;
}) {
  return request.postOriginal({
    url: '/monitor/flow-point/query-indicator-data-by-screenAndTime',
    data
  }).then(res => res.data)
}

/**
 * 获取监测点详细信息
 * @param indicatorCode 监测点编码
 * @returns 监测点详细信息
 */
export function getIndicatorDetailByCode(indicatorCode: string) {
  return request.getOriginal({
    url: '/monitor/flow-point/get-indicator-detail-by-code',
    params: { indicatorCode }
  }).then(res => res.data)
}

/**
 * 通过画面ID获取Points数据
 * @param screenId 画面ID
 * @returns Points数据
 */
export function getPointsDataByScreenId(screenId: string | number) {
  return request.getOriginal({
    url: '/monitor/flow-point/getPointsDataByScreenId',
    params: { screenId }
  }).then(res => res.data)
}
