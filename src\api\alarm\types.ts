/**
 * 告警相关接口类型定义
 */

// 分页请求参数
export interface PageParams {
  page: number
  pageSize: number
  [key: string]: any
}

// 分页响应结果
export interface PageResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 规则因子类型
export interface RuleFactorDO {
  id?: number             // 主键ID
  ruleId?: number         // 关联告警规则ID
  variable?: string       // 表达式中的变量名（如 JS_COD）
  indicatorId?: number    // 指标唯一id
  operator?: string       // 比较符号：>、<、>=、<=、=、!=
  threshold?: number      // 阈值
  factorCode?: string     // 指标编码（如 COD_01）
  factorType?: string     // 指标类型
  factorName?: string     // 因子名称
  unit?: string           // 单位（可选，用于前端显示）
  description?: string    // 变量说明（可选）
}

// 告警规则类型
export interface AlarmRule {
  id?: number | string    // 主键ID，编辑时必传，新增时不传
  name: string            // 规则名称
  factoryId: string       // 水厂ID
  factoryName?: string    // 水厂名称
  ruleType: string        // 规则类型：single=单因子，combination=组合因子
  expression?: string     // 组合表达式（组合因子时使用）
  level: string           // 告警级别 I=重要，II=一般
  enabled?: boolean       // 状态：1=启用，0=停用
  remark?: string         // 备注
  ruleFactors?: RuleFactorDO[] // 告警规则因子表（组合表达式变量配置/监测因子）列表
  createTime?: string     // 创建时间
  updateTime?: string     // 更新时间
  creator?: string        // 创建者ID
  updater?: string        // 更新者ID
  deleted?: boolean       // 是否删除
}

// 告警级别枚举
export enum AlarmLevel {
  I = 'I',                // 重要
  II = 'II'               // 一般
}

// 规则类型枚举
export enum RuleType {
  SINGLE = 'single',      // 单因子
  COMBINATION = 'combination' // 组合因子
}

// 告警通知方式枚举
export enum NotifyType {
  SMS = 'SMS',             // 短信
  EMAIL = 'EMAIL',         // 邮件
  WECHAT = 'WECHAT',       // 微信
  SYSTEM = 'SYSTEM'        // 系统内部通知
}

// 告警通知配置
export interface NotifyConfig {
  id: number | string
  level: AlarmLevel        // 告警级别
  notifyTypes: NotifyType[] // 通知方式
  contacts: Contact[]      // 接收人
  enabled: boolean         // 是否启用
  createTime?: string      // 创建时间
  updateTime?: string      // 更新时间
}

// 联系人信息
export interface Contact {
  id: number | string
  name: string            // 联系人姓名
  mobile?: string         // 手机号码
  email?: string          // 电子邮箱
  wechat?: string         // 微信ID
}

// 告警记录
export interface AlarmRecord {
  id: number | string
  ruleId: number | string  // 告警规则ID
  ruleName?: string        // 告警规则名称
  factoryId: number        // 水厂ID
  factoryName?: string     // 水厂名称
  metricId: number         // 指标ID
  metricName?: string      // 指标名称
  metricValue: number      // 指标值
  threshold: number        // 阈值
  operator: string         // 操作符
  level: AlarmLevel        // 告警级别
  alarmTime: string        // 告警时间
  acknowledged: boolean    // 是否已确认
  acknowledgeTime?: string // 确认时间
  acknowledgeUser?: string // 确认用户
  remark?: string          // 备注
}

// 告警统计数据
export interface AlarmStatistics {
  total: number            // 总告警数
  unacknowledged: number   // 未确认告警数
  todayTotal: number       // 今日告警数
  byLevel: {               // 各级别告警数
    [key in AlarmLevel]: number
  }
} 