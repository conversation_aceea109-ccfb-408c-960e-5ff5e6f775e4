.dom-item {
    position: absolute;
    box-sizing: border-box;
    border: 1px solid #aaa;
    background: #1c2834;
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.dom-item-split-btn {
    width: 28px;
    height: 28px;
    background: url(./icon/screen_custom_division_split.svg) no-repeat;
}
.dom-item-split-btn:hover,
.dom-item-merge-btn:hover {
    cursor: pointer;
}

.dom-item-merge-btn {
    position: absolute;
    right: 5px;
    top: 5px;
    width: 24px;
    height: 24px;
    background: url(./icon/screen_custom_division_merge.svg) no-repeat;
}

.dom-item-flex {
    display: flex;
    box-sizing: border-box;
    border: 1px solid #aaa;
    background: #161a1e; float: left;
    justify-content: center;
    align-items: center;
}
.point-e-resize {
    position: absolute;
    z-index: 10;
    cursor: e-resize;
}
.point-n-resize {
    position: absolute;
    z-index: 10;
    cursor: n-resize;
}

.window-division-bottom-container {
    height: 64px;
    background-color: #121a23;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}

.window-division-btn {
    margin-right: 8px;
    float: right;
    width: 60px;
	height: 32px;
	border-radius: 4px;
	border: 0;
}

.window-division-btn-info {
    background-color: #ffffff;
    color: #445160;
}

.window-division-btn-primary {
    background-color: #1c79f4;
    color: #fff;
}

.window-division-btn:hover {
    cursor: pointer;
}