<template>
  <div class="app-container" v-loading="loading" element-loading-text="数据加载中，请稍候...">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-content">
            <div class="header-title">生产消耗数据填报</div>
            <div v-if="hasTempData" class="temp-data-indicator">
              <el-alert title="存在未提交的暂存数据" type="warning" :closable="false" show-icon style="margin-right: 15px;" />
            </div>
            <div class="header-controls">
              <!-- <el-select v-model="selectedFactory" placeholder="请选择查询水厂范围" class="control-item" filterable>
                <template v-for="group in groupedFactoryList">
                  <el-option-group v-if="group.childrenLevel3 && group.childrenLevel3.length" :key="'group-' + group.id"
                    :label="group.name">
                    <el-option v-for="item in group.childrenLevel3" :key="'item-' + item.id" :label="item.name"
                      :value="item.id" class="child-factory-option" />
                  </el-option-group>
                  <el-option v-else-if="group.level === 3" :key="'item-' + group.id" :label="group.name"
                    :value="group.id" />
                </template>
</el-select> -->
              <el-select v-model="selectedIndicators" multiple collapse-tags collapse-tags-tooltip placeholder="指标筛选"
                class="control-item control-item-large" clearable @clear="handleClearIndicators">
                <el-option-group v-for="group in indicatorGroups" :key="group.value" :label="group.label">
                  <template v-for="item in group.options" :key="item.value">
                    <el-option :label="item.label" :value="item.value" class="parent-option">
                      {{ item.label }}
                    </el-option>
                    <el-option v-for="child in item.children" :key="child.value"
                      :label="`${item.label} - ${child.label}`" :value="child.value" class="child-option">
                      <span class="sub-item-label">{{ child.label }}</span>
                    </el-option>
                  </template>
                </el-option-group>
              </el-select>
              <el-date-picker v-model="selectedMonth" type="month" placeholder="选择月份" class="control-item date-picker"
                @change="handleMonthChange" />
              <div class="date-range-group">
                <el-select v-model="startDay" placeholder="起始日" class="day-select" clearable
                  @change="handleDayRangeChange">
                  <el-option v-for="day in availableStartDays" :key="day" :label="`${day}日`" :value="day" />
                </el-select>
                <span class="separator">至</span>
                <el-select v-model="endDay" placeholder="结束日" class="day-select" clearable
                  @change="handleDayRangeChange">
                  <el-option v-for="day in availableEndDays" :key="day" :label="`${day}日`" :value="day" />
                </el-select>
                <el-button type="info" @click="handleTemporarilySave">暂存</el-button>
                <el-button v-hasPermi="['report:prod-quality-data:submit']" type="primary"
                  @click="handleSubmit">提交</el-button>
                <el-button v-hasPermi="['report:prod-quality-data:export']" type="success"
                  @click="handleExport">导出</el-button>
                <SupplementButton report-type="consumption" @click="showAdditionalRecordingDialog" />
              </div>
            </div>
          </div>
        </div>
      </template>

      <div class="table-container">
        <el-table ref="dataTableRef" :data="filteredTableData" border :span-method="handleSpanMethod" size="small">
          <!-- 固定列 -->
          <el-table-column fixed="left" label="项目" prop="group" width="120" align="center" class-name="fixed-column">
            <template #default="scope">
              <template v-if="getSpanMethod(scope.$index, 'group').rowspan">
                {{ scope.row.group }}
              </template>
            </template>
          </el-table-column>

          <el-table-column fixed="left" label="指标名称" prop="name" width="140" align="center" class-name="fixed-column">
            <template #default="scope">
              <template v-if="getSpanMethod(scope.$index, 'name').rowspan">
                {{ scope.row.name }}
              </template>
            </template>
          </el-table-column>

          <el-table-column fixed="left" label="子指标" prop="subName" width="100" align="center"
            class-name="fixed-column" />
          <el-table-column fixed="left" label="单位" prop="unit" width="80" align="center" class-name="fixed-column" />

          <!-- 昨天的数据列 -->
          <el-table-column v-if="isYesterdayColumnVisible" :label="`${yesterdayDay}日`" :prop="`day${yesterdayDay}`"
            width="60" align="center" class-name="data-column">
            <template #header>
              <div style="color: #1890ff; font-weight: bold;">{{ yesterdayDay }}日</div>
            </template>
            <template #default="{ row }">
              <el-input v-model="row[`day${yesterdayDay}`]" size="small" type="text" :step="0.01"
                :disabled="!isCellEditable(yesterdayDay, row)" @input="val => handleValueChange(row, yesterdayDay, val)"
                @blur="e => handleInputBlur(e, row, yesterdayDay)" @wheel.prevent
                :class="{ 'temp-data': row[`day${yesterdayDay}Temp`] }" />
            </template>
          </el-table-column>

          <!-- 其他日期列 -->
          <template v-for="day in filteredDisplayDays" :key="day">
            <el-table-column :label="`${day}日`" :prop="`day${day}`" width="60" align="center" class-name="data-column">
              <template #default="{ row }">
                <el-input v-model="row[`day${day}`]" size="small" type="text" :step="0.01"
                  :disabled="!isCellEditable(day, row)" @input="val => handleValueChange(row, day, val)"
                  @blur="e => handleInputBlur(e, row, day)" @wheel.prevent
                  :class="{ 'temp-data': row[`day${day}Temp`] }" />
              </template>
            </el-table-column>
          </template>


          <el-table-column label="平均值" width="80" align="center" fixed="right">
            <template #default="{ row }">
              <span>{{ calculateAverage(row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最大值" width="80" align="center" fixed="right">
            <template #default="{ row }">
              <span>{{ calculateMax(row) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="最小值" width="80" align="center" fixed="right">
            <template #default="{ row }">
              <span>{{ calculateMin(row) }}</span>
            </template>
          </el-table-column>


          <!-- 累计值列 -->
          <el-table-column label="累计值" width="80" align="center" fixed="right">
            <template #default="{ row }">
              <span>{{ calculateTotal(row) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 补录弹窗 -->
    <el-dialog v-model="additionalRecordingVisible" :title="`数据补录 - ${currentFactoryName}`" width="90%" append-to-body
      destroy-on-close>
      <div class="additional-recording-container" v-loading="additionalLoading" element-loading-text="数据加载中，请稍候...">
        <div class="filter-row">
          <el-date-picker v-model="additionalDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :disabled-date="disabledDate" @change="handleAdditionalDateRangeChange"
            class="date-range-picker" />
          <el-button type="primary" @click="handleAdditionalSubmit">提交</el-button>
          <el-button @click="additionalRecordingVisible = false">取消</el-button>
        </div>

        <el-table ref="additionalTableRef" :data="filteredTableData" border :span-method="handleSpanMethod" size="small"
          style="margin-top: 15px;">
          <!-- 固定列 -->
          <el-table-column fixed="left" label="项目" prop="group" width="120" align="center" class-name="fixed-column">
            <template #default="scope">
              <template v-if="getSpanMethod(scope.$index, 'group').rowspan">
                {{ scope.row.group }}
              </template>
            </template>
          </el-table-column>

          <el-table-column fixed="left" label="指标名称" prop="name" width="140" align="center" class-name="fixed-column">
            <template #default="scope">
              <template v-if="getSpanMethod(scope.$index, 'name').rowspan">
                {{ scope.row.name }}
              </template>
            </template>
          </el-table-column>

          <el-table-column fixed="left" label="子指标" prop="subName" width="100" align="center"
            class-name="fixed-column" />
          <el-table-column fixed="left" label="单位" prop="unit" width="80" align="center" class-name="fixed-column" />

          <!-- 补录日期列 -->
          <el-table-column v-for="day in additionalDisplayDays" :key="day" :label="formatAdditionalDate(day)"
            width="100" align="center">
            <template #default="{ row }">
              <el-input v-model="row[`day${getAdditionalDayKey(day)}`]" size="small" type="text" :step="0.01"
                :disabled="row.subName.includes('单耗') || row.subName.includes('湿泥') || row.subName.includes('绝干污泥产量')"
                @input="val => handleValueChange(row, getAdditionalDayKey(day), val)"
                @blur="e => handleInputBlur(e, row, getAdditionalDayKey(day))" @wheel.prevent
                :class="{ 'temp-data': row[`day${getAdditionalDayKey(day)}Temp`] }" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { FactoryApi } from '@/api/report/factory/index'
import { prodConsumptionDataAPI } from '@/api/report/prodConsumptionData/index'
import * as RoleApi from '@/api/system/role/index'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import Big from 'big.js'
import { ElButton, ElMessage } from 'element-plus'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

const appStore = useAppStore()


// 空值或未填显示的值
const EMPTY_VALUE = '/'

// 添加数字格式化函数
const formatNumber = (value: number | string | undefined): string => {
  if (value === undefined || value === null || value === '') return EMPTY_VALUE
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return EMPTY_VALUE

  // 所有数字都保留两位小数，包括0
  return num.toFixed(2)
}

// 用户信息
const userStore = useUserStore()
const userId = computed(() => userStore.getUser.id)

// 当前选中的月份
const selectedMonth = ref(new Date())

// 日期区间选择
const dateRange = ref<[Date, Date] | null>(null)
const startDay = ref<number>(0)
const endDay = ref<number>(0)

// 可选的结束日期
const availableEndDays = computed<number[]>(() => {
  const days: number[] = []
  const minDay = startDay.value || 1
  const today = new Date()
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()

  // 如果是当前月份，最大可选日期为昨天
  if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
    const maxDay = today.getDate()
    for (let i = minDay; i < maxDay; i++) {
      days.push(i)
    }
  } else {
    // 如果是历史月份，最大可选日期为该月的最大天数
    const maxDay = daysInMonth.value
    for (let i = minDay; i <= maxDay; i++) {
      days.push(i)
    }
  }

  return days
})

// 可选的开始日期
const availableStartDays = computed<number[]>(() => {
  const days: number[] = []
  const maxDay = endDay.value || (selectedMonth.value.getFullYear() === new Date().getFullYear() &&
    selectedMonth.value.getMonth() === new Date().getMonth() ?
    new Date().getDate() : daysInMonth.value)
  for (let i = 1; i <= maxDay; i++) {
    days.push(i)
  }
  return days
})

// 处理日期范围变化
const handleDayRangeChange = () => {
  if (startDay.value && endDay.value) {
    const year = selectedMonth.value.getFullYear()
    const month = selectedMonth.value.getMonth()
    dateRange.value = [
      new Date(year, month, startDay.value),
      new Date(year, month, endDay.value)
    ]
  } else {
    dateRange.value = null
  }
  loadData()
}

// 获取当月天数
const daysInMonth = computed(() => {
  const year = selectedMonth.value.getFullYear()
  const month = selectedMonth.value.getMonth() + 1
  return new Date(year, month, 0).getDate()
})

// 获取昨天的日期
const yesterday = computed(() => {
  const now = new Date()
  // 创建昨天的日期
  const yesterdayDate = new Date(now)
  yesterdayDate.setDate(now.getDate() - 1)
  return yesterdayDate
})

// 获取昨天的日期（天）
const yesterdayDay = computed(() => {
  return yesterday.value.getDate()
})

// 获取昨天所在的月份
const yesterdayMonth = computed(() => {
  return yesterday.value.getMonth()
})

// 获取昨天所在的年份
const yesterdayYear = computed(() => {
  return yesterday.value.getFullYear()
})

// 判断昨天是否在当前选择的月份中
const isYesterdayInSelectedMonth = computed(() => {
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()

  return yesterdayYear.value === selectedYear && yesterdayMonth.value === selectedMonthNum
})

// 判断是否显示昨天的列
const isYesterdayColumnVisible = computed(() => {
  return isYesterdayInSelectedMonth.value
})

// 获取实际显示的天数（当月只显示到昨天，历史月份显示整月）
const displayDays = computed<number[]>(() => {
  const now = new Date()
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth()

  // 如果有日期区间选择，则只显示区间内的日期
  if (startDay.value && endDay.value) {
    const start = Math.min(startDay.value, endDay.value)
    const end = Math.max(startDay.value, endDay.value)
    const days = Array.from({ length: end - start + 1 }, (_, i) => start + i)

    // 如果包含昨天，将昨天移到最前面（仅当昨天在当前选择的月份中）
    if (isYesterdayInSelectedMonth.value) {
      const yesterdayIndex = days.indexOf(yesterdayDay.value)
      if (yesterdayIndex !== -1) {
        days.splice(yesterdayIndex, 1)
        days.unshift(yesterdayDay.value)
      }
    }

    return days
  }

  // 如果是当月，只显示到昨天
  if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
    // 获取到昨天为止的所有天数
    const days = Array.from({ length: now.getDate() - 1 }, (_, i) => i + 1)

    // 将昨天移到最前面
    const yesterdayIndex = days.indexOf(yesterdayDay.value)
    if (yesterdayIndex !== -1) {
      days.splice(yesterdayIndex, 1)
      days.unshift(yesterdayDay.value)
    }

    return days
  }

  // 如果是历史月份，显示整月
  return Array.from({ length: daysInMonth.value }, (_, i) => i + 1)
})

// 过滤掉昨天的日期列
const filteredDisplayDays = computed(() => {
  return displayDays.value.filter(day => {
    // 如果昨天在当前选择的月份中，过滤掉昨天（因为昨天会单独显示在最前面）
    if (isYesterdayInSelectedMonth.value && day === yesterdayDay.value) {
      return false
    }

    // 过滤掉今天和之后的日期
    const now = new Date()
    const selectedYear = selectedMonth.value.getFullYear()
    const selectedMonthNum = selectedMonth.value.getMonth()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth()
    const currentDay = now.getDate()

    if (selectedYear === currentYear && selectedMonthNum === currentMonth && day >= currentDay) {
      return false
    }

    return true
  })
})

// 添加统计数据存储
const statData = ref<any>(null)

// 加载统计数据
const loadStatData = async () => {
  if (!selectedFactory.value) {
    return
  }

  try {
    // 获取当前选择月份的起始日期和结束日期
    const year = selectedMonth.value.getFullYear()
    const month = selectedMonth.value.getMonth()

    // 月份的第一天
    const startDate = new Date(year, month, 1)
    // 月份的最后一天
    const endDate = new Date(year, month + 1, 0)

    // 如果有选择日期范围，则使用选择的范围
    const start = startDay.value ? new Date(year, month, startDay.value) : startDate
    const end = endDay.value ? new Date(year, month, endDay.value) : endDate

    // 格式化日期为 yyyy-MM-dd
    const formatStartDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`
    const formatEndDate = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`

    const params = {
      factoryId: selectedFactory.value,
      startDate: formatStartDate,
      endDate: formatEndDate
    }

    try {
      const result = await prodConsumptionDataAPI.getConsumptionStat(params)

      // 处理不同的返回格式
      let data = null
      if (Array.isArray(result) && result.length > 0) {
        // 直接返回数组的情况
        data = result[0]
      } else if (result && result.code === 0 && result.data && result.data.length > 0) {
        // 标准响应格式的情况
        data = result.data[0]
      } else if (result && typeof result === 'object' && !Array.isArray(result)) {
        // 直接返回对象的情况
        data = result
      }

      if (data) {
        statData.value = data
      } else {
        statData.value = null
      }
    } catch (apiError) {
      console.error('调用消耗统计数据接口失败:', apiError)
      statData.value = null
    }
  } catch (error) {
    console.error('获取消耗统计数据失败:', error)
    statData.value = null
  }
}

// 监听日期范围变化时，重新加载统计数据
watch([startDay, endDay], async () => {
  if (startDay.value && endDay.value) {
    await loadStatData()
  }
})

// 修改计算平均值的方法
const calculateAverage = (row: any) => {
  // 直接使用后端计算的结果
  if (statData.value && row.field) {
    const fieldName = row.field

    if (fieldName && statData.value[fieldName]) {
      if (statData.value[fieldName].avg !== undefined && statData.value[fieldName].avg !== null) {
        return formatNumber(statData.value[fieldName].avg)
      }
    }
  }

  return EMPTY_VALUE
}


// 修改计算累计值的方法
const calculateTotal = (row: any) => {
  // 单耗类的不计算累计值
  const specifiedSubNames = ['单耗', '电单耗', '湿泥'];
  if (specifiedSubNames.includes(row.subName)) {
    return EMPTY_VALUE;
  }

  // 直接使用后端计算的结果
  if (statData.value && row.field) {
    const fieldName = row.field

    if (fieldName && statData.value[fieldName] && statData.value[fieldName].total !== undefined && statData.value[fieldName].total !== null) {
      return formatNumber(statData.value[fieldName].total)
    }
  }

  return EMPTY_VALUE
}

// 添加计算最大值的方法
const calculateMax = (row: any) => {
  // 直接使用后端计算的结果
  if (statData.value && row.field) {
    const fieldName = row.field

    if (fieldName && statData.value[fieldName]) {
      if (statData.value[fieldName].max !== undefined && statData.value[fieldName].max !== null) {
        return formatNumber(statData.value[fieldName].max)
      }
    }
  }

  return EMPTY_VALUE
}

// 添加计算最小值的方法
const calculateMin = (row: any) => {
  // 直接使用后端计算的结果
  if (statData.value && row.field) {
    const fieldName = row.field

    if (fieldName && statData.value[fieldName]) {
      if (statData.value[fieldName].min !== undefined && statData.value[fieldName].min !== null) {
        return formatNumber(statData.value[fieldName].min)
      }
    }
  }

  return EMPTY_VALUE
}

// 格式化日期
const formatDate = (date: Date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
}

// 表格数据结构
const tableData = ref([
  // 产量
  { group: '产量', name: '水量', subName: '进水水量', unit: 'm³', field: 'inFlowVol', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '产量', name: '水量', subName: '出水水量', unit: 'm³', field: 'treatVol', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },

  // 能耗
  { group: '能耗', name: '电量', subName: '电量', unit: 'KWh', field: 'elecCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '能耗', name: '电量', subName: '电单耗', unit: 'KWh/Km³', field: 'elecSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },

  // 原材料消耗
  { group: '原材料消耗', name: '碳源', subName: '用量', unit: 'Kg', field: 'carbonUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '碳源', subName: '单耗', unit: 'Kg/Km³', field: 'carbonSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '次氯酸钠', subName: '用量', unit: 'Kg', field: 'sodiumHypoUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '次氯酸钠', subName: '单耗', unit: 'Kg/Km³', field: 'sodiumHypoSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '聚合氯化铝(PAC)', subName: '用量', unit: 'Kg', field: 'pacUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '聚合氯化铝(PAC)', subName: '单耗', unit: 'Kg/Km³', field: 'pacSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '聚合硫酸铁', subName: '用量', unit: 'Kg', field: 'ferricSulfUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '聚合硫酸铁', subName: '单耗', unit: 'Kg/Km³', field: 'ferricSulfSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '氢氧化钠', subName: '用量', unit: 'Kg', field: 'naohUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '氢氧化钠', subName: '单耗', unit: 'Kg/Km³', field: 'naohSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阴离子)', subName: '用量', unit: 'Kg', field: 'anPamUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阴离子)', subName: '单耗', unit: 'Kg/Km³', field: 'anPamSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阳离子)', subName: '用量', unit: 'Kg', field: 'catPamUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '原材料消耗', name: '固体聚丙烯酰胺(阳离子)', subName: '单耗', unit: 'Kg/Km³', field: 'catPamSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },

  // 污泥
  { group: '污泥', name: '污泥产量', subName: '60%污泥产量', unit: '吨(t)', field: 'sludge60Prod', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '污泥产量', subName: '80%污泥产量', unit: '吨(t)', field: 'sludge80Prod', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '污泥产量', subName: '绝干污泥产量', unit: '吨(t)', field: 'drySludgeProd', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '产泥率', subName: '湿泥', unit: 't/万m³', field: 'sludgeRate', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '固体聚丙烯酰胺(阳离子)', subName: '用量', unit: 'Kg', field: 'catPamSludgeUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '固体聚丙烯酰胺(阳离子)', subName: '单耗', unit: 'Kg/Km³', field: 'catPamSludgeSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '聚合氯化铝(PAC)', subName: '用量', unit: 'Kg', field: 'pacSludgeUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '聚合氯化铝(PAC)', subName: '单耗', unit: 'Kg/Km³', field: 'pacSludgeSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '液体铁盐(kg)', subName: '用量', unit: 'Kg', field: 'liqIronSaltUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '液体铁盐(kg)', subName: '单耗', unit: 'Kg/Km³', field: 'liqIronSaltSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '石灰', subName: '用量', unit: 'Kg', field: 'limeUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
  { group: '污泥', name: '石灰', subName: '单耗', unit: 'Kg/Km³', field: 'limeSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) }
])

// 在 script setup 中添加类型定义
interface IndicatorOption {
  label: string
  value: string
  options?: IndicatorOption[]
}

interface IndicatorGroup {
  label: string
  value: string
  options: IndicatorOption[]
}

// 选中的指标
const selectedIndicators = ref<string[]>([])

// 指标分组数据
const indicatorGroups = computed<IndicatorGroup[]>(() => {
  // 使用嵌套Map来保持强绑定关系
  const groups = new Map<string, Map<string, string[]>>()

  // 首先构建完整的层级结构
  tableData.value.forEach(row => {
    if (!groups.has(row.group)) {
      groups.set(row.group, new Map())
    }
    const groupMap = groups.get(row.group)!
    if (!groupMap.has(row.name)) {
      groupMap.set(row.name, [])
    }
    if (!groupMap.get(row.name)!.includes(row.subName)) {
      groupMap.get(row.name)!.push(row.subName)
    }
  })

  // 转换为选择器需要的结构
  return Array.from(groups.entries()).map(([groupLabel, nameMap]) => ({
    label: groupLabel,
    value: groupLabel,
    options: Array.from(nameMap.entries()).map(([name, subNames]) => ({
      label: name,
      value: `${groupLabel}/${name}`,
      children: subNames.map(subName => ({
        label: subName,
        value: `${groupLabel}/${name}/${subName}`
      }))
    }))
  }))
})

// 处理清空选择
const handleClearIndicators = () => {
  selectedIndicators.value = []
}

// 过滤后的表格数据
const filteredTableData = computed(() => {
  if (!selectedIndicators.value.length) {
    return tableData.value
  }

  // 解析选中的指标
  const selectedGroups = new Set<string>()
  const selectedNames = new Set<string>()
  const selectedFullPaths = new Set<string>()

  selectedIndicators.value.forEach(path => {
    const [group, name, subName] = path.split('/')
    if (group && !name && !subName) {
      selectedGroups.add(group)
    } else if (group && name && !subName) {
      selectedNames.add(`${group}/${name}`)
    } else if (group && name && subName) {
      selectedFullPaths.add(path)
    }
  })

  return tableData.value.filter(row => {
    const groupPath = row.group
    const namePath = `${row.group}/${row.name}`
    const fullPath = `${row.group}/${row.name}/${row.subName}`

    return selectedGroups.has(row.group) ||
      selectedNames.has(namePath) ||
      selectedFullPaths.has(fullPath)
  })
})

// 处理月份变化
const handleMonthChange = () => {
  const today = new Date()
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()

  // 设置起始日为1日
  startDay.value = 1

  // 如果是当前月份，结束日默认为昨天
  if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
    endDay.value = today.getDate() - 1
  } else {
    // 如果是历史月份，结束日默认为该月的最后一天
    endDay.value = daysInMonth.value
  }

  // 设置日期范围
  dateRange.value = [
    new Date(selectedYear, selectedMonthNum, startDay.value),
    new Date(selectedYear, selectedMonthNum, endDay.value)
  ]

  loadData()
  nextTick(() => {
    refreshTableScroll()
  })
}

// 添加接口类型定义
interface ApiResponse<T> {
  code: number;
  data: T;
  msg?: string;
}

interface DailyData {
  date: string;
  factoryId?: number;
  inFlowVol?: string;
  treatVol?: string;
  elecCons?: string;
  elecSingleCons?: string;
  carbonUsage?: string;
  carbonSingleCons?: string;
  sodiumHypoUsage?: string;
  sodiumHypoSingleCons?: string;
  pacUsage?: string;
  pacSingleCons?: string;
  ferricSulfUsage?: string;
  ferricSulfSingleCons?: string;
  naohUsage?: string;
  naohSingleCons?: string;
  anPamUsage?: string;
  anPamSingleCons?: string;
  catPamUsage?: string;
  catPamSingleCons?: string;
  sludge60Prod?: string;
  sludge80Prod?: string;
  drySludgeProd?: string;
  sludgeRate?: string;
  catPamSludgeUsage?: string;
  catPamSludgeSingleCons?: string;
  pacSludgeUsage?: string;
  pacSludgeSingleCons?: string;
  liqIronSaltUsage?: string;
  liqIronSaltSingleCons?: string;
  limeUsage?: string;
  limeSingleCons?: string;
  reporterId?: number;
  reviewStatus?: number;
}

// 获取数据库字段名
const getFieldName = (indicatorName: string, group: string): string => {
  const fieldMap: Record<string, string> = {
    // 产量
    '进水水量': 'inFlowVol',
    '出水水量': 'treatVol',

    // 能耗
    '电量': 'elecCons',
    '电单耗': 'elecSingleCons',

    // 原材料消耗
    '碳源用量': 'carbonUsage',
    '碳源单耗': 'carbonSingleCons',
    '次氯酸钠用量': 'sodiumHypoUsage',
    '次氯酸钠单耗': 'sodiumHypoSingleCons',
    'PAC用量': 'pacUsage',
    'PAC单耗': 'pacSingleCons',
    '聚合硫酸铁用量': 'ferricSulfUsage',
    '聚合硫酸铁单耗': 'ferricSulfSingleCons',
    '氢氧化钠用量': 'naohUsage',
    '氢氧化钠单耗': 'naohSingleCons',
    '阴离子PAM用量': 'anPamUsage',
    '阴离子PAM单耗': 'anPamSingleCons',
    '阳离子PAM用量': 'catPamUsage',
    '阳离子PAM单耗': 'catPamSingleCons',

    // 污泥
    '60%污泥产量': 'sludge60Prod',
    '80%污泥产量': 'sludge80Prod',
    '绝干污泥产量': 'drySludgeProd',
    '产泥率': 'sludgeRate',
    '污泥处理中阳离子PAM用量': 'catPamSludgeUsage',
    '污泥处理中阳离子PAM单耗': 'catPamSludgeSingleCons',
    '污泥处理中PAC用量': 'pacSludgeUsage',
    '污泥处理中PAC单耗': 'pacSludgeSingleCons',
    '液体铁盐用量': 'liqIronSaltUsage',
    '液体铁盐单耗': 'liqIronSaltSingleCons',
    '石灰用量': 'limeUsage',
    '石灰单耗': 'limeSingleCons'
  }

  return fieldMap[indicatorName] || ''
}

// 水厂相关类型定义
interface Factory {
  id: number;
  name: string;
  type: string;
  region: string;
  isActive: boolean;
}

// 水厂列表和选中的水厂
const factoryList = ref<Factory[]>([])

// 添加加载状态标记
const loading = ref(false)

// 添加修改标记
const hasChanges = ref(false)

// 添加修改记录
const modifiedData = ref<Record<string, any>>({})

// 添加报表管理员角色状态
const isReportAdminRole = ref(false)

// 存储水厂ID与水厂对象的映射
const factoryMap = ref(new Map())



// 递归构建水厂ID与水厂对象的映射
const buildFactoryMap = (factories) => {
  if (!factories || !factories.length) return;

  factories.forEach(factory => {
    // 将当前水厂添加到映射中
    factoryMap.value.set(factory.id, factory);

    // 处理子节点 childrenLevel3
    if (factory.childrenLevel3 && factory.childrenLevel3.length) {
      buildFactoryMap(factory.childrenLevel3);
    }

    // 处理子节点 children
    if (factory.children && factory.children.length) {
      buildFactoryMap(factory.children);
    }
  });
}

// 清空表格数据
const clearTableData = () => {
  // 重新初始化表格数据
  tableData.value = [
    // 产量
    { group: '产量', name: '水量', subName: '进水水量', unit: 'm³', field: 'inFlowVol', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '产量', name: '水量', subName: '出水水量', unit: 'm³', field: 'treatVol', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },

    // 能耗
    { group: '能耗', name: '电量', subName: '电量', unit: 'KWh', field: 'elecCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '能耗', name: '电量', subName: '电单耗', unit: 'KWh/Km³', field: 'elecSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },

    // 原材料消耗
    { group: '原材料消耗', name: '碳源', subName: '用量', unit: 'Kg', field: 'carbonUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '碳源', subName: '单耗', unit: 'Kg/Km³', field: 'carbonSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '次氯酸钠', subName: '用量', unit: 'Kg', field: 'sodiumHypoUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '次氯酸钠', subName: '单耗', unit: 'Kg/Km³', field: 'sodiumHypoSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '聚合氯化铝(PAC)', subName: '用量', unit: 'Kg', field: 'pacUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '聚合氯化铝(PAC)', subName: '单耗', unit: 'Kg/Km³', field: 'pacSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '聚合硫酸铁', subName: '用量', unit: 'Kg', field: 'ferricSulfUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '聚合硫酸铁', subName: '单耗', unit: 'Kg/Km³', field: 'ferricSulfSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '氢氧化钠', subName: '用量', unit: 'Kg', field: 'naohUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '氢氧化钠', subName: '单耗', unit: 'Kg/Km³', field: 'naohSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '固体聚丙烯酰胺(阴离子)', subName: '用量', unit: 'Kg', field: 'anPamUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '固体聚丙烯酰胺(阴离子)', subName: '单耗', unit: 'Kg/Km³', field: 'anPamSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '固体聚丙烯酰胺(阳离子)', subName: '用量', unit: 'Kg', field: 'catPamUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '原材料消耗', name: '固体聚丙烯酰胺(阳离子)', subName: '单耗', unit: 'Kg/Km³', field: 'catPamSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },

    // 污泥
    { group: '污泥', name: '污泥产量', subName: '60%污泥产量', unit: '吨(t)', field: 'sludge60Prod', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '污泥产量', subName: '80%污泥产量', unit: '吨(t)', field: 'sludge80Prod', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '污泥产量', subName: '绝干污泥产量', unit: '吨(t)', field: 'drySludgeProd', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '产泥率', subName: '湿泥', unit: 't/万m³', field: 'sludgeRate', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '固体聚丙烯酰胺(阳离子)', subName: '用量', unit: 'Kg', field: 'catPamSludgeUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '固体聚丙烯酰胺(阳离子)', subName: '单耗', unit: 'Kg/Km³', field: 'catPamSludgeSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '聚合氯化铝(PAC)', subName: '用量', unit: 'Kg', field: 'pacSludgeUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '聚合氯化铝(PAC)', subName: '单耗', unit: 'Kg/Km³', field: 'pacSludgeSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '液体铁盐(kg)', subName: '用量', unit: 'Kg', field: 'liqIronSaltUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '液体铁盐(kg)', subName: '单耗', unit: 'Kg/Km³', field: 'liqIronSaltSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '石灰', subName: '用量', unit: 'Kg', field: 'limeUsage', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) },
    { group: '污泥', name: '石灰', subName: '单耗', unit: 'Kg/Km³', field: 'limeSingleCons', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' })) }
  ]
}



/**
 * 检查当前用户是否拥有报表管理员的角色
 */
const checkIsHasReportAdminRole = async () => {
  try {
    const data = await RoleApi.checkIsHasRole({ roleCode: "report_admin" });
    isReportAdminRole.value = !!data;
  } catch (error) {
    isReportAdminRole.value = false;
  }
}

// 表格引用
const dataTableRef = ref<InstanceType<typeof import('element-plus').ElTable> | null>(null)

// 修改刷新表格布局的方法
const refreshTableScroll = () => {
  // 使用ref引用直接访问表格实例的方法
  if (dataTableRef.value) {
    // 调用表格的 doLayout 方法重新布局
    dataTableRef.value.doLayout()
  }

  // 获取表格容器元素
  const tableWrapper = document.querySelector('.el-table__body-wrapper') as HTMLElement
  if (tableWrapper) {
    // 确保横向滚动条可见
    tableWrapper.style.overflowX = 'auto'
    tableWrapper.style.scrollBehavior = 'smooth'

    // 确保表头和内容的滚动同步
    const headerWrapper = document.querySelector('.el-table__header-wrapper') as HTMLElement
    if (headerWrapper) {
      // 同步滚动事件
      tableWrapper.onscroll = () => {
        headerWrapper.scrollLeft = tableWrapper.scrollLeft
      }
    }

    // 强制更新固定列的高度
    const fixedLeftColumns = document.querySelector('.el-table__fixed-left') as HTMLElement
    const fixedRightColumns = document.querySelector('.el-table__fixed-right') as HTMLElement

    if (fixedLeftColumns) {
      fixedLeftColumns.style.height = '100%'
    }

    if (fixedRightColumns) {
      fixedRightColumns.style.height = '100%'
    }

    // 修复表格线条
    setTimeout(() => {
      const rows = document.querySelectorAll('.el-table__row')
      const cells = document.querySelectorAll('.el-table__cell')

      cells.forEach(cell => {
        (cell as HTMLElement).style.borderBottom = '1px solid var(--el-table-border-color)'
      })

      rows.forEach(row => {
        const cells = row.querySelectorAll('.el-table__cell')
        cells.forEach(cell => {
          (cell as HTMLElement).style.borderBottom = '1px solid var(--el-table-border-color)'
        })
      })
    }, 100)
  }
}

// 修改加载数据方法
const loadData = async () => {
  if (!loading.value) {
    loading.value = true
  }

  try {
    // 清空现有数据
    clearTableData()

    // 根据月份 厂站ID 查询历史数据
    const searchParam = {
      factoryId: selectedFactory.value,
      month: formatDate(selectedMonth.value),
    }

    // 调用接口获取历史数据
    try {
      const historyData = await prodConsumptionDataAPI.queryDataByFactoryIdAndDate(searchParam)
      if (historyData.code == 0) {
        // 处理历史数据，更新表格
        updateTableData(historyData.data)

        // 获取统计数据
        await loadStatData()

        // 等待DOM更新，然后刷新表格宽度和滚动条
        nextTick(() => {
          refreshTableScroll()
          // 额外的延迟刷新，确保数据完全渲染
          setTimeout(refreshTableScroll, 300)
        })
      }
    } catch (error) {
      console.error('获取历史数据失败:', error)
      ElMessage.error('获取历史数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 监听月份和日期范围变化时额外刷新表格
watch([selectedMonth, startDay, endDay], () => {
  nextTick(() => {
    refreshTableScroll()
    // 额外延迟刷新确保在数据变化后正确布局
    setTimeout(refreshTableScroll, 300)
  })
})

const selectedFactory = computed(() => appStore.getCurrentStation?.id)

// 监听水厂变化
watch(selectedFactory, (newValue) => {
  if (newValue) {
    hasChanges.value = false
    modifiedData.value = {}
    loadData()
  }
})

// 监听月份变化
watch(selectedMonth, () => {
  hasChanges.value = false
  modifiedData.value = {}
  loadData()
})

// 处理输入框失焦事件，格式化数字
const handleInputBlur = (e: FocusEvent, row: any, day: number) => {
  const input = e.target as HTMLInputElement
  const value = input.value

  // 如果输入为空，直接返回
  if (!value) return

  // 如果输入为"/"，直接保留
  if (value === EMPTY_VALUE) {

    return
  }

  // 尝试转换为数字
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    // 如果转换失败，清空输入
    row[`day${day}`] = ''
  }
}

// 修改 handleValueChange 函数
const handleValueChange = (row: any, day: number, value: string) => {

  // 调试日志
  // console.log('handleValueChange', row, day, value)
  // 如果输入为空，直接返回
  if (!value) {
    row[`day${day}`] = ''
    return
  }

  // 如果输入为"/"，直接保留
  if (value === EMPTY_VALUE) {
    // 标记有修改
    hasChanges.value = true

    // 记录修改的日期
    const key = `day${day}`
    if (!modifiedData.value[key]) {
      modifiedData.value[key] = {
        day,
        data: []
      }
    }
    return
  }

  // 验证输入是否为有效数字
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    // 如果输入无效，清空输入
    row[`day${day}`] = ''
    return
  }

  // 标记有修改
  hasChanges.value = true

  // 记录修改的日期
  const key = `day${day}`
  if (!modifiedData.value[key]) {
    modifiedData.value[key] = {
      day,
      data: []
    }
  }
}

// 提交数据的函数
const handleSubmit = async () => {
  if (!hasChanges.value && !hasTempData.value) {
    ElMessage.warning('没有数据修改或暂存数据，无需提交')
    return
  }

  if (!selectedFactory.value) {
    ElMessage.warning('请先选择水厂,再进行数据提交')
    return
  }

  try {
    // 将修改的数据转换为按日期组织的数据结构
    const formattedData: Record<string, DailyData> = {}

    // 收集所有需要提交的日期（包括修改的和暂存状态的）
    const daysToSubmit = new Set<number>()

    // 添加当前修改的日期
    Object.values(modifiedData.value).forEach((item: any) => {
      daysToSubmit.add(item.day)
    })

    // 添加所有暂存状态的日期
    for (let day = 1; day <= 31; day++) {
      tableData.value.some(row => {
        if (row[`day${day}Temp`]) {
          daysToSubmit.add(day)
          return true
        }
        return false
      })
    }

    // 处理所有需要提交的日期
    daysToSubmit.forEach(day => {
      const date = `${selectedMonth.value.getFullYear()}-${String(selectedMonth.value.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`

      // 初始化该日期的数据对象
      formattedData[date] = {
        date,
        factoryId: selectedFactory.value,
        reporterId: userId.value,
        reviewStatus: 0  // 设置为正常提交状态
      }

      // 获取该日期的所有指标数据
      tableData.value.forEach(row => {
        if (row.field) {
          const cellValue = row[`day${day}`];
          // 如果提交的数据为/ 则改为 null
          const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;
          (formattedData[date] as any)[row.field] = submitValue;
        }
      })
    })

    // 准备提交的数据结构
    const submitData = Object.values(formattedData)

    // 调用提交接口
    const res = await prodConsumptionDataAPI.saveOrUpdate(submitData)
    if (res.code === 0) {
      loadData()
      // 提交成功后重新获取统计数据
      await loadStatData()
      ElMessage.success('提交成功')
      // 提交成功后重置修改标记和修改记录
      hasChanges.value = false
      modifiedData.value = {}
    } else {
      ElMessage.error(res.msg || '提交失败')
    }
  } catch (error) {
    console.error('数据处理失败:', error)
  }
}

// 添加处理合并单元格的方法
const handleSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    return getSpanMethod(rowIndex, 'group')
  } else if (columnIndex === 1) {
    return getSpanMethod(rowIndex, 'name')
  }
  return {
    rowspan: 1,
    colspan: 1
  }
}

// 修改合并计算方法
const getSpanMethod = (index: number, prop: string) => {
  const row = filteredTableData.value[index]
  let rowspan = 1
  let colspan = 1

  // 只有在过滤后的数据中查找相同值
  for (let i = index + 1; i < filteredTableData.value.length; i++) {
    const nextRow = filteredTableData.value[i]
    if (prop === 'group' && nextRow.group === row.group) {
      rowspan++
    } else if (prop === 'name' && nextRow.group === row.group && nextRow.name === row.name) {
      rowspan++
    } else {
      break
    }
  }

  // 检查是否是第一个出现的值
  if (index === 0) {
    return { rowspan, colspan }
  }

  const prevRow = filteredTableData.value[index - 1]
  if (prop === 'group' && prevRow.group === row.group) {
    return { rowspan: 0, colspan: 0 }
  }
  if (prop === 'name' && prevRow.group === row.group && prevRow.name === row.name) {
    return { rowspan: 0, colspan: 0 }
  }

  return { rowspan, colspan }
}

// 添加更新表格数据的函数
const updateTableData = (historyData: any[]) => {
  // 清空现有数据
  tableData.value = tableData.value.map(row => ({
    ...row,
    ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '' }))
  }))

  // 重置暂存状态
  hasTempData.value = false

  // 更新历史数据
  historyData.forEach(item => {
    const date = new Date(item.date)
    const day = date.getDate()

    // 检查是否为暂存状态 (reviewStatus = 5)
    if (item.reviewStatus === 5) {
      hasTempData.value = true
    }

    // 遍历表格数据，根据field字段匹配更新
    tableData.value.forEach(row => {
      if (row.field && item[row.field] !== undefined) {
        let value;
        if (row.field === 'treatVol' || row.field === 'inFlowVol') {
          // 如果为进水水量或者出水水量，填写值为m3 数据库存的为Km3 所以需要乘以1k
          // Biground
          value = new Big(item[row.field]).times(1000).round(4).toString();
        } else {
          value = item[row.field];
        }

        // 检查单元格是否可编辑
        if (isCellEditable(day, row)) {
          // 如果可编辑，且值为null，则替换为"/"
          row[`day${day}`] = value === null ? EMPTY_VALUE : value.toString();

          // 如果是暂存状态的数据，给单元格添加标识(可以通过添加自定义属性来实现)
          if (item.reviewStatus === 5) {
            row[`day${day}Temp`] = true;
          }
        } else {
          // 如果不可编辑，保留原始值
          row[`day${day}`] = value === null ? EMPTY_VALUE : value.toString();
        }
      }
    })
  })
}

// 导出Excel
const handleExport = async () => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('生产消耗数据')

  // 设置列
  const columns = [
    { header: '项目', key: 'group', width: 20 },
    { header: '指标名称', key: 'name', width: 20 },
    { header: '子指标', key: 'subName', width: 20 },
    { header: '单位', key: 'unit', width: 15 },
    ...Array.from({ length: endDay.value - startDay.value + 1 }, (_, i) => ({
      header: `${startDay.value + i}号`,
      key: `day${startDay.value + i}`,
      width: 15
    })),
    { header: '平均值', key: 'avg', width: 15 }, // 添加平均值列
    { header: '最大值', key: 'max', width: 15 }, // 添加平均值列
    { header: '最小值', key: 'min', width: 15 }, // 添加平均值列
    { header: '累计值', key: 'total', width: 15 } // 添加平均值列
  ]

  worksheet.columns = columns as Partial<ExcelJS.Column>[]

  // 添加数据行（包括表头）
  const rows = [
    ['项目', '指标名称', '子指标', '单位',
      ...Array.from({ length: endDay.value - startDay.value + 1 }, (_, i) => `${startDay.value + i}号`),
      '平均值',
      '最大值',
      '最小值',
      '累计值'
    ]
  ]

  // 添加数据
  const processedData = []
  let lastGroup = ''
  let lastName = ''

  filteredTableData.value.forEach(row => {
    const rowData = [
      lastGroup !== row.group ? row.group : '',
      row.name,
      row.subName,
      row.unit,
      ...Array.from({ length: endDay.value - startDay.value + 1 }, (_, i) => {
        const dayIndex = startDay.value + i
        const value = row[`day${dayIndex}`]
        // 检查单元格是否可编辑
        if (isCellEditable(dayIndex, row)) {
          // 如果可编辑，且值为0或空字符串，替换为"/"
          return value == '0' ? EMPTY_VALUE : (value !== undefined ? value : '');
        } else {
          // 如果不可编辑，保留原始值
          return value !== undefined && value !== '' ? value : '';
        }
      }),
      calculateAverage(row),
      calculateMax(row),
      calculateMin(row),
      calculateTotal(row) // 添加累计值
    ]
    processedData.push(rowData)
    lastGroup = row.group
    lastName = row.name
  })

  // 写入所有数据
  worksheet.addRows(processedData)

  // 设置表头样式
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true, size: 12 }
  headerRow.height = 30

  // 合并表头中的指标名称和子指标列
  worksheet.mergeCells('B1:C1')
  const mergedHeaderCell = worksheet.getCell('B1')
  mergedHeaderCell.value = '指标名称'
  mergedHeaderCell.alignment = { vertical: 'middle', horizontal: 'center' }

  // 设置所有单元格的样式
  const totalColumns = 8 + (endDay.value - startDay.value + 1) // 固定列（项目、指标名称、子指标、单位、平均值 最小值 最大值 累计值）+ 日期列
  const lastRowNum = processedData.length + 1 // +1 for header row

  // 遍历所有单元格设置样式
  for (let rowNum = 1; rowNum <= lastRowNum; rowNum++) {
    const row = worksheet.getRow(rowNum)
    row.height = 30

    for (let colNum = 1; colNum <= totalColumns; colNum++) {
      const cell = row.getCell(colNum)
      // 设置所有单元格的对齐方式和字体
      cell.alignment = { vertical: 'middle', horizontal: 'center' }
      cell.font = rowNum === 1 ? { bold: true, size: 12 } : { size: 12 }
      // 设置边框
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    }
  }

  // 处理合并单元格
  let currentGroup = ''
  let groupStartRow = 2
  let currentName = ''
  let nameStartRow = 2
  let currentRow = 2

  filteredTableData.value.forEach((row, index) => {
    // 处理组（项目）列的合并
    if (row.group !== currentGroup) {
      if (currentGroup !== '' && groupStartRow < currentRow) {
        // 合并前一组的单元格
        worksheet.mergeCells(`A${groupStartRow}:A${currentRow - 1}`)
        const mergedCell = worksheet.getCell(`A${groupStartRow}`)
        mergedCell.alignment = { vertical: 'middle', horizontal: 'center' }
      }
      currentGroup = row.group
      groupStartRow = currentRow
    }

    // 处理指标名称列的合并
    if (row.name !== currentName) {
      if (currentName !== '' && nameStartRow < currentRow) {
        // 合并前一指标名称的单元格
        worksheet.mergeCells(`B${nameStartRow}:B${currentRow - 1}`)
        const mergedCell = worksheet.getCell(`B${nameStartRow}`)
        mergedCell.alignment = { vertical: 'middle', horizontal: 'center' }
      }
      currentName = row.name
      nameStartRow = currentRow
    }
    currentRow++
  })

  // 处理最后一组的合并
  if (groupStartRow < currentRow) {
    worksheet.mergeCells(`A${groupStartRow}:A${currentRow - 1}`)
    const mergedCell = worksheet.getCell(`A${groupStartRow}`)
    mergedCell.alignment = { vertical: 'middle', horizontal: 'center' }
  }

  // 处理最后一个指标名称的合并
  if (nameStartRow < currentRow) {
    worksheet.mergeCells(`B${nameStartRow}:B${currentRow - 1}`)
    const mergedCell = worksheet.getCell(`B${nameStartRow}`)
    mergedCell.alignment = { vertical: 'middle', horizontal: 'center' }
  }

  // 生成并下载文件
  const buffer = await workbook.xlsx.writeBuffer()
  const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const fileExtension = '.xlsx'
  const fileName = `生产消耗数据_${formatDate(selectedMonth.value)}_${startDay.value}日至${endDay.value}日${fileExtension}`

  const blob = new Blob([buffer], { type: fileType })
  saveAs(blob, fileName)
}

// 修改onMounted为异步函数
onMounted(async () => {
  try {
    loading.value = true


    // 检查是否有报表管理员权限
    await checkIsHasReportAdminRole()

    // 设置默认的日期值
    const today = new Date()

    // 如果今天是月初1号，自动切换到上个月
    if (today.getDate() === 1) {
      // 设置为上个月
      selectedMonth.value = new Date(today.getFullYear(), today.getMonth() - 1, 1)

      // 设置起始日为1日
      startDay.value = 1
      // 设置结束日为上个月的最后一天
      endDay.value = new Date(today.getFullYear(), today.getMonth(), 0).getDate()

      // 设置日期范围
      dateRange.value = [
        new Date(selectedMonth.value.getFullYear(), selectedMonth.value.getMonth(), startDay.value),
        new Date(selectedMonth.value.getFullYear(), selectedMonth.value.getMonth(), endDay.value)
      ]

      // 加载数据
      await loadData()
    } else {
      const currentYear = today.getFullYear()
      const currentMonth = today.getMonth()

      // 设置起始日为1日
      startDay.value = 1
      // 设置结束日为当天减一天
      endDay.value = today.getDate() - 1

      // 设置日期范围
      dateRange.value = [
        new Date(currentYear, currentMonth, startDay.value),
        new Date(currentYear, currentMonth, endDay.value)
      ]
      // 加载数据
      await loadData()
    }

    // 添加窗口大小变化监听器，确保表格布局响应式调整
    window.addEventListener('resize', refreshTableScroll)

    // 添加缩放监听器
    window.addEventListener('zoom', refreshTableScroll)

    // 等待表格渲染完成后刷新表格布局
    nextTick(() => {
      refreshTableScroll()

      // 额外的延迟刷新，确保在组件完全渲染后重新布局
      setTimeout(refreshTableScroll, 500)
    })
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    loading.value = false
  }
})

// 页面卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', refreshTableScroll)
  window.removeEventListener('zoom', refreshTableScroll)

  // 清除滚动事件监听
  const tableWrapper = document.querySelector('.el-table__body-wrapper') as HTMLElement
  if (tableWrapper) {
    tableWrapper.onscroll = null
  }
})

// 修改isCellEditable方法，使用factoryMap来判断特殊厂站
const isCellEditable = (day: number, row: any) => {
  // 如果是单耗行，所有人都不允许编辑
  if (row.subName.includes('单耗') || row.subName.includes('湿泥') || row.subName.includes('绝干污泥产量')) {
    return false
  }

  // 如果是报表管理员，可以编辑非单耗行
  if (isReportAdminRole.value) {
    return true
  }

  // 获取当前日期
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(today.getDate() - 1)

  // 特殊厂站列表：肥东、庐江、巢湖下属的厂站
  const specialFactoryNames = ['肥东', '庐江', '巢湖']

  // 检查当前选中的水厂是否属于特殊厂站
  const isSpecialFactory = () => {
    if (!selectedFactory.value) return false

    // 从映射中获取当前水厂
    const currentFactory = factoryMap.value.get(selectedFactory.value);
    if (!currentFactory) return false;

    // 检查当前水厂名称是否包含特殊厂站名称
    if (specialFactoryNames.some(name => currentFactory.name.includes(name))) {
      return true;
    }

    // 检查当前水厂的父级是否是特殊厂站
    // 这里需要遍历所有水厂来查找父子关系
    let isChildOfSpecial = false;
    factoryMap.value.forEach((factory) => {
      // 检查是否是特殊厂站
      const isSpecial = specialFactoryNames.some(name => factory.name.includes(name));
      if (!isSpecial) return;

      // 检查当前水厂是否是这个特殊厂站的子厂站
      const isChild = (parentFactory, targetId) => {
        if (parentFactory.childrenLevel3) {
          if (parentFactory.childrenLevel3.some(child => child.id === targetId)) {
            return true;
          }
        }

        if (parentFactory.children) {
          if (parentFactory.children.some(child => child.id === targetId)) {
            return true;
          }

          // 递归检查更深层次的子厂站
          for (const child of parentFactory.children) {
            if (isChild(child, targetId)) {
              return true;
            }
          }
        }

        return false;
      };

      if (isChild(factory, selectedFactory.value)) {
        isChildOfSpecial = true;
      }
    });

    return isChildOfSpecial;
  }

  // 如果是特殊厂站且是能耗(电量)指标，允许编辑前7天的数据
  if (row.name === '电量' && row.subName === '电量') {
    if (isSpecialFactory()) {
      // 计算日期是否在过去7天内
      const currentDate = new Date(selectedMonth.value.getFullYear(), selectedMonth.value.getMonth(), day)
      const sevenDaysAgo = new Date(today)
      sevenDaysAgo.setDate(today.getDate() - 7)

      // 如果日期在过去7天内，允许编辑
      if (currentDate >= sevenDaysAgo && currentDate < today) {
        console.log('特殊厂站电量指标7天内可编辑:', day);
        return true
      }
    }
  }

  // 非管理员用户只能编辑昨天的数据
  // 检查日期是否是昨天
  if (isYesterdayInSelectedMonth.value && day === yesterdayDay.value) {
    return true
  }

  // 非管理员用户不能编辑昨天之前的数据
  return false
}

// 表格列类型定义
interface TableColumn {
  label: string;
  prop?: string;
  width?: number;
  align?: string;
  fixed?: string | boolean;
  className?: string;
  headerCellStyle?: Record<string, string>;
}

// 表格列定义
const tableColumns = computed<TableColumn[]>(() => {
  const fixedColumns: TableColumn[] = [
    { label: '项目', prop: 'group', width: 120, align: 'center', fixed: 'left' },
    { label: '指标名称', prop: 'name', width: 110, align: 'center', fixed: 'left' },
    { label: '子指标', prop: 'subName', width: 100, align: 'center', fixed: 'left' },
    { label: '单位', prop: 'unit', width: 80, align: 'center', fixed: 'left' }
  ]

  const dayColumns: TableColumn[] = []

  // 添加昨天的列
  if (isYesterdayInSelectedMonth.value) {
    dayColumns.push({
      label: `${yesterdayDay.value}日`,
      prop: `day${yesterdayDay.value}`,
      width: 60,
      align: 'center',
      className: 'data-column',
      headerCellStyle: { background: '#e6f7ff', color: '#1890ff' }
    })
  }

  // 添加其他日期列
  filteredDisplayDays.value.forEach(day => {
    dayColumns.push({
      label: `${day}日`,
      prop: `day${day}`,
      width: 60,
      align: 'center',
      className: 'data-column'
    })
  })

  // 添加平均值列
  const avgColumn: TableColumn = {
    label: '平均值',
    width: 80,
    align: 'center',
    fixed: 'right'
  }

  return [...fixedColumns, ...dayColumns, avgColumn]
})

// 递归找到第一个 level=3 的节点
function findFirstLevel3(tree: any[]): any | null {
  for (const node of tree) {
    if (node.level === 3) return node;
    if (node.children) {
      const found = findFirstLevel3(node.children);
      if (found) return found;
    }
  }
  return null;
}


// 生成用于分组展示的工厂树（只保留有 level=3 子节点的父节点和所有 level=3 节点）
const groupedFactoryList = computed(() => {
  function filterTree(tree: any[]): any[] {
    return tree
      .map(node => {
        let childrenLevel3 = [];
        if (node.children && node.children.length) {
          childrenLevel3 = node.children.filter(child => child.level === 3);
        }
        return {
          ...node,
          childrenLevel3
        };
      })
      .filter(node => (node.childrenLevel3 && node.childrenLevel3.length) || node.level === 3)
  }
  return filterTree(factoryList.value)
})

// 补录功能相关
const additionalRecordingVisible = ref(false)
const additionalLoading = ref(false)
const additionalDateRange = ref<[Date, Date] | null>(null)
const additionalDisplayDays = ref<Date[]>([])
const additionalTableRef = ref(null)

// 禁用当前日期及之后的日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now() - 86400000 // 今天及之后的日期不可选
}

// 格式化补录日期显示
const formatAdditionalDate = (date: Date) => {
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

// 获取补录日期的key
const getAdditionalDayKey = (date: Date) => {
  return date.getDate()
}

// 获取当前选中水厂的名称
const currentFactoryName = computed(() => {
  if (!selectedFactory.value) return '';

  // 在工厂列表中查找匹配的水厂
  for (const group of factoryList.value) {
    // 检查当前节点
    if (group.id === selectedFactory.value) {
      return group.name;
    }

    // 检查子节点
    if (group.childrenLevel3 && group.childrenLevel3.length) {
      const found = group.childrenLevel3.find(item => item.id === selectedFactory.value);
      if (found) return found.name;
    }

    // 检查children
    if (group.children && group.children.length) {
      const found = findFactoryById(group.children, selectedFactory.value);
      if (found) return found.name;
    }
  }

  return '';
});

// 递归查找水厂
const findFactoryById = (factories: any[], id: number): any => {
  for (const factory of factories) {
    if (factory.id === id) {
      return factory;
    }

    if (factory.children && factory.children.length) {
      const found = findFactoryById(factory.children, id);
      if (found) return found;
    }
  }

  return null;
};

// 显示补录弹窗
const showAdditionalRecordingDialog = () => {
  if (!selectedFactory.value) {
    ElMessage.warning('请先选择水厂')
    return
  }
  console.log("xxxx")
  additionalRecordingVisible.value = true
  additionalDateRange.value = null
  additionalDisplayDays.value = []

  // 设置默认时间范围为上个月25日至上个月月底
  const today = new Date()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()

  // 计算上个月的年和月
  const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1
  const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear

  // 上个月25日
  const startDate = new Date(lastMonthYear, lastMonth, 25)

  // 上个月最后一天（通过设置下个月的第0天来获取）
  const endDate = new Date(currentYear, currentMonth, 0)

  additionalDateRange.value = [startDate, endDate]
  handleAdditionalDateRangeChange()
}

// 监听补录对话框的关闭事件
watch(additionalRecordingVisible, (newValue) => {
  console.log(newValue)
  if (!newValue) { // 当对话框关闭时
    // 刷新主页面数据
    loadData()
    console.log("shuaxin ")
    // 额外确保表格布局正确刷新
    nextTick(() => {
      refreshTableScroll()
      // 额外的延迟刷新，确保数据完全渲染
      setTimeout(refreshTableScroll, 300)
    })
  }
})

// 处理补录日期范围变化
const handleAdditionalDateRangeChange = async () => {
  if (!additionalDateRange.value) {
    additionalDisplayDays.value = []
    return
  }

  const [start, end] = additionalDateRange.value
  const days: Date[] = []

  // 生成日期范围内的所有日期
  const currentDate = new Date(start)
  while (currentDate <= end) {
    days.push(new Date(currentDate))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  additionalDisplayDays.value = days

  // 加载补录日期范围内的历史数据
  additionalLoading.value = true
  try {
    // 根据接口参数要求，使用月份格式调用查询接口
    // 由于日期范围可能跨月，需要获取每个月的数据并合并

    // 获取起始日期所在月份
    const startMonth = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}`

    // 获取结束日期所在月份
    const endMonth = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}`

    let allHistoryData: any[] = []

    // 如果起始月份和结束月份相同，只需查询一次
    if (startMonth === endMonth) {
      const searchParam = {
        factoryId: selectedFactory.value,
        month: startMonth
      }

      const historyData = await prodConsumptionDataAPI.queryDataByFactoryIdAndDate(searchParam)
      if (historyData && historyData.code === 0) {
        allHistoryData = historyData.data || []
      }
    } else {
      // 如果跨月，需要查询两个月的数据
      // 查询起始月
      const startMonthParam = {
        factoryId: selectedFactory.value,
        month: startMonth
      }

      const startMonthData = await prodConsumptionDataAPI.queryDataByFactoryIdAndDate(startMonthParam)
      if (startMonthData && startMonthData.code === 0) {
        allHistoryData = [...allHistoryData, ...(startMonthData.data || [])]
      }

      // 查询结束月
      const endMonthParam = {
        factoryId: selectedFactory.value,
        month: endMonth
      }

      const endMonthData = await prodConsumptionDataAPI.queryDataByFactoryIdAndDate(endMonthParam)
      if (endMonthData && endMonthData.code === 0) {
        allHistoryData = [...allHistoryData, ...(endMonthData.data || [])]
      }
    }

    // 清空现有数据，防止不同日期的数据混淆
    tableData.value.forEach(row => {
      additionalDisplayDays.value.forEach(day => {
        const dayKey = `day${getAdditionalDayKey(day)}`
        row[dayKey] = ''
      })
    })

    // 处理历史数据，更新表格
    updateAdditionalTableData(allHistoryData)

  } catch (error) {
    console.error('获取补录历史数据失败:', error)
    ElMessage.error('获取补录历史数据失败')
  } finally {
    additionalLoading.value = false
  }

  // 刷新表格布局
  nextTick(() => {
    if (additionalTableRef.value) {
      // @ts-ignore
      additionalTableRef.value.doLayout()
    }
  })
}

// 更新补录表格数据
const updateAdditionalTableData = (historyData: any[]) => {
  // 更新历史数据
  historyData.forEach(item => {
    const date = new Date(item.date)
    const day = getAdditionalDayKey(date)

    // 遍历表格数据，根据field字段匹配更新
    tableData.value.forEach(row => {
      if (row.field && item[row.field] !== undefined) {
        let value;
        if (row.field === 'treatVol' || row.field === 'inFlowVol') {
          // 如果为进水水量或者出水水量，填写值为m3 数据库存的为Km3 所以需要乘以1k
          // value = item[row.field] * 1000;
          value = new Big(item[row.field]).times(1000).round(4).toString();
        } else {
          value = item[row.field];
        }

        row[`day${day}`] = value === null ? EMPTY_VALUE : value.toString();
      }
    })
  })

  // 强制刷新视图
  tableData.value = [...tableData.value]
}

// 处理补录提交
const handleAdditionalSubmit = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('没有数据修改，无需提交')
    return
  }

  if (!selectedFactory.value) {
    ElMessage.warning('请先选择水厂,再进行数据提交')
    return
  }

  additionalLoading.value = true

  try {
    // 将修改的数据转换为按日期组织的数据结构
    const formattedData: Record<string, DailyData> = {}

    // 遍历所有修改过的日期
    Object.values(modifiedData.value).forEach((item: any) => {
      // 找到对应的日期对象
      const dayObj = additionalDisplayDays.value.find(date => date.getDate() === item.day);
      if (!dayObj) return;

      // 使用补录日期范围中的实际日期构建日期字符串
      const date = `${dayObj.getFullYear()}-${String(dayObj.getMonth() + 1).padStart(2, '0')}-${String(dayObj.getDate()).padStart(2, '0')}`

      // 初始化该日期的数据对象
      formattedData[date] = {
        date,
        factoryId: selectedFactory.value,
        reporterId: userId.value,
        reviewStatus: 0
      }

      // 获取该日期的所有指标数据
      tableData.value.forEach(row => {
        if (row.field) {
          const cellValue = row[`day${item.day}`];
          // 如果提交的数据为/ 则改为 null
          const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;

          // 不再需要特殊处理进水水量和出水水量，后端会处理转换
          (formattedData[date] as any)[row.field] = submitValue;
        }
      })
    })

    // 准备提交的数据结构
    const submitData = Object.values(formattedData)

    // 调用补录接口
    const res = await prodConsumptionDataAPI.additionalRecording(submitData)

    // 检查返回结果
    if (res && res.data) {
      ElMessage.success('补录成功')
      additionalRecordingVisible.value = false

      // 重置修改标记和修改记录
      hasChanges.value = false
      modifiedData.value = {}

      // 刷新主页面数据
      await loadData()

      // 额外确保表格布局正确刷新
      nextTick(() => {
        refreshTableScroll()
        // 额外的延迟刷新，确保数据完全渲染
        setTimeout(refreshTableScroll, 300)
      })
    } else {
      const errorMsg = res?.data?.msg || '补录失败'
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('补录提交失败:', error)
    ElMessage.error('补录失败，请检查网络连接或联系管理员')
  } finally {
    additionalLoading.value = false
  }
}

// 添加暂存功能
const handleTemporarilySave = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('没有数据修改，无需暂存')
    return
  }

  if (!selectedFactory.value) {
    ElMessage.warning('请先选择水厂,再进行数据暂存')
    return
  }

  try {
    // 将修改的数据转换为按日期组织的数据结构
    const formattedData: Record<string, DailyData> = {}

    // 遍历所有修改过的日期
    Object.values(modifiedData.value).forEach((item: any) => {
      const date = `${selectedMonth.value.getFullYear()}-${String(selectedMonth.value.getMonth() + 1).padStart(2, '0')}-${String(item.day).padStart(2, '0')}`

      // 初始化该日期的数据对象
      formattedData[date] = {
        date,
        factoryId: selectedFactory.value,
        reporterId: userId.value,
        reviewStatus: 5  // 设置为暂存状态
      }

      // 获取该日期的所有指标数据
      tableData.value.forEach(row => {
        if (row.field) {
          const cellValue = row[`day${item.day}`];
          // 如果提交的数据为/ 则改为 null
          const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;
          (formattedData[date] as any)[row.field] = submitValue;
        }
      })
    })

    // 准备提交的数据结构
    const submitData = Object.values(formattedData)

    // 调用暂存接口
    const res = await prodConsumptionDataAPI.saveTemporarily(submitData)
    if (res.code === 0) {
      loadData()
      ElMessage.success('暂存成功')
      // 暂存成功后重置修改标记和修改记录
      hasChanges.value = false
      modifiedData.value = {}
    } else {
      ElMessage.error(res.msg || '暂存失败')
    }
  } catch (error) {
    console.error('数据处理失败:', error)
    ElMessage.error('暂存失败')
  }
}

// 添加暂存状态标记
const hasTempData = ref(false)
</script>

<style scoped>
/* 表格基础样式 */
.app-container {
  /* padding: 20px; */
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card__body) {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  overflow: auto;
  background-color: #fff;
}

:deep(.el-table) {
  width: auto !important;
  min-width: auto !important;
  display: inline-block;
  border-collapse: collapse;
  height: 100%;
}

/* 固定列样式 */
:deep(.el-table) {
  .fixed-column {
    background-color: #bde7f9 !important;

    .el-table__cell {
      background-color: #bde7f9 !important;
    }

    .cell {
      background-color: #bde7f9 !important;
    }
  }

  .el-table__fixed-left {
    .el-table__cell {
      background-color: #bde7f9 !important;
    }

    .el-table__body td.el-table__cell {
      background-color: #bde7f9 !important;
    }

    .cell {
      background-color: #bde7f9 !important;
    }
  }
}

/* 表头样式 */
:deep(.el-table__header) {
  th.el-table__cell {
    background-color: #bde7f9 !important;
    color: #333;
    font-weight: 500;
    padding: 8px 0 !important;
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
  }
}

/* 数据列样式 */
:deep(.el-table) {
  .data-column {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
  }

  .el-table__row .data-column {
    background-color: #ffffff !important;
  }
}

/* 表格布局控制 */
:deep(.el-table__body),
:deep(.el-table__header) {
  table-layout: fixed !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

/* 单元格样式 */
:deep(.el-table .el-table__cell) {
  padding: 0 !important;
}

:deep(.el-table .cell) {
  padding: 0 4px;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 输入框样式 */
:deep(.el-input--small) {
  width: 100%;
  line-height: 24px;
}

:deep(.el-input__wrapper) {
  padding: 0 4px;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  text-align: center;
  padding: 0;
  height: 24px;
  line-height: 24px;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
}

/* 隐藏数字输入框的上下箭头 */
:deep(input[type="number"]::-webkit-inner-spin-button),
:deep(input[type="number"]::-webkit-outer-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

:deep(input[type="number"]) {
  -moz-appearance: textfield;
}

/* 表头和其他控件样式 */
.card-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.header-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 1000px;
  margin-left: 20px;
}

.control-item {
  width: 200px;
}

.control-item-large {
  width: 300px;
}

.date-picker {
  width: 200px;
}

.button-group {
  display: flex;
  gap: 8px;
}

:deep(.el-date-editor.el-input) {
  width: 200px;
}

:deep(.el-date-editor .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset !important;
}

:deep(.el-date-editor .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
}

:deep(.el-date-editor .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
}

.date-range-group {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.day-select {
  width: 100px;
}

.separator {
  color: var(--el-text-color-regular);
  margin: 0 4px;
}

/* 下拉选项样式 */
.parent-option {
  font-weight: bold;
  background-color: #f5f7fa;
}

.child-option {
  padding-left: 20px !important;
}

.child-factory-option {
  padding-left: 40px !important;
}

.sub-item-label {
  padding-left: 8px;
  color: #606266;
}

:deep(.el-select-dropdown__item) {
  padding: 0 20px;
  height: 34px;
  line-height: 34px;
}

:deep(.el-select-dropdown__item.parent-option) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.child-option) {
  padding-left: 40px;
}


:deep(.el-select-group__title) {
  font-size: 15px;
  font-weight: bold;
}

/* 添加补录弹窗相关样式 */
.additional-recording-container {
  padding: 10px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.date-range-picker {
  width: 350px;
}

/* 暂存数据样式 */
:deep(.temp-data .el-input__wrapper) {
  background-color: #fffbe5 !important;
  border: 1px dashed #e6a23c !important;
  box-shadow: none !important;
}

:deep(.temp-data .el-input__wrapper:hover) {
  background-color: #fffbe5 !important;
  border: 1px dashed #e6a23c !important;
}

:deep(.temp-data .el-input__wrapper.is-focus) {
  background-color: #fffbe5 !important;
  border: 1px dashed #e6a23c !important;
}

.temp-data-indicator {
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-right: 10px;
}
</style>
