import request from '@/config/axios'

/**
 * 监控配置接口
 */
export const MonitorApi = {
  /**
   * 获取监控点位列表
   */
  getPointTree: async () => {
    try {
      const response = await request.get({ url: '/monitor/video/getPointTree' })
      return response
    } catch (error) {
      console.error('获取监控点位树API调用失败:', error)
      throw error
    }
  },
  
  /**
   * 开始监控视频
   */
  startVideo: async (data) => {
    try {
      const response = await request.post({ url: '/monitor/video/StartVideo', data })
      return response
    } catch (error) {
      console.error('开始监控视频API调用失败:', error)
      throw error
    }
  },

  /**
   * 操作摄像头
   */
  OperateCamera: async (data) => {
    try {
      const response = await request.post({ url: '/monitor/video/OperateCamera', data })
      return response
    } catch (error) {
      console.error('操作摄像头API调用失败:', error)
      throw error
    }
  },

  /**
   * 控制摄像头方向
   */
  OperateDirect: async (data) => {
    try {
      const response = await request.post({ url: '/monitor/video/OperateDirect', data })
      return response
    } catch (error) {
      console.error('控制摄像头方向API调用失败:', error)
      throw error
    }
  },

  /**
   * 获取Token
   */
  getToken: async () => {
    try {
      const response = await request.get({ url: '/monitor/video/getToken' })
      return response as unknown as string
    } catch (error) {
      console.error('获取Token API调用失败:', error)
      throw error
    }
  }
}
