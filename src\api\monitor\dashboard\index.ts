import request from '@/config/axios'

// 定义类型
interface DataBoardSearchReqDTO {
  factoryId?: string;
  factoryCode?: string;
  pointCodeList?: string[];
  indicatorCode?: string;
  startTime?: string;
  endTime?: string;
}
// 数据看板 API
export const DashBoardApi = {
  // 查询指标数据趋势数据
  queryPointTrendData: async (data: DataBoardSearchReqDTO) => {
    return await request.postOriginal({ url: `/monitor/data-board/query-point-trend-data`, data })
  },
  // 查询指标最新数据
  queryPointLastData: async (data: DataBoardSearchReqDTO) => {
    return await request.postOriginal({ url: `/monitor/data-board/query-point-last-data`, data })
  },
 // 查询指标24小时趋势数据
 singlePointTrendData24h: async (data: DataBoardSearchReqDTO) => {
  return await request.postOriginal({ url: `/monitor/data-board/single-point-trend-data-24h`, data })
},
  // 获取厂站指标元数据
  getFactoryMetrics: async (factoryId: string) => {
    return await request.getOriginal({
      url: `/monitor/themeIndicator/indicatorGroupByFactory/${factoryId}`,
    });
  }

}
