import request from '@/config/axios'


// 数据集管理 API
export const prodConsumptionDataAPI = {
  // 查询数据集管理分页
  queryDataByFactoryIdAndDate: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-consumption-data/list`,data})
  },

  // 查询数据集管理分页
  saveOrUpdate: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-consumption-data/saveOrUpdateBatch`,data})
  },

  // 补录数据
  additionalRecording: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-consumption-data/additional-recording`,data})
  },

  // 查询汇总数据
  queryProductCollectData: async (params : any) => {
    return await request.getOriginal({url: `/report/prod-consumption-data/stat/all`,params})
  },

  // 获取消耗统计数据（平均值、最大值、最小值、累计值）
  getConsumptionStat: async (params: any) => {
    return await request.get({url: `/report/prod-consumption-data/consumption-stat`, params})
  },

  // 暂存数据
  saveTemporarily: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-consumption-data/saveTemporarily`,data})
  }
}