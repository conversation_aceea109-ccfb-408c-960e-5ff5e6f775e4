<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh, Search } from '@element-plus/icons-vue'
import SummaryCard from '@/components/SummaryCard/index.vue'
import Echart from '@/components/Echart/src/Echart.vue'
import { formatDate } from '@/utils/formatTime'
import * as XLSX from 'xlsx'

// 商品类型选项
const productTypeOptions = ref([
  { label: '全部', value: 'all' },
  { label: '化学药剂', value: 'chemical' },
  { label: '设备配件', value: 'equipment' },
  { label: '办公用品', value: 'office' },
  { label: '维修材料', value: 'maintenance' }
])

// 仓库选项
const warehouseOptions = ref([
  { label: '全部仓库', value: 'all' },
  { label: '主仓库', value: 'main' },
  { label: '化学品仓库', value: 'chemical' },
  { label: '配件仓库', value: 'parts' },
  { label: '临时仓库', value: 'temp' }
])

// 用途分类选项
const usageOptions = ref([
  { label: '全部', value: 'all' },
  { label: '生产用', value: 'production' },
  { label: '维修用', value: 'maintenance' },
  { label: '办公用', value: 'office' },
  { label: '备用', value: 'backup' }
])

// 筛选条件
const filters = ref({
  productType: 'all',
  warehouse: 'all',
  usage: 'all',
  timeRange: []
})

// 原始数据（不变的基础数据）
const originalData = ref({
  stockSummary: {
    totalValue: 2580000,
    totalQuantity: 15680,
    warehouseCount: 5,
    lowStockCount: 12
  },
  dimensionStats: {
    byWarehouse: [
      { name: '主仓库', value: 8500, amount: 1200000, type: 'main' },
      { name: '化学品仓库', value: 3200, amount: 850000, type: 'chemical' },
      { name: '配件仓库', value: 2800, amount: 420000, type: 'parts' },
      { name: '临时仓库', value: 1180, amount: 110000, type: 'temp' }
    ],
    byProduct: [
      { name: '化学药剂', value: 5200, amount: 1580000, type: 'chemical' },
      { name: '设备配件', value: 4800, amount: 680000, type: 'equipment' },
      { name: '办公用品', value: 3200, amount: 180000, type: 'office' },
      { name: '维修材料', value: 2480, amount: 140000, type: 'maintenance' }
    ],
    byUsage: [
      { name: '生产用', value: 9800, amount: 1980000, type: 'production' },
      { name: '维修用', value: 3200, amount: 380000, type: 'maintenance' },
      { name: '办公用', value: 1800, amount: 120000, type: 'office' },
      { name: '备用', value: 880, amount: 100000, type: 'backup' }
    ]
  },
  warningData: [
    { id: 1, materialName: '聚合氯化铝', materialCode: 'PAC001', warehouse: '化学品仓库', warehouseType: 'chemical', currentStock: 50, minLimit: 100, maxLimit: 1000, status: 'low', usage: '生产用', usageType: 'production', productType: 'chemical' },
    { id: 2, materialName: '活性炭', materialCode: 'AC002', warehouse: '主仓库', warehouseType: 'main', currentStock: 25, minLimit: 50, maxLimit: 500, status: 'low', usage: '生产用', usageType: 'production', productType: 'chemical' },
    { id: 3, materialName: '水泵轴承', materialCode: 'BR003', warehouse: '配件仓库', warehouseType: 'parts', currentStock: 5, minLimit: 10, maxLimit: 100, status: 'critical', usage: '维修用', usageType: 'maintenance', productType: 'equipment' },
    { id: 4, materialName: '过滤膜', materialCode: 'FM004', warehouse: '主仓库', warehouseType: 'main', currentStock: 1200, minLimit: 100, maxLimit: 1000, status: 'high', usage: '生产用', usageType: 'production', productType: 'equipment' },
    { id: 5, materialName: '消毒剂', materialCode: 'DS005', warehouse: '化学品仓库', warehouseType: 'chemical', currentStock: 15, minLimit: 30, maxLimit: 300, status: 'low', usage: '生产用', usageType: 'production', productType: 'chemical' },
    { id: 6, materialName: '办公纸张', materialCode: 'OF006', warehouse: '主仓库', warehouseType: 'main', currentStock: 200, minLimit: 50, maxLimit: 500, status: 'normal', usage: '办公用', usageType: 'office', productType: 'office' },
    { id: 7, materialName: '维修工具', materialCode: 'MT007', warehouse: '配件仓库', warehouseType: 'parts', currentStock: 30, minLimit: 20, maxLimit: 100, status: 'normal', usage: '维修用', usageType: 'maintenance', productType: 'maintenance' },
    { id: 8, materialName: '备用电机', materialCode: 'EM008', warehouse: '临时仓库', warehouseType: 'temp', currentStock: 2, minLimit: 5, maxLimit: 20, status: 'critical', usage: '备用', usageType: 'backup', productType: 'equipment' }
  ]
})

// 数据过滤函数
const filterDataByConditions = (data, filters) => {
  return data.filter(item => {
    // 商品类型筛选
    if (filters.productType !== 'all' && item.productType !== filters.productType) {
      return false
    }

    // 仓库筛选
    if (filters.warehouse !== 'all' && item.warehouseType !== filters.warehouse) {
      return false
    }

    // 用途筛选
    if (filters.usage !== 'all' && item.usageType !== filters.usage) {
      return false
    }

    return true
  })
}

// 实时库存汇总数据（计算属性）
const stockSummary = computed(() => {
  const filteredWarnings = filterDataByConditions(originalData.value.warningData, filters.value)

  // 根据筛选条件计算汇总数据
  let filteredStats = originalData.value.dimensionStats.byProduct
  if (filters.value.productType !== 'all') {
    filteredStats = filteredStats.filter(item => item.type === filters.value.productType)
  }

  const totalValue = filteredStats.reduce((sum, item) => sum + item.amount, 0)
  const totalQuantity = filteredStats.reduce((sum, item) => sum + item.value, 0)

  return {
    totalValue,
    totalQuantity,
    warehouseCount: filters.value.warehouse === 'all' ? 5 : 1,
    lowStockCount: filteredWarnings.filter(item => item.status === 'low' || item.status === 'critical').length
  }
})

// 按维度统计数据（计算属性）
const dimensionStats = computed(() => {
  let byWarehouse = [...originalData.value.dimensionStats.byWarehouse]
  let byProduct = [...originalData.value.dimensionStats.byProduct]
  let byUsage = [...originalData.value.dimensionStats.byUsage]

  // 根据筛选条件过滤数据
  if (filters.value.warehouse !== 'all') {
    byWarehouse = byWarehouse.filter(item => item.type === filters.value.warehouse)
  }

  if (filters.value.productType !== 'all') {
    byProduct = byProduct.filter(item => item.type === filters.value.productType)
  }

  if (filters.value.usage !== 'all') {
    byUsage = byUsage.filter(item => item.type === filters.value.usage)
  }

  return {
    byWarehouse,
    byProduct,
    byUsage
  }
})

// 出入库趋势数据
const trendData = ref({
  dates: [],
  inStock: [],
  outStock: [],
  netChange: []
})

// 库存预警数据（计算属性）
const warningData = computed(() => {
  return filterDataByConditions(originalData.value.warningData, filters.value)
})



// 查询条件
const queryForm = ref({
  materialName: '',
  materialCode: '',
  warehouse: '',
  timeRange: [],
  usage: ''
})

// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 生成模拟趋势数据
const generateTrendData = () => {
  const dates = []
  const inStock = []
  const outStock = []
  const netChange = []

  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(formatDate(date, 'MM-DD'))

    const inValue = Math.floor(Math.random() * 200) + 100
    const outValue = Math.floor(Math.random() * 180) + 80
    inStock.push(inValue)
    outStock.push(outValue)
    netChange.push(inValue - outValue)
  }

  trendData.value = { dates, inStock, outStock, netChange }
}

// 原始查询数据
const originalQueryData = ref([])

// 生成模拟查询数据
const generateQueryData = () => {
  const materials = [
    { name: '聚合氯化铝', productType: 'chemical' },
    { name: '活性炭', productType: 'chemical' },
    { name: '水泵轴承', productType: 'equipment' },
    { name: '过滤膜', productType: 'equipment' },
    { name: '消毒剂', productType: 'chemical' },
    { name: '絮凝剂', productType: 'chemical' },
    { name: '石英砂', productType: 'maintenance' },
    { name: '无烟煤', productType: 'maintenance' },
    { name: '办公纸张', productType: 'office' },
    { name: '打印机', productType: 'office' }
  ]

  const warehouses = [
    { name: '主仓库', type: 'main' },
    { name: '化学品仓库', type: 'chemical' },
    { name: '配件仓库', type: 'parts' },
    { name: '临时仓库', type: 'temp' }
  ]

  const usages = [
    { name: '生产用', type: 'production' },
    { name: '维修用', type: 'maintenance' },
    { name: '办公用', type: 'office' },
    { name: '备用', type: 'backup' }
  ]

  const data = []
  for (let i = 0; i < 50; i++) {
    const material = materials[Math.floor(Math.random() * materials.length)]
    const warehouse = warehouses[Math.floor(Math.random() * warehouses.length)]
    const usage = usages[Math.floor(Math.random() * usages.length)]

    data.push({
      id: i + 1,
      materialCode: `MAT${String(i + 1).padStart(3, '0')}`,
      materialName: material.name,
      productType: material.productType,
      warehouse: warehouse.name,
      warehouseType: warehouse.type,
      usage: usage.name,
      usageType: usage.type,
      unit: '吨',
      currentStock: Math.floor(Math.random() * 1000) + 10,
      unitPrice: (Math.random() * 1000 + 100).toFixed(2),
      totalValue: 0,
      lastUpdateTime: formatDate(new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD HH:mm:ss')
    })
  }

  data.forEach(item => {
    item.totalValue = (item.currentStock * parseFloat(item.unitPrice)).toFixed(2)
  })

  originalQueryData.value = data
}

// 过滤后的查询数据（计算属性）
const filteredQueryData = computed(() => {
  let data = [...originalQueryData.value]

  // 应用全局筛选条件
  data = filterDataByConditions(data, filters.value)

  // 应用查询表单条件
  if (queryForm.value.materialName) {
    data = data.filter(item =>
      item.materialName.includes(queryForm.value.materialName)
    )
  }

  if (queryForm.value.materialCode) {
    data = data.filter(item =>
      item.materialCode.includes(queryForm.value.materialCode)
    )
  }

  if (queryForm.value.warehouse) {
    data = data.filter(item =>
      item.warehouse === queryForm.value.warehouse
    )
  }

  if (queryForm.value.usage) {
    data = data.filter(item =>
      item.usage === queryForm.value.usage
    )
  }

  return data
})

// 分页后的查询数据
const queryTableData = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredQueryData.value.slice(start, end)
})

// 更新分页总数
watch(filteredQueryData, (newData) => {
  pagination.value.total = newData.length
  pagination.value.currentPage = 1
}, { immediate: true })

// 计算图表配置
const trendChartOptions = computed(() => ({
  title: {
    text: '出入库趋势分析',
    left: 'center',
    textStyle: { fontSize: '1rem' }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    data: ['入库', '出库', '净变化'],
    bottom: 0
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: trendData.value.dates,
    axisLabel: { fontSize: '0.8rem' }
  },
  yAxis: {
    type: 'value',
    axisLabel: { fontSize: '0.8rem' }
  },
  series: [
    {
      name: '入库',
      type: 'line',
      data: trendData.value.inStock,
      smooth: true,
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '出库',
      type: 'line',
      data: trendData.value.outStock,
      smooth: true,
      itemStyle: { color: '#F56C6C' }
    },
    {
      name: '净变化',
      type: 'bar',
      data: trendData.value.netChange,
      itemStyle: { color: '#409EFF' }
    }
  ]
}))

const warehouseChartOptions = computed(() => ({
  title: {
    text: '仓库库存分布',
    left: 'center',
    textStyle: { fontSize: '1rem' }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '库存数量',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '1.2rem',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: dimensionStats.value.byWarehouse.map(item => ({
        value: item.value,
        name: item.name
      }))
    }
  ]
}))

// 获取预警状态样式
const getWarningStatusClass = (status) => {
  switch (status) {
    case 'critical': return 'text-red-600 bg-red-50'
    case 'low': return 'text-orange-600 bg-orange-50'
    case 'high': return 'text-blue-600 bg-blue-50'
    default: return 'text-green-600 bg-green-50'
  }
}

// 获取预警状态文本
const getWarningStatusText = (status) => {
  switch (status) {
    case 'critical': return '严重不足'
    case 'low': return '库存不足'
    case 'high': return '库存过多'
    default: return '正常'
  }
}

// 刷新数据
const handleRefresh = () => {
  generateTrendData()
  generateQueryData()
  ElMessage.success('数据已刷新')
}

// 重置筛选条件
const handleFilterReset = () => {
  filters.value = {
    productType: 'all',
    warehouse: 'all',
    usage: 'all',
    timeRange: []
  }
  ElMessage.success('筛选条件已重置')
}

// 监听筛选条件变化
watch(filters, () => {
  // 筛选条件变化时，重置查询表单的分页
  pagination.value.currentPage = 1
}, { deep: true })

// 导出台账
const handleExport = () => {
  try {
    // 准备导出数据
    const exportData = []

    // 添加汇总信息
    exportData.push({
      '数据类型': '库存汇总',
      '项目': '库存总金额',
      '数值': stockSummary.value.totalValue,
      '单位': '元',
      '备注': `筛选条件: 商品类型=${filters.value.productType}, 仓库=${filters.value.warehouse}, 用途=${filters.value.usage}`
    })

    exportData.push({
      '数据类型': '库存汇总',
      '项目': '库存总数量',
      '数值': stockSummary.value.totalQuantity,
      '单位': '件',
      '备注': ''
    })

    exportData.push({
      '数据类型': '库存汇总',
      '项目': '仓库数量',
      '数值': stockSummary.value.warehouseCount,
      '单位': '个',
      '备注': ''
    })

    exportData.push({
      '数据类型': '库存汇总',
      '项目': '预警商品数量',
      '数值': stockSummary.value.lowStockCount,
      '单位': '种',
      '备注': ''
    })

    // 添加空行分隔
    exportData.push({})

    // 添加仓库分布数据
    dimensionStats.value.byWarehouse.forEach(item => {
      exportData.push({
        '数据类型': '仓库分布',
        '项目': item.name,
        '数值': item.value,
        '单位': '件',
        '备注': `金额: ${item.amount}元`
      })
    })

    // 添加空行分隔
    exportData.push({})

    // 添加商品类型分布数据
    dimensionStats.value.byProduct.forEach(item => {
      exportData.push({
        '数据类型': '商品类型分布',
        '项目': item.name,
        '数值': item.value,
        '单位': '件',
        '备注': `金额: ${item.amount}元`
      })
    })

    // 添加空行分隔
    exportData.push({})

    // 添加用途分布数据
    dimensionStats.value.byUsage.forEach(item => {
      exportData.push({
        '数据类型': '用途分布',
        '项目': item.name,
        '数值': item.value,
        '单位': '件',
        '备注': `金额: ${item.amount}元`
      })
    })

    // 添加空行分隔
    exportData.push({})

    // 添加预警数据
    warningData.value.forEach(item => {
      exportData.push({
        '数据类型': '库存预警',
        '项目': item.materialName,
        '数值': item.currentStock,
        '单位': '件',
        '备注': `编码: ${item.materialCode}, 仓库: ${item.warehouse}, 状态: ${getWarningStatusText(item.status)}, 最小限制: ${item.minLimit}, 最大限制: ${item.maxLimit}`
      })
    })

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 数据类型
      { wch: 20 }, // 项目
      { wch: 15 }, // 数值
      { wch: 10 }, // 单位
      { wch: 50 }  // 备注
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '库存总览台账')

    // 生成文件名
    const now = new Date()
    const fileName = `库存总览台账_${formatDate(now, 'YYYY-MM-DD_HH-mm-ss')}.xlsx`

    // 导出文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success('台账导出成功！')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 查询库存
const handleQuery = () => {
  // 查询逻辑已通过计算属性自动处理
  pagination.value.currentPage = 1
  ElMessage.success('查询完成')
}

// 重置查询
const handleReset = () => {
  queryForm.value = {
    materialName: '',
    materialCode: '',
    warehouse: '',
    timeRange: [],
    usage: ''
  }
  pagination.value.currentPage = 1
  ElMessage.success('已重置查询条件')
}



// 分页处理
const handlePageChange = (page) => {
  pagination.value.currentPage = page
}

const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

// 组件挂载时初始化数据
onMounted(() => {
  generateTrendData()
  generateQueryData()
})
</script>

<template>
  <div class="stock-overview">
    <!-- 页面标题 -->
    <div class="header-section">
      <h1 class="page-title">库存总览</h1>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-row">
        <div class="filter-item">
          <label>商品类型：</label>
          <el-select v-model="filters.productType" placeholder="请选择">
            <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="filter-item">
          <label>仓库：</label>
          <el-select v-model="filters.warehouse" placeholder="请选择">
            <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="filter-item">
          <label>用途：</label>
          <el-select v-model="filters.usage" placeholder="请选择">
            <el-option v-for="item in usageOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="filter-item">
          <label>时间范围：</label>
          <el-date-picker v-model="filters.timeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD" />
        </div>
        <div class="filter-item">
          <el-button type="primary" @click="handleFilterReset">重置筛选</el-button>
        </div>
        <div class="filter-actions">
          <el-button type="primary" :icon="Refresh" @click="handleRefresh">刷新数据</el-button>
          <el-button :icon="Download" @click="handleExport">导出台账</el-button>
        </div>
      </div>
    </el-card>

    <!-- 实时库存汇总 -->
    <div class="summary-section">
      <div class="summary-grid">
        <SummaryCard title="库存总金额" :value="stockSummary.totalValue" unit="元" icon="ep:money" icon-color="text-green-600"
          icon-bg-color="bg-green-100" :decimals="0" />
        <SummaryCard title="库存总数量" :value="stockSummary.totalQuantity" unit="件" icon="ep:box" icon-color="text-blue-600"
          icon-bg-color="bg-blue-100" :decimals="0" />
        <SummaryCard title="仓库数量" :value="stockSummary.warehouseCount" unit="个" icon="ep:house"
          icon-color="text-purple-600" icon-bg-color="bg-purple-100" :decimals="0" />
        <SummaryCard title="预警商品" :value="stockSummary.lowStockCount" unit="种" icon="ep:warning"
          icon-color="text-red-600" icon-bg-color="bg-red-100" :decimals="0" />
      </div>
    </div>

    <!-- 图形化分析 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 出入库趋势图 -->
        <el-card class="chart-card">
          <template #header>
            <span class="card-title">出入库趋势分析</span>
          </template>
          <div class="chart-container">
            <Echart :options="trendChartOptions" height="20rem" />
          </div>
        </el-card>

        <!-- 仓库库存结构图 -->
        <el-card class="chart-card">
          <template #header>
            <span class="card-title">仓库库存分布</span>
          </template>
          <div class="chart-container">
            <Echart :options="warehouseChartOptions" height="20rem" />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 库存预警 -->
    <el-card class="warning-section">
      <template #header>
        <div class="section-header">
          <span class="card-title">库存预警</span>
          <el-tag type="danger" size="small">{{warningData.filter(item => item.status === 'critical').length}}
            严重</el-tag>
          <el-tag type="warning" size="small">{{warningData.filter(item => item.status === 'low').length}} 不足</el-tag>
        </div>
      </template>
      <el-table :data="warningData" border>
        <el-table-column prop="materialCode" label="物料编码" width="120" align="center" />
        <el-table-column prop="materialName" label="物料名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="warehouse" label="仓库" width="120" align="center" />
        <el-table-column prop="usage" label="用途" width="100" align="center" />
        <el-table-column prop="currentStock" label="当前库存" width="100" align="center" />
        <el-table-column prop="minLimit" label="最小限制" width="100" align="center" />
        <el-table-column prop="maxLimit" label="最大限制" width="100" align="center" />
        <el-table-column label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :class="getWarningStatusClass(row.status)" size="small">
              {{ getWarningStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

      </el-table>
    </el-card>

    <!-- 库存查询 -->
    <el-card class="query-section">
      <template #header>
        <span class="card-title">库存查询</span>
      </template>

      <!-- 查询条件 -->
      <div class="query-form">
        <el-form :model="queryForm" inline>
          <el-form-item label="物料名称：">
            <el-input v-model="queryForm.materialName" placeholder="请输入物料名称" clearable />
          </el-form-item>
          <el-form-item label="物料编码：">
            <el-input v-model="queryForm.materialCode" placeholder="请输入物料编码" clearable />
          </el-form-item>
          <el-form-item label="仓库：">
            <el-select v-model="queryForm.warehouse" placeholder="请选择仓库" clearable>
              <el-option v-for="item in warehouseOptions.slice(1)" :key="item.value" :label="item.label"
                :value="item.label" />
            </el-select>
          </el-form-item>
          <el-form-item label="用途：">
            <el-select v-model="queryForm.usage" placeholder="请选择用途" clearable>
              <el-option v-for="item in usageOptions.slice(1)" :key="item.value" :label="item.label"
                :value="item.label" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间段：">
            <el-date-picker v-model="queryForm.timeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button :icon="Download" @click="handleExport">导出</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 查询结果表格 -->
      <el-table :data="queryTableData" border>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="materialCode" label="物料编码" width="120" align="center" />
        <el-table-column prop="materialName" label="物料名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="warehouse" label="仓库" width="120" align="center" />
        <el-table-column prop="usage" label="用途" width="100" align="center" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="currentStock" label="当前库存" width="100" align="center" />
        <el-table-column prop="unitPrice" label="单价(元)" width="100" align="center" />
        <el-table-column prop="totalValue" label="总价值(元)" width="120" align="center" />
        <el-table-column prop="lastUpdateTime" label="最后更新时间" width="160" align="center" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handlePageChange" />
      </div>
    </el-card>
  </div>
</template>

<style scoped>
/* 使用rem单位实现自适应布局 */
.stock-overview {
  padding: 1.5rem;
  min-height: calc(100vh - 4rem);
  background-color: #f5f7fa;
}

/* 页面标题区域 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 筛选条件卡片 */
.filter-card {
  margin-bottom: 1.5rem;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-item label {
  font-size: 0.9rem;
  color: #606266;
  white-space: nowrap;
}

/* 汇总统计区域 */
.summary-section {
  margin-bottom: 1.5rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15rem, 1fr));
  gap: 1rem;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 1.5rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.chart-card {
  min-height: 25rem;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  width: 100%;
  height: 100%;
}

/* 预警区域 */
.warning-section {
  margin-bottom: 1.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 查询区域 */
.query-section {
  margin-bottom: 1.5rem;
}

.query-form {
  margin-bottom: 1rem;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stock-overview {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }
}

/* Element Plus 组件样式调整 */
:deep(.el-card__header) {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 1.25rem;
}

:deep(.el-table) {
  font-size: 0.9rem;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-form-item__label) {
  font-size: 0.9rem;
}

:deep(.el-input__inner) {
  font-size: 0.9rem;
}

:deep(.el-select) {
  min-width: 8rem;
}

:deep(.el-button) {
  font-size: 0.9rem;
}

:deep(.el-tag) {
  font-size: 0.8rem;
}
</style>
