<template>
  <div class="app-container" v-loading="loading" element-loading-text="数据加载中，请稍候...">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-content">
            <div class="header-title">生产质量数据填报</div>
            <div v-if="hasTempData" class="temp-data-indicator">
              <el-alert title="存在未提交的暂存数据" type="warning" :closable="false" show-icon style="margin-right: 15px;" />
            </div>
            <div class="header-controls">
              <!-- <el-select v-model="selectedFactory" placeholder="请选择水厂" class="control-item" filterable>
                <template v-for="group in groupedFactoryList">
                  <el-option-group v-if="group.childrenLevel3 && group.childrenLevel3.length" :key="'group-' + group.id"
                    :label="group.name">
                    <el-option v-for="item in group.childrenLevel3" :key="'item-' + item.id" :label="item.name"
                      :value="item.id" class="child-factory-option" />
                  </el-option-group>
                  <el-option v-else-if="group.level === 3" :key="'item-' + group.id" :label="group.name"
                    :value="group.id" />
                </template>
</el-select> -->
              <el-select v-model="selectedIndicators" multiple collapse-tags collapse-tags-tooltip placeholder="指标筛选"
                class="control-item control-item-large" clearable @clear="handleClearIndicators"
                @change="handleIndicatorChange">
                <el-option-group v-for="group in indicatorGroups" :key="group.value" :label="group.label">
                  <el-option :label="group.label" :value="group.value" class="group-option">
                    {{ group.label }}
                  </el-option>
                  <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value"
                    class="indicator-option">
                    {{ item.label }}
                  </el-option>
                </el-option-group>
              </el-select>
              <el-date-picker v-model="selectedMonth" type="month" placeholder="选择月份" class="control-item date-picker"
                @change="handleMonthChange" />
              <div class="date-range-group">
                <el-select v-model="startDay" placeholder="起始日" class="day-select" clearable
                  @change="handleDayRangeChange">
                  <el-option v-for="day in availableStartDays" :key="day" :label="`${day}日`" :value="day" />
                </el-select>
                <span class="separator">至</span>
                <el-select v-model="endDay" placeholder="结束日" class="day-select" clearable
                  @change="handleDayRangeChange">
                  <el-option v-for="day in availableEndDays" :key="day" :label="`${day}日`" :value="day" />
                </el-select>
                <el-button type="info" @click="handleTemporarilySave">暂存</el-button>
                <el-button v-hasPermi="['report:prod-quality-data:submit']" type="primary"
                  @click="handleSubmit">提交</el-button>
                <el-button v-hasPermi="['report:prod-quality-data:export']" type="success"
                  @click="handleExport">导出</el-button>
                <SupplementButton report-type="quality" @click="showAdditionalRecordingDialog" />
              </div>
            </div>
          </div>
        </div>
      </template>

      <el-table :data="filteredTableData" border style="width: 100%"
        :header-cell-style="{ backgroundColor: '#bde7f9', padding: '4px 0' }" :cell-style="({ column }) => {
          const isFixedColumn = column.fixed === 'left';
          return {
            padding: '2px 0',
            backgroundColor: isFixedColumn ? '#bde7f9' : '#ffffff'
          }
        }" :span-method="handleSpanMethod" size="small">
        <!-- 指标分组（固定列） -->
        <el-table-column fixed="left" label="指标分类" prop="group" width="120" align="center">
          <template #default="scope">
            <template v-if="isFirstInGroup(scope.row, scope.$index)">
              {{ scope.row.group }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="left" label="指标名称" prop="name" width="120" align="center" />
        <el-table-column fixed="left" label="单位" prop="unit" width="80" align="center" />
        <el-table-column fixed="left" label="标准值" prop="standard" width="100" align="center" />

        <!-- 日期列（可滚动区域） -->
        <el-table-column v-if="isYesterdayColumnVisible" :label="`${yesterdayDay}日`" :prop="`day${yesterdayDay}`"
          width="60" align="center" :header-cell-style="{ background: '#e6f7ff !important', color: '#1890ff' }">
          <template #header>
            <div
              style="background-color: #e6f7ff; color: #1890ff; font-weight: bold; height: 100%; display: flex; align-items: center; justify-content: center;">
              {{ yesterdayDay }}日
            </div>
          </template>
          <template #default="{ row }">
            <el-input v-model="row[`day${yesterdayDay}`]" size="small" type="text" :step="0.01" :min="0"
              :disabled="!isCellEditable(yesterdayDay, row)"
              @input="(val: string) => handleValueChange(row, yesterdayDay, val)"
              @blur="(e: FocusEvent) => handleInputBlur(e, row, yesterdayDay)" @wheel.prevent
              :class="{ 'temp-data': row[`day${yesterdayDay}Temp`] }" />
          </template>
        </el-table-column>
        <el-table-column v-for="day in filteredDisplayDays" :key="day" :label="`${day}日`" :prop="`day${day}`" width="60"
          align="center">
          <template #default="{ row }">
            <el-input v-model="row[`day${day}`]" size="small" type="text" :step="0.01" :min="0"
              :disabled="!isCellEditable(day, row)" @input="(val: string) => handleValueChange(row, day, val)"
              @blur="(e: FocusEvent) => handleInputBlur(e, row, day)" @wheel.prevent
              :class="{ 'temp-data': row[`day${day}Temp`] }" />
          </template>
        </el-table-column>

        <!-- 添加汇总列 -->
        <el-table-column label="平均值" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <span>{{ calculateAverage(row) }}</span>
          </template>
        </el-table-column>

        <!-- 添加最大值列 -->
        <el-table-column label="最大值" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <span>{{ calculateMax(row) }}</span>
          </template>
        </el-table-column>

        <!-- 添加最小值列 -->
        <el-table-column label="最小值" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <span>{{ calculateMin(row) }}</span>
          </template>
        </el-table-column>

        <!-- 累计值列 -->
        <el-table-column label="累计值" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <span>{{ calculateTotal(row) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 补录弹窗 -->
    <el-dialog v-model="additionalRecordingVisible" :title="`数据补录 - ${currentFactoryName}`" width="90%" append-to-body
      destroy-on-close>
      <div class="additional-recording-container" v-loading="additionalLoading" element-loading-text="数据加载中，请稍候...">
        <div class="filter-row">
          <el-date-picker v-model="additionalDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :disabled-date="disabledDate" @change="handleAdditionalDateRangeChange"
            class="date-range-picker" />
          <el-button type="primary" @click="handleAdditionalSubmit">提交补录</el-button>
          <el-button @click="additionalRecordingVisible = false">取消</el-button>
        </div>

        <el-table :data="filteredTableData" border style="width: 100%; margin-top: 15px;"
          :header-cell-style="{ backgroundColor: '#bde7f9', padding: '4px 0' }" :cell-style="({ column }) => {
            const isFixedColumn = column.fixed === 'left';
            return {
              padding: '2px 0',
              backgroundColor: isFixedColumn ? '#bde7f9' : '#ffffff'
            }
          }" :span-method="handleSpanMethod" size="small" ref="additionalTableRef">
          <!-- 指标分组（固定列） -->
          <el-table-column fixed="left" label="指标分类" prop="group" width="120" align="center">
            <template #default="scope">
              <template v-if="isFirstInGroup(scope.row, scope.$index)">
                {{ scope.row.group }}
              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="left" label="指标名称" prop="name" width="120" align="center" />
          <el-table-column fixed="left" label="单位" prop="unit" width="80" align="center" />
          <el-table-column fixed="left" label="标准值" prop="standard" width="100" align="center" />

          <!-- 日期列（可滚动区域） -->
          <el-table-column v-for="day in additionalDisplayDays" :key="day" :label="`${formatAdditionalDate(day)}`"
            width="100" align="center">
            <template #default="{ row }">
              <el-input v-model="row[`day${getAdditionalDayKey(day)}`]" size="small" type="text" :step="0.01" :min="0"
                @input="(val) => handleValueChange(row, getAdditionalDayKey(day), val)"
                @blur="(e) => handleInputBlur(e, row, getAdditionalDayKey(day))" @wheel.prevent />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { FactoryApi } from '@/api/report/factory/index'
import { getIndicatorStandardConfig } from '@/api/report/indicatorStandardConfig'
import { ProdQualityDataAPI } from '@/api/report/prodQualityData/index'
import { getConfigByReportType } from '@/api/report/supplementConfig'
import * as RoleApi from '@/api/system/role/index'
import SupplementButton from '@/components/SupplementButton/index.vue'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'

import { ElButton, ElMessage } from 'element-plus'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { computed, nextTick, ref, watch } from 'vue'

const appStore = useAppStore()

// 空值或未填显示的值
const EMPTY_VALUE = '/'

// 标准值(空值或未填显示的值)
const CONFIG_EMPTY_VALUE = '/'

// 用户信息
const userStore = useUserStore()
const userId = computed(() => userStore.getUser.id)

// 当前选中的月份
const selectedMonth = ref(new Date())

// 获取当月天数
const daysInMonth = computed(() => {
  const year = selectedMonth.value.getFullYear()
  const month = selectedMonth.value.getMonth() + 1
  return new Date(year, month, 0).getDate()
})

// 添加接口类型定义
interface DailyData {
  date: string;
  factoryId?: number;
  inPh?: string;
  inTemp?: string;
  inCodcr?: string;
  inBod5?: string;
  inSs?: string;
  inNh3n?: string;
  inTn?: string;
  inTp?: string;
  outPh?: string;
  outTemp?: string;
  outCodcr?: string;
  outBod5?: string;
  outSs?: string;
  outNh3n?: string;
  outTn?: string;
  outTp?: string;
  inFlowWaterVolume?: string;
  dailyTreatmentVol?: string;
  reporterId?: number;
  reviewStatus?: number;
}

// 日期区间选择
const dateRange = ref<[Date, Date] | null>(null)
const startDay = ref<number>(0)
const endDay = ref<number>(0)

// 可选的结束日期
const availableEndDays = computed<number[]>(() => {
  const days: number[] = []
  const minDay = startDay.value || 1
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(today.getDate() - 1)
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()

  // 如果是当前月份，最大可选日期为昨天
  if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
    const maxDay = yesterday.getDate()
    for (let i = minDay; i <= maxDay; i++) {
      days.push(i)
    }
  } else {
    // 如果是历史月份，最大可选日期为该月的最大天数
    const maxDay = daysInMonth.value
    for (let i = minDay; i <= maxDay; i++) {
      days.push(i)
    }
  }

  return days
})

// 可选的开始日期
const availableStartDays = computed<number[]>(() => {
  const days: number[] = []
  const maxDay = endDay.value || (selectedMonth.value.getFullYear() === new Date().getFullYear() &&
    selectedMonth.value.getMonth() === new Date().getMonth() ?
    new Date().getDate() : daysInMonth.value)
  for (let i = 1; i <= maxDay; i++) {
    days.push(i)
  }
  return days
})

// 处理日期范围变化
const handleDayRangeChange = () => {
  if (startDay.value && endDay.value) {
    const year = selectedMonth.value.getFullYear()
    const month = selectedMonth.value.getMonth()
    dateRange.value = [
      new Date(year, month, startDay.value),
      new Date(year, month, endDay.value)
    ]
  } else {
    dateRange.value = null
  }
}

// 修改 handleMonthChange 函数
const handleMonthChange = () => {
  const today = new Date()
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()

  // 如果是当前月份，结束日默认为昨天天
  if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
    startDay.value = 1
    endDay.value = today.getDate() - 1
  } else {
    // 如果是历史月份，结束日默认为该月的最后一天
    startDay.value = 1
    endDay.value = daysInMonth.value
  }

  dateRange.value = null
  loadData()
  nextTick(() => {
    const tableWrapper = document.querySelector('.el-table__body-wrapper') as HTMLElement
    if (tableWrapper) {
      tableWrapper.style.scrollBehavior = 'smooth'
      tableWrapper.scrollLeft = tableWrapper.scrollWidth
    }
  })
}

// 获取今天的日期
const today = computed(() => {
  const now = new Date()
  return now.getDate()
})

// 获取昨天的日期
const yesterday = computed(() => {
  const now = new Date()
  // 创建昨天的日期
  const yesterdayDate = new Date(now)
  yesterdayDate.setDate(now.getDate() - 1)
  return yesterdayDate
})

// 获取昨天的日期（天）
const yesterdayDay = computed(() => {
  return yesterday.value.getDate()
})

// 获取昨天所在的月份
const yesterdayMonth = computed(() => {
  return yesterday.value.getMonth()
})

// 获取昨天所在的年份
const yesterdayYear = computed(() => {
  return yesterday.value.getFullYear()
})

// 判断昨天是否在当前选择的月份中
const isYesterdayInSelectedMonth = computed(() => {
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()

  return yesterdayYear.value === selectedYear && yesterdayMonth.value === selectedMonthNum
})

// 判断是否显示昨天的列
const isYesterdayColumnVisible = computed(() => {
  return isYesterdayInSelectedMonth.value
})

// 修改 displayDays 计算属性
const displayDays = computed<number[]>(() => {
  const now = new Date()
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth()

  // 如果有日期区间选择，则只显示区间内的日期
  if (startDay.value && endDay.value) {
    const start = Math.min(startDay.value, endDay.value)
    const end = Math.max(startDay.value, endDay.value)
    const days = Array.from({ length: end - start + 1 }, (_, i) => start + i)

    // 如果包含昨天，将昨天移到最前面（仅当昨天在当前选择的月份中）
    if (isYesterdayInSelectedMonth.value) {
      const yesterdayIndex = days.indexOf(yesterdayDay.value)
      if (yesterdayIndex !== -1) {
        days.splice(yesterdayIndex, 1)
        days.unshift(yesterdayDay.value)
      }
    }

    return days
  }

  // 如果是当月，只显示到当天（不包括当天）
  if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
    // 获取到昨天为止的所有天数
    const days = Array.from({ length: now.getDate() - 1 }, (_, i) => i + 1)

    // 将昨天移到最前面
    const yesterdayIndex = days.indexOf(yesterdayDay.value)
    if (yesterdayIndex !== -1) {
      days.splice(yesterdayIndex, 1)
      days.unshift(yesterdayDay.value)
    }

    return days
  }

  // 如果是历史月份，显示整月
  return Array.from({ length: daysInMonth.value }, (_, i) => i + 1)
})

// 添加新的计算属性，过滤掉昨天
const filteredDisplayDays = computed(() => {
  return displayDays.value.filter(day => {
    // 如果昨天在当前选择的月份中，过滤掉昨天（因为昨天会单独显示在最前面）
    if (isYesterdayInSelectedMonth.value && day === yesterdayDay.value) {
      return false
    }
    // 过滤掉今天和之后的日期
    const now = new Date()
    const selectedYear = selectedMonth.value.getFullYear()
    const selectedMonthNum = selectedMonth.value.getMonth()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth()
    const currentDay = now.getDate()

    if (selectedYear === currentYear && selectedMonthNum === currentMonth && day >= currentDay) {
      return false
    }

    return true
  })
})

// 格式化日期
const formatDate = (date: Date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
}

// 表格数据结构
const tableData = ref([
  // 进水物理特性
  { group: '进水物理特性', groupFlag: '进水', name: 'pH', unit: '/', standard: '6-9', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '进水物理特性', groupFlag: '进水', name: '水温', unit: '℃', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },

  // 进水污染物浓度
  { group: '进水污染物浓度', groupFlag: '进水', name: 'CODcr', unit: 'mg/L', standard: '≤380', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '进水污染物浓度', groupFlag: '进水', name: 'BOD5', unit: 'mg/L', standard: '≤180', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '进水污染物浓度', groupFlag: '进水', name: 'SS(L)', unit: 'mg/L', standard: '≤280', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '进水污染物浓度', groupFlag: '进水', name: 'NH3-N', unit: 'mg/L', standard: '≤35', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '进水污染物浓度', groupFlag: '进水', name: 'TN', unit: 'mg/L', standard: '≤50', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '进水污染物浓度', groupFlag: '进水', name: 'TP', unit: 'mg/L', standard: '≤6', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },

  // 出水物理特性
  { group: '出水物理特性', groupFlag: '出水', name: 'pH', unit: '/', standard: '6-9', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '出水物理特性', groupFlag: '出水', name: '水温', unit: '℃', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },

  // 出水污染物浓度
  { group: '出水污染物浓度', groupFlag: '出水', name: 'CODcr', unit: 'mg/L', standard: '≤30', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '出水污染物浓度', groupFlag: '出水', name: 'BOD5', unit: 'mg/L', standard: '≤6', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '出水污染物浓度', groupFlag: '出水', name: 'SS(L)', unit: 'mg/L', standard: '≤10', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '出水污染物浓度', groupFlag: '出水', name: 'NH3-N', unit: 'mg/L', standard: '≤1.5', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '出水污染物浓度', groupFlag: '出水', name: 'TN', unit: 'mg/L', standard: '≤5', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '出水污染物浓度', groupFlag: '出水', name: 'TP', unit: 'mg/L', standard: '≤0.3', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },

  // 水量
  { group: '水量', groupFlag: '水量', name: '进水水量', unit: 'm³', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
  { group: '水量', groupFlag: '水量', name: '日处理量', unit: 'm³', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) }
])

// 在 script setup 中修改类型定义
interface IndicatorOption {
  label: string
  value: string
}

interface IndicatorGroup {
  label: string
  value: string
  options: IndicatorOption[]
}

// 选中的指标
const selectedIndicators = ref<string[]>([])

// 定义指标与分类的强绑定关系
const INDICATOR_GROUPS = {
  '进水物理特性': ['pH', '水温'],
  '进水污染物浓度': ['CODcr', 'BOD5', 'SS(L)', 'NH3-N', 'TN', 'TP'],
  '出水物理特性': ['pH', '水温'],
  '出水污染物浓度': ['CODcr', 'BOD5', 'SS(L)', 'NH3-N', 'TN', 'TP'],
  '水量': ['进水水量', '日处理量']
} as const

// 指标分组数据
const indicatorGroups = computed<IndicatorGroup[]>(() => {
  const groupOrder = ['进水物理特性', '进水污染物浓度', '出水物理特性', '出水污染物浓度', '水量']

  return groupOrder.map(group => ({
    label: group,
    value: group,
    options: INDICATOR_GROUPS[group].map(name => ({
      label: name,
      value: `${group}/${name}`
    }))
  }))
})

// 过滤后的表格数据
const filteredTableData = computed(() => {
  if (!selectedIndicators.value.length) {
    return tableData.value
  }

  // 解析选中的指标，按分组组织
  const selectedItems = new Map<string, Set<string>>()

  selectedIndicators.value.forEach(path => {
    const [group, name] = path.split('/')
    if (!selectedItems.has(group)) {
      selectedItems.set(group, new Set())
    }
    if (!name) {
      // 如果选择整个分组，添加该分组下的所有指标
      INDICATOR_GROUPS[group].forEach(indicatorName => {
        selectedItems.get(group)?.add(indicatorName)
      })
    } else {
      // 验证指标是否属于该分组
      if (INDICATOR_GROUPS[group].includes(name)) {
        selectedItems.get(group)?.add(name)
      }
    }
  })

  // 按原始顺序过滤数据，并确保指标在正确的分组中
  const filtered = tableData.value.filter(row => {
    return selectedItems.has(row.group) &&
      INDICATOR_GROUPS[row.group].includes(row.name) &&
      selectedItems.get(row.group)?.has(row.name)
  })

  // 确保每个分组的指标都连续排列
  const groupedData = []
  const groups = new Set(filtered.map(row => row.group))

  for (const group of groups) {
    const groupItems = filtered.filter(row => row.group === group)
    groupedData.push(...groupItems)
  }

  return groupedData
})

// 处理清空选择
const handleClearIndicators = () => {
  selectedIndicators.value = []
}

// 验证选择的指标是否有效
const validateIndicatorSelection = (path: string) => {
  const [group, name] = path.split('/')
  if (!name) return true // 选择整个分组是有效的
  return INDICATOR_GROUPS[group]?.includes(name) || false
}

// 处理指标选择变化
const handleIndicatorChange = (value: string[]) => {
  // 过滤掉无效的选择
  selectedIndicators.value = value.filter(validateIndicatorSelection)
}

// 判断单元格是否可编辑
const isCellEditable = (day: number, row: any) => {
  // 如果是报表管理员，所有单元格都可编辑
  if (isReportAdminRole.value) {
    return true
  }

  // BOD₅行始终可编辑，不受日期限制
  if (row && row.name === 'BOD5') {
    return true
  }

  const now = new Date()
  const selectedYear = selectedMonth.value.getFullYear()
  const selectedMonthNum = selectedMonth.value.getMonth()

  // 获取昨天的日期信息
  const yesterdayDate = new Date(now)
  yesterdayDate.setDate(now.getDate() - 1)
  const yesterdayDay = yesterdayDate.getDate()
  const yesterdayMonth = yesterdayDate.getMonth()
  const yesterdayYear = yesterdayDate.getFullYear()

  // 如果是当月
  if (selectedYear === now.getFullYear() && selectedMonthNum === now.getMonth()) {
    // 只有昨天的数据可编辑
    return day === yesterdayDay
  }

  // 如果是昨天所在的月份（处理跨月情况）
  if (selectedYear === yesterdayYear && selectedMonthNum === yesterdayMonth) {
    // 只有昨天的数据可编辑
    return day === yesterdayDay
  }

  // 如果是更早的历史月份，所有日期都不可编辑
  return false
}

// 获取数据库字段名
const getFieldName = (indicatorName: string, group: string): string => {
  // 处理进出水指标
  if (['pH', '水温', 'CODcr', 'BOD5', 'SS(L)', 'NH3-N', 'TN', 'TP'].includes(indicatorName)) {
    const prefix = group.includes('进水') ? 'in' : 'out'

    // 基础字段名映射（不包含前缀）
    const baseFieldMap: Record<string, string> = {
      'pH': 'Ph',
      '水温': 'Temp',
      'CODcr': 'Codcr',
      'BOD5': 'Bod5',
      'SS(L)': 'Ss',
      'NH3-N': 'Nh3n',
      'TN': 'Tn',
      'TP': 'Tp'
    }

    const baseField = baseFieldMap[indicatorName] || ''
    return prefix + baseField
  }

  // 处理水量指标
  const waterVolumeMap: Record<string, string> = {
    '进水水量': 'inFlowWaterVolume',
    '日处理量': 'dailyTreatmentVol',
  }

  return waterVolumeMap[indicatorName] || ''
}

// 添加审核状态
const reviewStatus = ref(0)  // 0: 待审核, 1: 已通过, 2: 已拒绝

// 水厂相关类型定义
interface Factory {
  id: number;
  name: string;
  type: string;
  region: string;
  isActive: boolean;
}

// 水厂列表和选中的水厂
const factoryList = ref<any[]>([])
const selectedFactory = computed(() => appStore.getCurrentStation?.id)


// 添加加载状态标记
const loading = ref(false)

// 添加修改标记
const hasChanges = ref(false)

// 添加修改记录
const modifiedData = ref<Record<string, any>>({})

const isReportAdminRole = ref(false)

/**
 * 检查当前用户是否拥有报表管理员的角色
 */
const checkIsHasReportAdminRole = async () => {
  try {
    // 改回传递包含 roleCode 的对象参数
    const data = await RoleApi.checkIsHasRole({ roleCode: "report_admin" })
    isReportAdminRole.value = !!data
    // console.log('报表管理员权限状态:', isReportAdminRole.value)
  } catch (error) {
    console.error('检查报表管理员权限失败:', error)
    isReportAdminRole.value = false
  }
}

// 递归找到第一个 level=3 的节点
function findFirstLevel3(tree: any[]): any | null {
  for (const node of tree) {
    if (node.level === 3) return node;
    if (node.children) {
      const found = findFirstLevel3(node.children);
      if (found) return found;
    }
  }
  return null;
}

// 生成用于分组展示的工厂树（只保留有 level=3 子节点的父节点和所有 level=3 节点）
const groupedFactoryList = computed(() => {
  function filterTree(tree: any[]): any[] {
    return tree
      .map(node => {
        let childrenLevel3 = [];
        if (node.children && node.children.length) {
          childrenLevel3 = node.children.filter(child => child.level === 3);
        }
        return {
          ...node,
          childrenLevel3
        };
      })
      .filter(node => (node.childrenLevel3 && node.childrenLevel3.length) || node.level === 3)
  }
  return filterTree(factoryList.value)
})

// const queryFactoryList = async () => {
//   try {
//     // 添加userId参数
//     let res = await FactoryApi.queryFactoryListByUser({ userId: userId.value });
//     if (res && res.data?.length > 0) {
//       factoryList.value = res.data; // 保持原始树结构
//       // 默认选中第一个 level=3
//       const first = findFirstLevel3(res.data);
//       if (!selectedFactory.value && first) {
//         selectedFactory.value = first.id;
//       }
//     }
//   } catch (error) {
//     console.error('获取水厂列表失败:', error);
//     ElMessage.error('获取水厂列表失败');
//   }
// }

// 获取标准值配置
const getStandardConfig = async () => {
  try {
    const res = await getIndicatorStandardConfig({
      factoryId: selectedFactory.value ?? null
    })
    if (Array.isArray(res)) {
      const newTableData = tableData.value.map(row => {
        let name = row.groupFlag + row.name
        const standard = res.find(item => {
          let config = item.configJson || {}
          if (!config.indicatorLabel) {
            console.log("指标缺少indicatorLabel", item)
            return false
          }
          return config.indicatorLabel === name
        })

        if (standard && standard.configJson) {
          const config = standard.configJson

          // 情况1: 有上下限
          if (config.lowerLimit !== undefined && config.upperLimit !== undefined) {
            row.standard = `${config.lowerLimit}-${config.upperLimit}`
          }
          // 情况2: 只有标准值且不为null
          else if (config.standard !== undefined && config.standard !== null) {
            row.standard = `≤${config.standard}`
          }
          // 情况3: 标准值为null或未定义
          else {
            row.standard = CONFIG_EMPTY_VALUE
          }
        } else {
          row.standard = CONFIG_EMPTY_VALUE
        }

        return row
      })

      // 强制刷新视图
      tableData.value = [...newTableData]
    }
  } catch (error) {
    console.error('获取标准值配置失败:', error)
    ElMessage.error('获取标准值配置失败')
  }
}

// 修改 loadData 函数
const loadData = async () => {
  if (!loading.value) {
    loading.value = true
  }

  try {
    // 根据月份 厂站ID 查询历史数据
    const searchParam = {
      factoryId: selectedFactory.value,
      month: formatDate(selectedMonth.value),
    }

    // 调用接口获取历史数据
    try {
      const historyData = await ProdQualityDataAPI.queryDataByFactoryIdAndDate(searchParam)

      if (historyData) {
        // 处理历史数据，更新表格
        updateTableData(historyData)

        // 在历史数据更新后，再获取标准值配置
        await getStandardConfig()

        // 获取统计数据
        await loadStatData()
      }
    } catch (error) {
      console.error('获取历史数据失败:', error)
      ElMessage.error('获取历史数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 监听水厂变化
watch(selectedFactory, (newValue) => {
  if (newValue) {
    hasChanges.value = false
    modifiedData.value = {}
    loadData()
  }
})

// 处理输入框失焦事件，格式化数字
const handleInputBlur = (e: FocusEvent, row: any, day: number) => {
  const input = e.target as HTMLInputElement
  const value = input.value

  // 如果输入为空，直接返回
  if (!value) return

  // 如果输入为"/"，直接保留
  if (value === EMPTY_VALUE) {

    return
  }

  // 尝试转换为数字
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    // 如果转换失败，清空输入
    row[`day${day}`] = ''
  }
}

// 修改 handleValueChange 函数
const handleValueChange = (row: any, day: number, value: string) => {

  // 调试日志
  // console.log('handleValueChange', row, day, value)
  // 如果输入为空，直接返回
  if (!value) {
    row[`day${day}`] = ''
    return
  }

  // 如果输入为"/"，直接保留
  if (value === EMPTY_VALUE) {
    // 标记有修改
    hasChanges.value = true

    // 记录修改的日期
    const key = `day${day}`
    if (!modifiedData.value[key]) {
      modifiedData.value[key] = {
        day,
        data: []
      }
    }
    return
  }

  // 验证输入是否为有效数字
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    // 如果输入无效，清空输入
    row[`day${day}`] = ''
    return
  }

  // 标记有修改
  hasChanges.value = true

  // 记录修改的日期
  const key = `day${day}`
  if (!modifiedData.value[key]) {
    modifiedData.value[key] = {
      day,
      data: []
    }
  }
}

// 修改 handleSubmit 函数
const handleSubmit = async () => {
  if (!hasChanges.value && !hasTempData.value) {
    ElMessage.warning('没有数据修改，无需提交')
    return
  }

  if (!factoryList.value) {
    ElMessage.warning('请先选择水厂,再进行数据提交')
    return
  }

  try {
    // 将修改的数据转换为按日期组织的数据结构
    const formattedData: Record<string, DailyData> = {}
    const submittedDays = new Set<string>()

    // 遍历所有修改过的日期
    Object.values(modifiedData.value).forEach((item: any) => {
      const date = `${selectedMonth.value.getFullYear()}-${String(selectedMonth.value.getMonth() + 1).padStart(2, '0')}-${String(item.day).padStart(2, '0')}`
      submittedDays.add(date)

      // 初始化该日期的数据对象
      formattedData[date] = {
        date,
        factoryId: selectedFactory.value,
        reporterId: userId.value,
        reviewStatus: 0  // 正常提交状态
      }

      // 获取该日期的所有指标数据
      tableData.value.forEach(row => {
        const field = getFieldName(row.name, row.group)
        if (field) {
          const cellValue = row[`day${item.day}`];
          // 如果提交的数据为/ 则改为 null
          const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;
          (formattedData[date] as any)[field] = submitValue;
        }
      })
    })

    // 收集暂存状态的数据
    tableData.value.forEach(row => {
      for (let day = 1; day <= 31; day++) {
        if (row[`day${day}Temp`]) {
          const date = `${selectedMonth.value.getFullYear()}-${String(selectedMonth.value.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`

          // 如果这个日期还没有被处理过
          if (!submittedDays.has(date)) {
            submittedDays.add(date)

            // 初始化该日期的数据对象
            formattedData[date] = {
              date,
              factoryId: selectedFactory.value,
              reporterId: userId.value,
              reviewStatus: 0  // 从暂存状态改为正常提交状态
            }
          }

          // 获取字段名称并添加数据
          const field = getFieldName(row.name, row.group)
          if (field) {
            const cellValue = row[`day${day}`];
            // 如果提交的数据为/ 则改为 null
            const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;
            (formattedData[date] as any)[field] = submitValue;
          }
        }
      }
    })

    // 准备提交的数据结构
    const submitData = Object.values(formattedData);
    console.log('submitData', submitData)
    // 调用提交接口
    const res = await ProdQualityDataAPI.saveOrUpdate(submitData)
    if (res && res.code === 0) {
      ElMessage.success('提交成功')
      loadData();
      // 提交成功后重新获取统计数据
      await loadStatData();
      // 提交成功后重置修改标记和修改记录
      hasChanges.value = false
      modifiedData.value = {}
    } else {
      ElMessage.error(res.msg || '提交失败')
    }
  } catch (error) {
    console.error('数据处理失败:', error)
  }
}

// 监听月份变化时重置修改标记和修改记录
watch(selectedMonth, () => {
  hasChanges.value = false
  modifiedData.value = {}
})

// 重置数据
const handleReset = () => {
  tableData.value = tableData.value.map(row => ({
    ...row,
    ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false }))
  }))
}

// 导出Excel
const handleExport = async () => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('水质数据')

  // 设置列
  const columns = [
    { header: '指标分类', key: 'group', width: 15 },
    { header: '指标名称', key: 'name', width: 15 },
    { header: '单位', key: 'unit', width: 10 },
    { header: '标准值', key: 'standard', width: 12 }
  ]

  // 获取排序后的日期列（当天的列不放在最前面）
  const sortedDays = [...displayDays.value].sort((a, b) => a - b)

  // 只添加筛选范围内的日期列
  const daysInRange = sortedDays.filter(day => {
    if (startDay.value && endDay.value) {
      return day >= startDay.value && day <= endDay.value
    }
    return true
  })

  // 添加日期列
  daysInRange.forEach(day => {
    columns.push({
      header: `${day}日`,
      key: `day${day}`,
      width: 8
    })
  })

  // 添加平均值列
  columns.push({
    header: '平均值',
    key: 'average',
    width: 10
  })

  // 添加最大值列
  columns.push({
    header: '最大值',
    key: 'max',
    width: 10
  })

  // 添加最小值列
  columns.push({
    header: '最小值',
    key: 'min',
    width: 10
  })

  // 添加累计值列
  columns.push({
    header: '累计值',
    key: 'total',
    width: 10
  })

  worksheet.columns = columns

  // 设置表头样式
  const headerRow = worksheet.getRow(1)
  headerRow.font = { bold: true }
  headerRow.alignment = { vertical: 'middle', horizontal: 'center' }
  headerRow.height = 25

  // 添加数据
  let currentGroup = ''
  let groupStartRow = 3

  // 使用筛选后的数据
  filteredTableData.value.forEach((row, index) => {
    const rowData = {
      group: row.group,
      name: row.name,
      unit: row.unit,
      standard: row.standard
    }

    // 添加日期数据，确保每个单元格都有值（即使是空值）
    daysInRange.forEach(day => {

      const cellValue = row[`day${day}`] // 获取单元格的原始值
      // 检查值是否为 "" 或 "0" (formatNumber会将0转为"")
      rowData[`day${day}`] = (cellValue == null) ? '' : cellValue
    })
    // 添加平均值
    rowData['average'] = calculateAverage(row)
    // 添加最大值
    rowData['max'] = calculateMax(row)
    // 添加最小值
    rowData['min'] = calculateMin(row)
    // 添加累计值
    rowData['total'] = calculateTotal(row)

    const excelRowIndex = index + 2 // Excel行号从1开始，第1行是表头

    // 添加数据行
    const excelRow = worksheet.addRow(rowData)

    // 为每个单元格设置样式
    excelRow.eachCell((cell) => {
      cell.alignment = { vertical: 'middle', horizontal: 'center' }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    })

    // 处理分组合并
    if (row.group !== currentGroup) {
      // 如果不是第一个分组，合并上一个分组的单元格
      if (currentGroup !== '' && groupStartRow < excelRowIndex) {
        worksheet.mergeCells(groupStartRow, 1, excelRowIndex - 1, 1)

        // 设置合并后单元格的样式
        const mergedCell = worksheet.getCell(groupStartRow, 1)
        mergedCell.alignment = { vertical: 'middle', horizontal: 'center' }
        mergedCell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      }
      currentGroup = row.group
      groupStartRow = excelRowIndex
    }
  })

  // 合并最后一个分组的单元格
  if (groupStartRow < filteredTableData.value.length + 2) {
    worksheet.mergeCells(groupStartRow, 1, filteredTableData.value.length + 1, 1)

    // 设置最后一个合并单元格的样式
    const lastMergedCell = worksheet.getCell(groupStartRow, 1)
    lastMergedCell.alignment = { vertical: 'middle', horizontal: 'center' }
    lastMergedCell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
  }

  // 设置所有单元格的样式
  worksheet.eachRow((row, rowNumber) => {
    row.height = 20
    row.eachCell((cell) => {
      cell.alignment = { vertical: 'middle', horizontal: 'center' }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    })
  })

  // 生成文件名（包含日期范围信息）
  const startDayStr = startDay.value ? `-${startDay.value}日` : ''
  const endDayStr = endDay.value ? `至${endDay.value}日` : ''
  const fileName = `水质数据_${formatDate(selectedMonth.value)}${startDayStr}${endDayStr}.xlsx`

  // 生成并下载文件
  const buffer = await workbook.xlsx.writeBuffer()
  const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const blob = new Blob([buffer], { type: fileType })
  saveAs(blob, fileName)
}

// 判断是否是分组中的第一行
const isFirstInGroup = (row: any, index: number) => {
  return index === 0 || filteredTableData.value[index - 1].group !== row.group
}

// 处理单元格合并
const handleSpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  if (columnIndex === 0) { // 只处理第一列（指标分类）
    const currentGroup = row.group
    let rowspan = 0
    let colspan = 1

    // 向下查找相同分组的行数
    for (let i = rowIndex; i < filteredTableData.value.length; i++) {
      if (filteredTableData.value[i].group === currentGroup) {
        rowspan++
      } else {
        break
      }
    }

    // 如果是分组的第一行，返回合并信息
    if (isFirstInGroup(row, rowIndex)) {
      return {
        rowspan,
        colspan
      }
    } else {
      // 不是第一行则隐藏单元格
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }

  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}

// 添加数字格式化函数
const formatNumber = (value: number | string | undefined): string => {
  if (value === undefined || value === null || value === '') return EMPTY_VALUE
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return EMPTY_VALUE

  // 如果是0，显示为 "/"
  if (num === 0) return EMPTY_VALUE

  // 其他数字都保留两位小数
  return num.toFixed(2)
}

// 刷新表格滚动
const refreshTableScroll = () => {
  const tableWrapper = document.querySelector('.el-table__body-wrapper') as HTMLElement
  if (tableWrapper) {
    tableWrapper.style.scrollBehavior = 'smooth'
    tableWrapper.scrollLeft = 0
  }
}

// 修改 updateTableData 函数
const updateTableData = (historyData: any[]) => {
  // 清空现有数据
  tableData.value = [
    { group: '进水物理特性', groupFlag: '进水', name: 'pH', unit: '/', standard: '6-9', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水物理特性', groupFlag: '进水', name: '水温', unit: '℃', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水污染物浓度', groupFlag: '进水', name: 'CODcr', unit: 'mg/L', standard: '≤380', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水污染物浓度', groupFlag: '进水', name: 'BOD5', unit: 'mg/L', standard: '≤180', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水污染物浓度', groupFlag: '进水', name: 'SS(L)', unit: 'mg/L', standard: '≤280', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水污染物浓度', groupFlag: '进水', name: 'NH3-N', unit: 'mg/L', standard: '≤35', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水污染物浓度', groupFlag: '进水', name: 'TN', unit: 'mg/L', standard: '≤50', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '进水污染物浓度', groupFlag: '进水', name: 'TP', unit: 'mg/L', standard: '≤6', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水物理特性', groupFlag: '出水', name: 'pH', unit: '/', standard: '6-9', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水物理特性', groupFlag: '出水', name: '水温', unit: '℃', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水污染物浓度', groupFlag: '出水', name: 'CODcr', unit: 'mg/L', standard: '≤30', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水污染物浓度', groupFlag: '出水', name: 'BOD5', unit: 'mg/L', standard: '≤6', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水污染物浓度', groupFlag: '出水', name: 'SS(L)', unit: 'mg/L', standard: '≤10', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水污染物浓度', groupFlag: '出水', name: 'NH3-N', unit: 'mg/L', standard: '≤1.5', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水污染物浓度', groupFlag: '出水', name: 'TN', unit: 'mg/L', standard: '≤5', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '出水污染物浓度', groupFlag: '出水', name: 'TP', unit: 'mg/L', standard: '≤0.3', ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '水量', groupFlag: '水量', name: '进水水量', unit: 'm³', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) },
    { group: '水量', groupFlag: '水量', name: '日处理量', unit: 'm³', standard: CONFIG_EMPTY_VALUE, ...Array.from({ length: 31 }, (_, i) => ({ [`day${i + 1}`]: '', [`day${i + 1}Temp`]: false })) }
  ]

  // 重置暂存状态
  hasTempData.value = false

  // 更新历史数据
  historyData.forEach(item => {
    const date = new Date(item.date)
    const day = date.getDate()

    // 检查是否为暂存状态 (reviewStatus = 5)
    const isTempData = item.reviewStatus === 5
    if (isTempData) {
      hasTempData.value = true
    }

    // 更新进水物理特性
    const inPhRow = tableData.value.find(row => row.group === '进水物理特性' && row.name === 'pH')
    if (inPhRow) {
      const formattedValue = formatNumber(item.inPh);
      inPhRow[`day${day}`] = item.inPh === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inPhRow[`day${day}Temp`] = true;
    }

    const inTempRow = tableData.value.find(row => row.group === '进水物理特性' && row.name === '水温')
    if (inTempRow) {
      const formattedValue = formatNumber(item.inTemp);
      inTempRow[`day${day}`] = item.inTemp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inTempRow[`day${day}Temp`] = true;
    }

    // 更新进水污染物浓度
    const inCodcrRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'CODcr')
    if (inCodcrRow) {
      const formattedValue = formatNumber(item.inCodcr);
      inCodcrRow[`day${day}`] = item.inCodcr === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inCodcrRow[`day${day}Temp`] = true;
    }

    const inBod5Row = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'BOD5')
    if (inBod5Row) {
      const formattedValue = formatNumber(item.inBod5);
      inBod5Row[`day${day}`] = item.inBod5 === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inBod5Row[`day${day}Temp`] = true;
    }

    const inSsRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'SS(L)')
    if (inSsRow) {
      const formattedValue = formatNumber(item.inSs);
      inSsRow[`day${day}`] = item.inSs === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inSsRow[`day${day}Temp`] = true;
    }

    const inNh3nRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'NH3-N')
    if (inNh3nRow) {
      const formattedValue = formatNumber(item.inNh3n);
      inNh3nRow[`day${day}`] = item.inNh3n === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inNh3nRow[`day${day}Temp`] = true;
    }

    const inTnRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'TN')
    if (inTnRow) {
      const formattedValue = formatNumber(item.inTn);
      inTnRow[`day${day}`] = item.inTn === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inTnRow[`day${day}Temp`] = true;
    }

    const inTpRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'TP')
    if (inTpRow) {
      const formattedValue = formatNumber(item.inTp);
      inTpRow[`day${day}`] = item.inTp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inTpRow[`day${day}Temp`] = true;
    }

    // 更新出水物理特性
    const outPhRow = tableData.value.find(row => row.group === '出水物理特性' && row.name === 'pH')
    if (outPhRow) {
      const formattedValue = formatNumber(item.outPh);
      outPhRow[`day${day}`] = item.outPh === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outPhRow[`day${day}Temp`] = true;
    }
    const outTempRow = tableData.value.find(row => row.group === '出水物理特性' && row.name === '水温')
    if (outTempRow) {
      const formattedValue = formatNumber(item.outTemp);
      outTempRow[`day${day}`] = item.outTemp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outTempRow[`day${day}Temp`] = true;
    }

    // 更新出水污染物浓度
    const outCodcrRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'CODcr')
    if (outCodcrRow) {
      const formattedValue = formatNumber(item.outCodcr);
      outCodcrRow[`day${day}`] = item.outCodcr === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outCodcrRow[`day${day}Temp`] = true;
    }

    const outBod5Row = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'BOD5')
    if (outBod5Row) {
      const formattedValue = formatNumber(item.outBod5);
      outBod5Row[`day${day}`] = item.outBod5 === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outBod5Row[`day${day}Temp`] = true;
    }

    const outSsRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'SS(L)')
    if (outSsRow) {
      const formattedValue = formatNumber(item.outSs);
      outSsRow[`day${day}`] = item.outSs === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outSsRow[`day${day}Temp`] = true;
    }

    const outNh3nRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'NH3-N')
    if (outNh3nRow) {
      const formattedValue = formatNumber(item.outNh3n);
      outNh3nRow[`day${day}`] = item.outNh3n === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outNh3nRow[`day${day}Temp`] = true;
    }

    const outTnRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'TN')
    if (outTnRow) {
      const formattedValue = formatNumber(item.outTn);
      outTnRow[`day${day}`] = item.outTn === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outTnRow[`day${day}Temp`] = true;
    }

    const outTpRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'TP')
    if (outTpRow) {
      const formattedValue = formatNumber(item.outTp);
      outTpRow[`day${day}`] = item.outTp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outTpRow[`day${day}Temp`] = true;
    }

    // 更新水量
    const inFlowWaterVolumeRow = tableData.value.find(row => row.group === '水量' && row.name === '进水水量')
    if (inFlowWaterVolumeRow) {
      const formattedValue = formatNumber(item.inFlowWaterVolume);
      inFlowWaterVolumeRow[`day${day}`] = item.inFlowWaterVolume === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inFlowWaterVolumeRow[`day${day}Temp`] = true;
    }
    const dailyTreatmentRow = tableData.value.find(row => row.group === '水量' && row.name === '日处理量')
    if (dailyTreatmentRow) {
      const formattedValue = formatNumber(item.dailyTreatmentVol);
      dailyTreatmentRow[`day${day}`] = item.dailyTreatmentVol === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) dailyTreatmentRow[`day${day}Temp`] = true;
    }
  })
}

// 修改onMounted为异步函数
onMounted(async () => {
  loading.value = true
  try {
    // 1. 检查是否有报表管理员权限（同步，无需 await）
    checkIsHasReportAdminRole()
    //
    // // 2. 加载水厂列表
    // await queryFactoryList()

    // 3. 设置默认起始日和结束日
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(today.getDate() - 1)

    // 如果今天是月初1号，自动切换到上个月
    if (today.getDate() === 1) {
      // 设置为上个月
      selectedMonth.value = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      startDay.value = 1
      endDay.value = new Date(today.getFullYear(), today.getMonth(), 0).getDate() // 上个月的最后一天
      await loadData()
    } else {
      const selectedYear = selectedMonth.value.getFullYear()
      const selectedMonthNum = selectedMonth.value.getMonth()
      const currentYear = today.getFullYear()
      const currentMonth = today.getMonth()

      startDay.value = 1
      if (selectedYear === currentYear && selectedMonthNum === currentMonth) {
        endDay.value = yesterday.getDate() // 修改为昨天的日期
      } else {
        endDay.value = daysInMonth.value
      }
      await loadData()
    }


    // 4. 加载数据
    // 默认选择水厂之后 会调用一次loadData()方法
    // 在此处就不多于调用
    // await loadData()

  } catch (error) {
    console.error('页面初始化失败:', error)
    ElMessage.error('页面初始化失败')
  } finally {
    loading.value = false
  }
})


interface DateRange {
  label: string;
  value: string;
  start: number;
  end: number;
}

// 日期范围选择
const selectedRange = ref<string>('')

// 生成日期范围选项
const dateRangeOptions = computed<DateRange[]>(() => {
  const options: DateRange[] = []
  const totalDays = daysInMonth.value

  // 生成每5天一组的选项
  for (let i = 1; i <= totalDays; i += 5) {
    const end = Math.min(i + 4, totalDays)
    options.push({
      label: `${i}-${end}日`,
      value: `${i}-${end}`,
      start: i,
      end: end
    })
  }

  // 添加整月选项
  options.unshift({
    label: '整月',
    value: 'all',
    start: 1,
    end: totalDays
  })

  return options
})

// 处理范围选择变化
const handleRangeChange = (value: string) => {
  if (!value) {
    startDay.value = 0
    endDay.value = 0
    dateRange.value = null
    return
  }

  const option = dateRangeOptions.value.find(opt => opt.value === value)
  if (option) {
    startDay.value = option.start
    endDay.value = option.end
    const year = selectedMonth.value.getFullYear()
    const month = selectedMonth.value.getMonth()
    dateRange.value = [
      new Date(year, month, option.start),
      new Date(year, month, option.end)
    ]
  }
}

// 处理单元格编辑
const handleCellEdit = (day: number, field: string, value: string) => {
  // const rowIndex = tableData.value.findIndex(row => getFieldName(row.name, row.group) === field);
  // if (rowIndex > -1) {
  //   const row = tableData.value[rowIndex];
  //   if (!isCellEditable(day, row)) {
  //     ElMessage.warning('该日期不可编辑');
  //     return;
  //   }
  //   row[`day${day}`] = value;
  // }
}

// 添加统计数据存储
const statData = ref<any>(null)

// 加载统计数据
const loadStatData = async () => {
  if (!selectedFactory.value) {
    return
  }

  try {
    // 获取当前选择月份的起始日期和结束日期
    const year = selectedMonth.value.getFullYear()
    const month = selectedMonth.value.getMonth()

    // 月份的第一天
    const startDate = new Date(year, month, 1)
    // 月份的最后一天
    const endDate = new Date(year, month + 1, 0)

    // 如果有选择日期范围，则使用选择的范围
    const start = startDay.value ? new Date(year, month, startDay.value) : startDate
    const end = endDay.value ? new Date(year, month, endDay.value) : endDate

    // 格式化日期为 yyyy-MM-dd
    const formatStartDate = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-${String(start.getDate()).padStart(2, '0')}`
    const formatEndDate = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}-${String(end.getDate()).padStart(2, '0')}`

    const params = {
      factoryId: selectedFactory.value,
      startDate: formatStartDate,
      endDate: formatEndDate
    }
    
    try {
      const result = await ProdQualityDataAPI.getQualityStat(params)

      // 处理不同的返回格式
      let data = null
      if (Array.isArray(result) && result.length > 0) {
        // 直接返回数组的情况
        data = result[0]
      } else if (result && result.code === 0 && result.data && result.data.length > 0) {
        // 标准响应格式的情况
        data = result.data[0]
      } else if (result && typeof result === 'object' && !Array.isArray(result)) {
        // 直接返回对象的情况
        data = result
      }

      if (data) {
        statData.value = data
      } else {
        statData.value = null
      }
    } catch (apiError) {
      console.error('调用统计数据接口失败:', apiError)
      statData.value = null
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    statData.value = null
  }
}



// 监听日期范围变化时，重新加载统计数据
watch([startDay, endDay], async () => {
  if (startDay.value && endDay.value) {
    await loadStatData()
  }
})

// 修改计算平均值的方法
const calculateAverage = (row: any) => {
  // 如果有统计数据，直接使用后端计算的结果
  if (statData.value) {
    const fieldName = getFieldName(row.name, row.group)

    if (fieldName && statData.value[fieldName]) {
      if (statData.value[fieldName].avg !== undefined && statData.value[fieldName].avg !== null) {
        return formatNumber(statData.value[fieldName].avg)
      }
    }
  }

  return EMPTY_VALUE
}

// 修改计算累计值的方法
const calculateTotal = (row: any) => {
  // 如果有统计数据，直接使用后端计算的结果
  if (statData.value) {
    const fieldName = getFieldName(row.name, row.group)
    if (fieldName && statData.value[fieldName] && statData.value[fieldName].total !== undefined && statData.value[fieldName].total !== null) {
      return formatNumber(statData.value[fieldName].total)
    }
  }

  // 只有水量相关指标才显示累计值，其他指标显示为"/"
  if (row.name !== '进水水量' && row.name !== '日处理量') {
    return EMPTY_VALUE
  }

  return EMPTY_VALUE
}

// 修改计算最大值的方法
const calculateMax = (row: any) => {
  // 如果有统计数据，直接使用后端计算的结果
  if (statData.value) {
    const fieldName = getFieldName(row.name, row.group)
    if (fieldName && statData.value[fieldName] && statData.value[fieldName].max !== undefined && statData.value[fieldName].max !== null) {
      return formatNumber(statData.value[fieldName].max)
    }
  }

  return EMPTY_VALUE
}

// 修改计算最小值的方法
const calculateMin = (row: any) => {
  // 如果有统计数据，直接使用后端计算的结果
  if (statData.value) {
    const fieldName = getFieldName(row.name, row.group)
    if (fieldName && statData.value[fieldName] && statData.value[fieldName].min !== undefined && statData.value[fieldName].min !== null) {
      return formatNumber(statData.value[fieldName].min)
    }
  }

  return EMPTY_VALUE
}

// 补录功能相关
const additionalRecordingVisible = ref(false)
const additionalLoading = ref(false)
const additionalDateRange = ref<[Date, Date] | null>(null)
const additionalDisplayDays = ref<Date[]>([])
const additionalTableRef = ref(null)

// 禁用当前日期及之后的日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now() - 86400000 // 今天及之后的日期不可选
}

// 格式化补录日期显示
const formatAdditionalDate = (date: Date) => {
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

// 获取补录日期的key
const getAdditionalDayKey = (date: Date) => {
  return date.getDate()
}

// 获取当前选中水厂的名称
const currentFactoryName = computed(() => {
  if (!selectedFactory.value) return '';

  // 在工厂列表中查找匹配的水厂
  for (const group of factoryList.value) {
    // 检查当前节点
    if (group.id === selectedFactory.value) {
      return group.name;
    }

    // 检查子节点
    if (group.childrenLevel3 && group.childrenLevel3.length) {
      const found = group.childrenLevel3.find(item => item.id === selectedFactory.value);
      if (found) return found.name;
    }

    // 检查children
    if (group.children && group.children.length) {
      const found = findFactoryById(group.children, selectedFactory.value);
      if (found) return found.name;
    }
  }

  return '';
});

// 递归查找水厂
const findFactoryById = (factories: any[], id: number): any => {
  for (const factory of factories) {
    if (factory.id === id) {
      return factory;
    }

    if (factory.children && factory.children.length) {
      const found = findFactoryById(factory.children, id);
      if (found) return found;
    }
  }

  return null;
};

// 显示补录弹窗
const showAdditionalRecordingDialog = () => {
  if (!selectedFactory.value) {
    ElMessage.warning('请先选择水厂')
    return
  }

  additionalRecordingVisible.value = true
  additionalDateRange.value = null
  additionalDisplayDays.value = []

  // 设置默认时间范围为上个月25日至上个月月底
  const today = new Date()
  const currentYear = today.getFullYear()
  const currentMonth = today.getMonth()

  // 计算上个月的年和月
  const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1
  const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear

  // 上个月25日
  const startDate = new Date(lastMonthYear, lastMonth, 25)

  // 上个月最后一天（通过设置下个月的第0天来获取）
  const endDate = new Date(currentYear, currentMonth, 0)

  additionalDateRange.value = [startDate, endDate]
  handleAdditionalDateRangeChange()
}
watch(additionalRecordingVisible, (newValue) => {
  if (!newValue) { // 当对话框关闭时
    // 刷新主页面数据
    loadData()

    // 额外确保表格布局正确刷新
    nextTick(() => {
      refreshTableScroll()
      // 额外的延迟刷新，确保数据完全渲染
      setTimeout(refreshTableScroll, 300)
    })
  }
})

// 处理补录日期范围变化
const handleAdditionalDateRangeChange = async () => {
  if (!additionalDateRange.value) {
    additionalDisplayDays.value = []
    return
  }

  const [start, end] = additionalDateRange.value
  const days: Date[] = []

  // 生成日期范围内的所有日期
  const currentDate = new Date(start)
  while (currentDate <= end) {
    days.push(new Date(currentDate))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  additionalDisplayDays.value = days

  // 加载补录日期范围内的历史数据
  additionalLoading.value = true
  try {
    // 根据接口参数要求，使用月份格式调用查询接口
    // 由于日期范围可能跨月，需要获取每个月的数据并合并

    // 获取起始日期所在月份
    const startMonth = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}`

    // 获取结束日期所在月份
    const endMonth = `${end.getFullYear()}-${String(end.getMonth() + 1).padStart(2, '0')}`

    let allHistoryData: any[] = []

    // 如果起始月份和结束月份相同，只需查询一次
    if (startMonth === endMonth) {
      const searchParam = {
        factoryId: selectedFactory.value,
        month: startMonth
      }

      const historyData = await ProdQualityDataAPI.queryDataByFactoryIdAndDate(searchParam)
      if (historyData) {
        allHistoryData = historyData
      }
    } else {
      // 如果跨月，需要查询两个月的数据
      // 查询起始月
      const startMonthParam = {
        factoryId: selectedFactory.value,
        month: startMonth
      }

      const startMonthData = await ProdQualityDataAPI.queryDataByFactoryIdAndDate(startMonthParam)
      if (startMonthData) {
        allHistoryData = [...allHistoryData, ...startMonthData]
      }

      // 查询结束月
      const endMonthParam = {
        factoryId: selectedFactory.value,
        month: endMonth
      }

      const endMonthData = await ProdQualityDataAPI.queryDataByFactoryIdAndDate(endMonthParam)
      if (endMonthData) {
        allHistoryData = [...allHistoryData, ...endMonthData]
      }
    }

    // 清空现有数据，防止不同日期的数据混淆
    tableData.value.forEach(row => {
      additionalDisplayDays.value.forEach(day => {
        const dayKey = `day${getAdditionalDayKey(day)}`
        row[dayKey] = ''
      })
    })

    // 处理历史数据，更新表格
    updateAdditionalTableData(allHistoryData)

  } catch (error) {
    console.error('获取补录历史数据失败:', error)
    ElMessage.error('获取补录历史数据失败')
  } finally {
    additionalLoading.value = false
  }

  // 刷新表格布局
  nextTick(() => {
    if (additionalTableRef.value) {
      additionalTableRef.value.doLayout()
    }
  })
}

// 更新补录表格数据
const updateAdditionalTableData = (historyData: any[]) => {
  // 更新历史数据
  historyData.forEach(item => {
    const date = new Date(item.date)
    const day = getAdditionalDayKey(date)

    // 检查是否为暂存状态 (reviewStatus = 5)
    const isTempData = item.reviewStatus === 5
    if (isTempData) {
      hasTempData.value = true
    }

    // 更新进水物理特性
    const inPhRow = tableData.value.find(row => row.group === '进水物理特性' && row.name === 'pH')
    if (inPhRow) {
      const formattedValue = formatNumber(item.inPh);
      inPhRow[`day${day}`] = item.inPh === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inPhRow[`day${day}Temp`] = true;
    }

    const inTempRow = tableData.value.find(row => row.group === '进水物理特性' && row.name === '水温')
    if (inTempRow) {
      const formattedValue = formatNumber(item.inTemp);
      inTempRow[`day${day}`] = item.inTemp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inTempRow[`day${day}Temp`] = true;
    }

    // 更新进水污染物浓度
    const inCodcrRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'CODcr')
    if (inCodcrRow) {
      const formattedValue = formatNumber(item.inCodcr);
      inCodcrRow[`day${day}`] = item.inCodcr === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inCodcrRow[`day${day}Temp`] = true;
    }

    const inBod5Row = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'BOD5')
    if (inBod5Row) {
      const formattedValue = formatNumber(item.inBod5);
      inBod5Row[`day${day}`] = item.inBod5 === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inBod5Row[`day${day}Temp`] = true;
    }

    const inSsRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'SS(L)')
    if (inSsRow) {
      const formattedValue = formatNumber(item.inSs);
      inSsRow[`day${day}`] = item.inSs === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inSsRow[`day${day}Temp`] = true;
    }

    const inNh3nRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'NH3-N')
    if (inNh3nRow) {
      const formattedValue = formatNumber(item.inNh3n);
      inNh3nRow[`day${day}`] = item.inNh3n === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inNh3nRow[`day${day}Temp`] = true;
    }

    const inTnRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'TN')
    if (inTnRow) {
      const formattedValue = formatNumber(item.inTn);
      inTnRow[`day${day}`] = item.inTn === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inTnRow[`day${day}Temp`] = true;
    }

    const inTpRow = tableData.value.find(row => row.group === '进水污染物浓度' && row.name === 'TP')
    if (inTpRow) {
      const formattedValue = formatNumber(item.inTp);
      inTpRow[`day${day}`] = item.inTp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) inTpRow[`day${day}Temp`] = true;
    }

    // 更新出水物理特性
    const outPhRow = tableData.value.find(row => row.group === '出水物理特性' && row.name === 'pH')
    if (outPhRow) {
      const formattedValue = formatNumber(item.outPh);
      outPhRow[`day${day}`] = item.outPh === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outPhRow[`day${day}Temp`] = true;
    }

    const outTempRow = tableData.value.find(row => row.group === '出水物理特性' && row.name === '水温')
    if (outTempRow) {
      const formattedValue = formatNumber(item.outTemp);
      outTempRow[`day${day}`] = item.outTemp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outTempRow[`day${day}Temp`] = true;
    }

    // 更新出水污染物浓度
    const outCodcrRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'CODcr')
    if (outCodcrRow) {
      const formattedValue = formatNumber(item.outCodcr);
      outCodcrRow[`day${day}`] = item.outCodcr === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outCodcrRow[`day${day}Temp`] = true;
    }

    const outBod5Row = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'BOD5')
    if (outBod5Row) {
      const formattedValue = formatNumber(item.outBod5);
      outBod5Row[`day${day}`] = item.outBod5 === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outBod5Row[`day${day}Temp`] = true;
    }

    const outSsRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'SS(L)')
    if (outSsRow) {
      const formattedValue = formatNumber(item.outSs);
      outSsRow[`day${day}`] = item.outSs === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outSsRow[`day${day}Temp`] = true;
    }

    const outNh3nRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'NH3-N')
    if (outNh3nRow) {
      const formattedValue = formatNumber(item.outNh3n);
      outNh3nRow[`day${day}`] = item.outNh3n === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outNh3nRow[`day${day}Temp`] = true;
    }

    const outTnRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'TN')
    if (outTnRow) {
      const formattedValue = formatNumber(item.outTn);
      outTnRow[`day${day}`] = item.outTn === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outTnRow[`day${day}Temp`] = true;
    }

    const outTpRow = tableData.value.find(row => row.group === '出水污染物浓度' && row.name === 'TP')
    if (outTpRow) {
      const formattedValue = formatNumber(item.outTp);
      outTpRow[`day${day}`] = item.outTp === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) outTpRow[`day${day}Temp`] = true;
    }

    // 更新水量
    const dailyInTreatmentRow = tableData.value.find(row => row.group === '水量' && row.name === '进水水量')
    if (dailyInTreatmentRow) {
      const formattedValue = formatNumber(item.inFlowWaterVolume);
      dailyInTreatmentRow[`day${day}`] = item.inFlowWaterVolume === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) dailyInTreatmentRow[`day${day}Temp`] = true;
    }

    const dailyTreatmentRow = tableData.value.find(row => row.group === '水量' && row.name === '日处理量')
    if (dailyTreatmentRow) {
      const formattedValue = formatNumber(item.dailyTreatmentVol);
      dailyTreatmentRow[`day${day}`] = item.dailyTreatmentVol === null ? EMPTY_VALUE : formattedValue;
      if (isTempData) dailyTreatmentRow[`day${day}Temp`] = true;
    }
  })

  // 强制刷新视图
  tableData.value = [...tableData.value]
}

// 处理补录提交
const handleAdditionalSubmit = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('没有数据修改，无需提交')
    return
  }

  try {
    // 将修改的数据转换为按日期组织的数据结构
    const formattedData: Record<string, DailyData> = {}

    // 遍历所有修改过的日期
    Object.values(modifiedData.value).forEach((item: any) => {
      // 找到对应的日期对象
      const dayObj = additionalDisplayDays.value.find(date => date.getDate() === item.day);
      if (!dayObj) return;

      // 使用补录日期范围中的实际日期构建日期字符串
      const date = `${dayObj.getFullYear()}-${String(dayObj.getMonth() + 1).padStart(2, '0')}-${String(dayObj.getDate()).padStart(2, '0')}`

      // 初始化该日期的数据对象
      formattedData[date] = {
        date,
        factoryId: selectedFactory.value,
        reporterId: userId.value,
        reviewStatus: 0
      }

      // 获取该日期的所有指标数据
      tableData.value.forEach(row => {
        const field = getFieldName(row.name, row.group)
        if (field) {
          const cellValue = row[`day${item.day}`];
          // 如果提交的数据为/ 则改为 null
          const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;
          (formattedData[date] as any)[field] = submitValue;
        }
      })
    })

    // 准备提交的数据结构
    const submitData = Object.values(formattedData)

    // 调用补录接口
    additionalLoading.value = true
    const res = await ProdQualityDataAPI.additionalRecording(submitData)
    additionalLoading.value = false

    if (res && res.code === 0) {
      ElMessage.success('补录成功')
      additionalRecordingVisible.value = false
      loadData() // 刷新主页面数据
      // 重置修改标记和修改记录
      hasChanges.value = false
      modifiedData.value = {}
    } else {
      ElMessage.error(res.msg || '补录失败')
    }
  } catch (error) {
    additionalLoading.value = false
    console.error('数据处理失败:', error)
    ElMessage.error('补录失败')
  }
}

// 添加暂存状态标记
const hasTempData = ref(false)

// 处理暂存
const handleTemporarilySave = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('没有数据修改，无需暂存')
    return
  }

  if (!selectedFactory.value) {
    ElMessage.warning('请先选择水厂,再进行数据暂存')
    return
  }

  try {
    // 将修改的数据转换为按日期组织的数据结构
    const formattedData: Record<string, DailyData> = {}

    // 遍历所有修改过的日期
    Object.values(modifiedData.value).forEach((item: any) => {
      const date = `${selectedMonth.value.getFullYear()}-${String(selectedMonth.value.getMonth() + 1).padStart(2, '0')}-${String(item.day).padStart(2, '0')}`

      // 初始化该日期的数据对象
      formattedData[date] = {
        date,
        factoryId: selectedFactory.value,
        reporterId: userId.value,
        reviewStatus: 5  // 设置为暂存状态
      }

      // 获取该日期的所有指标数据
      tableData.value.forEach(row => {
        const field = getFieldName(row.name, row.group)
        if (field) {
          const cellValue = row[`day${item.day}`];
          // 如果提交的数据为/ 则改为 null
          const submitValue = cellValue === EMPTY_VALUE ? null : cellValue;
          (formattedData[date] as any)[field] = submitValue;
        }
      })
    })

    // 准备提交的数据结构
    const submitData = Object.values(formattedData);

    // 调用暂存接口
    const res = await ProdQualityDataAPI.saveTemporarily(submitData)
    if (res && res.code === 0) {
      ElMessage.success('暂存成功')
      loadData();
      // 暂存成功后重置修改标记和修改记录
      hasChanges.value = false
      modifiedData.value = {}
    } else {
      ElMessage.error(res.msg || '暂存失败')
    }
  } catch (error) {
    console.error('数据处理失败:', error)
    ElMessage.error('暂存失败')
  }
}

// 添加补录按钮显示控制相关变量
const showSupplementButton = ref(false)

// 添加检查补录配置的函数
const checkSupplementConfig = async () => {
  try {
    // 获取水质数据的补录配置
    const config = await getConfigByReportType('quality')
    if (!config) {
      showSupplementButton.value = false
      return
    }

    // 检查配置是否启用
    if (!config.enabled) {
      showSupplementButton.value = false
      return
    }

    // 检查当前时间是否在允许的操作时间范围内
    const now = new Date().getTime()
    if (config.operationStartTime && config.operationEndTime) {
      showSupplementButton.value = now >= config.operationStartTime && now <= config.operationEndTime
    } else {
      showSupplementButton.value = false
    }
  } catch (error) {
    console.error('获取补录配置失败:', error)
    showSupplementButton.value = false
  }
}


</script>

<style scoped>
.app-container {
  /* padding: 20px; */
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  flex: 0 0 auto;
}

.header-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-group {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-end;
}

.button-group {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
}

.control-item {
  width: 170px;
}

.control-item-large {
  width: 220px;
}

.date-picker {
  width: 140px;
}

.date-range-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.day-select {
  width: 90px;
}

.separator {
  color: var(--el-text-color-regular);
  padding: 0 2px;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table .cell) {
  padding: 0 4px;
}

:deep(.el-input--small) {
  line-height: 24px;
}

:deep(.el-input__wrapper) {
  padding: 0 4px;
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset !important;
}

:deep(.el-input__inner) {
  text-align: center;
  padding: 0;
  height: 24px;
  line-height: 24px;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
}

:deep(input[type="number"]::-webkit-inner-spin-button),
:deep(input[type="number"]::-webkit-outer-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}

:deep(input[type="number"]) {
  -moz-appearance: textfield;
}

:deep(.el-table__header) th {
  background-color: #bde7f9 !important;
}

:deep(.el-table__header) .el-table__cell {
  text-align: center;
}

:deep(.el-table__fixed-left .el-table__cell) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed-left) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed-left::before) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed-header-wrapper) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed::before),
:deep(.el-table__fixed-right::before) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed-left .el-table__cell) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed-left) {
  background-color: #bde7f9 !important;
}

:deep(.el-table__fixed-left::before) {
  background-color: #bde7f9 !important;
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: #bde7f9;
  --el-table-fixed-left-column: #bde7f9;
  --el-table-row-hover-bg-color: var(--el-fill-color-light);
}

.group-option {
  font-weight: bold;
  background-color: #f5f7fa;
}

.indicator-option {
  padding-left: 20px !important;
}

.indicator-label {
  padding-left: 8px;
  color: #606266;
}

:deep(.el-select-dropdown__item) {
  padding: 0 20px;
  height: 34px;
  line-height: 34px;
}

:deep(.el-select-dropdown__item.group-option) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.indicator-option) {
  padding-left: 40px;
}

.parent-option {
  font-weight: bold;
  background-color: #f5f7fa;
}

.child-option {
  padding-left: 20px !important;
}

.sub-item-label {
  padding-left: 8px;
  color: #606266;
}

:deep(.el-select-dropdown__item) {
  padding: 0 20px;
  height: 34px;
  line-height: 34px;
}

:deep(.el-select-dropdown__item.parent-option) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.child-option) {
  padding-left: 40px;
}

.day-range-select {
  width: 110px;
}

.day-select {
  width: 100px;
}

:deep(.today-cell-input .el-input__wrapper) {
  background-color: #ffebee !important;
}

:deep(.today-cell-input .el-input__wrapper:hover) {
  background-color: #ffcdd2 !important;
}

:deep(.today-cell-input .el-input__wrapper.is-focus) {
  background-color: #ffcdd2 !important;
}

.child-factory-option {
  padding-left: 40px !important;
}

:deep(.el-select-group__title) {
  font-size: 15px;
  font-weight: bold;
}

:deep(.el-select-dropdown__item.child-option) {
  padding-left: 40px;
}

.additional-recording-container {
  padding: 10px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.date-range-picker {
  width: 350px;
}

:deep(.el-select-group__title) {
  font-size: 15px;
  font-weight: bold;
}

:deep(.temp-data .el-input__wrapper) {
  background-color: #fffbe5 !important;
  border: 1px dashed #e6a23c !important;
  box-shadow: none !important;
}

:deep(.temp-data .el-input__wrapper:hover) {
  background-color: #fffbe5 !important;
  border: 1px dashed #e6a23c !important;
}

:deep(.temp-data .el-input__wrapper.is-focus) {
  background-color: #fffbe5 !important;
  border: 1px dashed #e6a23c !important;
}

.temp-data-indicator {
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-right: 10px;
}
</style>
