import request from '@/config/axios'

// 数据源管理 VO
export interface DataSourceVO {
  id: number // 主键ID
  sourceCode: string // 数据源编码
  sourceName: string // 数据源名称
  sourceDesc: string // 数据源描述
  sourceType: string // 数据源类型 DIC_NAME=SOURCE_TYPE; mysql，orace，sqlserver，elasticsearch，接口，javaBean，数据源类型字典中item-extend动态生成表单
  sourceConfig: string // 数据源连接配置json：关系库{ jdbcUrl:'', username:'', password:'' } ES{ hostList:'ip1:9300,ip2:9300,ip3:9300', clusterName:'elasticsearch_cluster' }  接口{ apiUrl:'http://ip:port/url', method:'' } javaBean{ beanNamw:'xxx' }
  enableFlag: number // 0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG
  version: number // 版本
}

// 测试数据源连接 VO
export interface TestDataSourceVO {
  sourceType: string
  sourceConfig: string
}

// 数据源管理 API
export const DataSourceApi = {
  // 查询数据源管理分页
  getDataSourcePage: async (params: any) => {
    return await request.get({ url: `/report/data-source/page`, params })
  },

  // 查询数据源管理详情
  getDataSource: async (id: number) => {
    return await request.get({ url: `/report/data-source/get?id=` + id })
  },

  // 新增数据源管理
  createDataSource: async (data: DataSourceVO) => {
    return await request.post({ url: `/report/data-source/create`, data })
  },

  // 修改数据源管理
  updateDataSource: async (data: DataSourceVO) => {
    return await request.put({ url: `/report/data-source/update`, data })
  },

  // 删除数据源管理
  deleteDataSource: async (id: number) => {
    return await request.delete({ url: `/report/data-source/delete?id=` + id })
  },

  // 导出数据源管理 Excel
  exportDataSource: async (params) => {
    return await request.download({ url: `/report/data-source/export-excel`, params })
  },

  // 根据数据源类型 获取数据源列表
  queryAllDataSource: async (params) => {
    return await request.getOriginal({ url: `/report/data-source/queryAllDataSource`, params })
  },

  // 数据源测试连接
  testDataSource: async (data: TestDataSourceVO) => {
    return await request.post({ url: `/report/data-source/test-connection`, data })
  }
}
