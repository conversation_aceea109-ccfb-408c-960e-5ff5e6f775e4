import request from '@/config/axios'

// 修订审核相关接口
export const RevisionReviewAPI = {
  // 获取待审核列表
  getReviewList: async (params: any) => {
    return await request.get({ url: '/assay/revision-review/list', params })
  },

  // 提交审核结果
  submitReview: async (data: any) => {
    return await request.post({ url: '/assay/revision-review/submit', data })
  }
}

// 接口参数类型定义
export interface ReviewSearchParams {
  startDate?: string
  endDate?: string
  sampleId?: string
  projectType?: string
  status?: string
}

export interface ReviewData {
  id: string
  sampleId: string
  testDate: string
  projectType: string
  tester: string
  originalValue: string
  revisedValue: string
  revisionReason: string
  revisionTime: string
  revisor: string
  status: 'pending' | 'approved' | 'rejected'
  reviewReason?: string
  reviewTime?: string
  reviewer?: string
} 