import request from '@/config/axios'

// 数据集管理 VO
export interface DataSetVO {
  id: number // 主键ID
  setCode: string // 数据集编码
  setName: string // 数据集名称
  setDesc: string // 数据集描述
  sourceCode: string // 数据源编码
  dynSentence: string // 动态查询sql或者接口中的请求体
  caseResult: string // 结果案例
  enableFlag: number // 0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG
  version: number // 版本
  setType: string // 数据集类型
}

// 数据调用测试 VO
export interface TestTransformVO {
  sourceCode: string // 数据源编码
  dynSentence: string // 动态查询sql或者接口中的请求体
  setType: string // 数据集类型
  dataSetTransformDtoList: any,
  dataSetParamDtoList:  any,
}

// 数据集管理 API
export const DataSetApi = {
  // 查询数据集管理分页
  getDataSetPage: async (params: any) => {
    return await request.get({ url: `/report/data-set/page`, params })
  },

  // 查询数据集管理详情
  getDataSet: async (id: number) => {
    return await request.get({ url: `/report/data-set/getById?id=` + id })
  },

  // 新增数据集管理
  createDataSet: async (data: DataSetVO) => {
    return await request.post({ url: `/report/data-set/create`, data })
  },

  // 修改数据集管理
  updateDataSet: async (data: DataSetVO) => {
    return await request.put({ url: `/report/data-set/update`, data })
  },

  // 删除数据集管理
  deleteDataSet: async (id: number) => {
    return await request.delete({ url: `/report/data-set/delete?id=` + id })
  },

  // 导出数据集管理 Excel
  exportDataSet: async (params) => {
    return await request.download({ url: `/report/data-set/export-excel`, params })
  },

  // 数据转换测试
  testTransform: async (data) => {
    return await request.postOriginal({ url: `/report/data-set/testTransform`, data })
  },

  // 获取所有已开启数据集
  queryAllEnableDataSet: async () => {
    return await request.getOriginal({ url: `/report/data-set/queryAllEnableDataSet` })
  },

  // 获取数据集参数
  getDataSetParams: async (setCode: string) => {
    return await request.getOriginal({ url: `/report/data-set/detailBysetId/${setCode}` })
  },
}
