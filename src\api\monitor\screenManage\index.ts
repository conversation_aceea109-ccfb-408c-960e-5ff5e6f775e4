import request from '@/config/axios'



// 获取工艺流程图列表
export function getScreenListByFactoryId( factoryId?: number | string) {
  return request.getOriginal({
    url: `/monitor/screenManage/list?factoryId=${factoryId}`,
  }).then(res => res.data)
}

// 获取工艺流程图详情
export function getScreenById( screenId?: number | string) {
   return request.getOriginal({
    url: `/monitor/screenManage/getFlowByScreenId?screenId=${screenId}`,
  }).then(res => res.data)
}


/**
 * 通过画面ID获取Points数据
 * @param screenId 画面ID
 * @returns Points数据
 */
export function getPointsDataByScreenId(screenId: string | number) {
  return request.getOriginal({
    url: '/monitor/flow-point/getPointsDataByScreenId',
    params: { screenId }
  }).then(res => res.data)
}



/**
 * 创建新画面
 * @param data 画面数据，包含名称、SVG内容等
 * @returns 创建结果的data部分
 */
export function saveScreen(data: any) {
  console.log("创建新画面的数据:", data);
  // 调用创建接口，并统一返回数据格式
  return request.postOriginal({ 
    url: '/monitor/screenManage/create', 
    data 
  })
  .then(res => {
    // 打印完整响应，便于调试
    console.log("创建接口完整响应:", res);
    return res.data;
  });
}

/**
 * 更新已有画面
 * @param data 画面数据，包含ID、名称、SVG内容等
 * @returns 更新结果的data部分
 */
export function updateScreen(data: any) {
  console.log("更新画面的数据:", data);
  // 调用更新接口，并统一返回数据格式
  return request.postOriginal({ 
    url: '/monitor/screenManage/update', 
    data 
  })
  .then(res => {
    // 打印完整响应，便于调试
    console.log("更新接口完整响应:", res);
    return res.data;
  });
}


// 删除工艺流程图
export function deleteScreen(id: string | number) {
  return request.delete({
    url: '/monitor/screenManage/deleteScreen',
    params: { id }
  });
}

// 获取监测指标列表
export const getIndicatorList = (factoryId: string | number) => {
  return request.get({ url: '/monitor/indicator/list', params: { factoryId } })
}

// 获取实时数据
export const getRealTimeData = (indicatorCodes: string[]) => {
  return request.post({ url: '/monitor/data/realtime', data: { indicatorCodes } })
}
