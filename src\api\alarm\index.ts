import request from '@/config/axios'

/**
 * 告警规则相关接口
 */

/**
 * 获取监测因子列表
 * @description 从ODS系统获取监测因子列表，按设备分组
 * @param factoryId 水厂ID
 * @returns 监测因子列表数据，按设备分组
 */
export const getFactorList = async (factoryId: string) => {
  console.log('API getFactorList 调用参数:', { factoryId })
  const response = await request.getOriginal({ url: '/monitor/ods/pointName', params: { factoryId } })
  console.log('API getFactorList 原始返回:', response)
  return response
}

/**
 * 获取告警规则配置分页列表
 * @param params 分页和查询参数 {page, pageSize, factoryId, name, level, ruleType}
 * @description 支持的查询参数:
 * - factoryId: 水厂ID
 * - name: 规则名称，模糊查询
 * - level: 告警等级（I=重要，II=一般）
 * - ruleType: 类型（single=单因子，combination=组合因子）
 */
export const getAlarmRules = async (params: any) => {
  return await request.getOriginal({ url: '/monitor/alarm/rules/page', params })
}

/**
 * 创建或更新告警规则
 * @param data 告警规则数据
 * @description 根据data中是否包含id字段决定是创建还是更新
 * - 含有id字段: 更新现有规则
 * - 无id字段: 创建新规则
 * @description 参数结构变更:
 * - operator和threshold已移至ruleFactors中的每个因子
 * - 新增了indicatorId、factorType字段在ruleFactors中
 * @returns 创建/更新结果
 */
export const saveAlarmRule = async (data: any) => {
  // 根据是否有id字段判断是创建还是更新
  if (data.id) {
    // 编辑模式
    return await request.put({ url: `/monitor/alarm/rules/update`, data })
  } else {
    // 新增模式
    return await request.postOriginal({ url: '/monitor/alarm/rules/create', data })
  }
}

/**
 * 获取告警规则详情
 * @param id 规则ID
 * @description 返回值结构变更:
 * - operator和threshold已从规则移至ruleFactors中的每个因子
 * - ruleFactors中新增了indicatorId和factorType字段
 */
export const getAlarmRuleDetail = async (id: number | string) => {
  return await request.getOriginal({ url: `/monitor/alarm/rules/get?id=${id}` })
}

/**
 * 创建告警规则
 * @param data 规则数据
 * @description 参数结构变更:
 * - 阈值threshold和操作符operator现在位于ruleFactors中
 * - 每个因子都有独立的阈值、操作符和指标ID
 * - 新增indicatorId字段表示指标唯一标识
 * - 新增factorType字段表示指标类型
 */
export const createAlarmRule = async (data: any) => {
  return await request.post({ url: '/monitor/alarm/rules/create', data })
}

/**
 * 删除告警规则
 * @param id 规则ID
 */
export const deleteAlarmRule = async (id: number | string) => {
  return await request.delete({ url: `/monitor/alarm/rules/delete?id=${id}` })
}

/**
 * 告警通知设置相关接口
 */

/**
 * 获取告警通知设置分页列表
 * @param params 分页和查询参数
 */
export const getNotificationSettings = async (params: any) => {
  return await request.getOriginal({ url: '/monitor/alarm/notify-config/page', params })
}

/**
 * 获取告警通知设置详情
 * @param level 告警级别
 */
export const getNotificationDetail = async (level: string) => {
  return await request.getOriginal({ url: `/monitor/alarm/notify-config/get?level=${level}` })
}

/**
 * 获取告警通知角色列表
 * @description 获取可选的接收角色列表
 * @returns 角色列表数据
 */
export const getNotifyRoles = async () => {
  const response = await request.getOriginal({ url: '/monitor/alarm/notify-config/getRoles' })
  console.log('角色列表API响应:', response)
  return response
}

/**
 * 更新告警通知设置
 * @param data 通知设置数据
 * @description 通过PUT方法更新告警通知配置信息
 */
export const updateNotifyConfig = async (data: any) => {
  console.log("更新告警通知配置参数:", data);
  return await request.put({ url: '/monitor/alarm/notify-config/update', data })
}

/**
 * 创建告警通知配置
 * @param data 通知配置数据
 * @description 支持的参数:
 * - id: 主键ID (可选)
 * - level: 告警等级（I=重要，II=一般）(必需)
 * - notifyMethods: 通知方式JSON配置 (可选)
 * - receiverRoles: 接收角色 (可选)
 */
export const createNotifyConfig = async (data: any) => {
  console.log("创建告警通知配置参数:", data);
  
  return await request.postOriginal({ url: '/monitor/alarm/notify-config/create', data })
}

// 告警记录相关接口

/**
 * 获取告警记录分页列表
 * @param params 分页和查询参数
 */
export const getAlarmRecords = async (params: any) => {
  return await request.getOriginal({ url: '/monitor/alarm/records/page', params })
}

/**
 * 获取告警统计数据
 */
export const getAlarmStatistics = async () => {
  return await request.getOriginal({ url: '/monitor/alarm/statistics' })
}

/**
 * 确认告警
 * @param id 告警记录ID
 * @param remark 备注信息
 */
export const acknowledgeAlarm = async (id: number | string, remark?: string) => {
  return await request.put({ 
    url: '/monitor/alarm/records/acknowledge', 
    data: { id, remark } 
  })
}

/**
 * 获取告警记录详情
 * @param id 告警记录ID
 * @returns 告警记录详情数据
 */
export const getAlarmRecordDetail = async (id: number | string) => {
  return await request.getOriginal({ url: `/monitor/alarm/records/detail?id=${id}` })
}

/**
 * 获取告警事件分页列表
 * @param params 分页和查询参数
 * @description 支持的查询参数:
 * - waterPlantId: 水厂ID
 * - pageNo: 当前页码
 * - pageSize: 每页大小
 * - status: 处理状态，可选
 * - level: 告警级别，可选
 * - startTime: 开始时间，可选
 * - endTime: 结束时间，可选
 */
export const getAlarmEvents = async (params: any) => {
  return await request.getOriginal({ url: '/monitor/alarm/event/page', params })
}

/**
 * 获取告警操作历史记录
 * @param params 查询参数
 * @description 支持的参数:
 * - eventId: 告警ID
 * - pageNo: 当前页码
 * - pageSize: 每页大小
 */
export const getAlarmActionLogs = async (params: any) => {
  return await request.getOriginal({ url: '/monitor/alarm/action-log/page', params })
}

/**
 * 处理告警事件
 * @param data 处理数据
 * @description 支持的参数:
 * - eventId: 告警ID (必需)
 * - handler: 处理人 (必需)
 * - remark: 备注信息 (可选)
 * - status: 目标状态 (必需，'acknowledged'=已确认或'resolved'=已解决)
 */
export const processAlarmEvent = async (data: {
  eventId: number | string;
  handler: string;
  remark?: string;
  processStatus: 'acknowledged' | 'resolved';
}) => {
  return await request.post({ 
    url: '/monitor/alarm/action-log/create',
    data
  })
}

/**
 * 处理告警记录
 * @param data 处理数据
 * @description 支持的参数:
 * - id: 告警记录ID (必需)
 * - handler: 处理人 (必需)
 * - remark: 备注信息 (可选)
 * - status: 目标状态 (必需，'acknowledged'=已确认或'resolved'=已解决)
 */
export const processAlarmRecord = async (data: {
  id: number | string;
  handler: string;
  remark?: string;
  status: 'acknowledged' | 'resolved';
}) => {
  return await request.put({ 
    url: '/monitor/alarm/records/process',
    data
  })
}

/**
 * 获取告警处理记录分页列表
 * @param params 分页和查询参数
 * @description 支持的查询参数:
 * - factoryId: 水厂ID
 * - handler: 处理人，模糊查询
 * - level: 告警级别
 * - startDate: 开始时间
 * - endDate: 结束时间
 * - pageNo: 当前页码
 * - pageSize: 每页大小
 */
export const getAlarmProcessRecords = async (params: any) => {
  return await request.getOriginal({ url: '/monitor/alarm/process-records/page', params })
}

/**
 * 获取告警处理详情
 * @param id 处理记录ID
 * @returns 告警处理详情数据
 */
export const getAlarmProcessDetail = async (id: number | string) => {
  return await request.getOriginal({ url: `/monitor/alarm/process-records/detail?id=${id}` })
} 