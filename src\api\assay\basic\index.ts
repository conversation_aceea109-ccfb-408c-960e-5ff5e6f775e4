import request from '@/config/axios'

// ==================== 类型定义 ====================

// 检测项目类型相关类型
export interface TestCategoryVO {
  id?: number
  factoryId: number
  name: string
  code: string
  description?: string
  isEnabled: boolean
  createTime?: string
  updateTime?: string
}

export interface TestCategoryCreateReqVO {
  factoryId: number
  name: string
  code: string
  description?: string
  isEnabled: boolean
}

export interface TestCategoryUpdateReqVO {
  id: number
  factoryId: number
  name: string
  code: string
  description?: string
  isEnabled: boolean
}

export interface TestCategoryPageReqVO extends PageParam {
  factoryId: number
  name?: string
  isEnabled?: boolean
}

// 检测项目相关类型
export interface TestProjectVO {
  id?: number
  factoryId: number
  name: string
  code: string
  categoryId: number
  description?: string
  isEnabled: boolean
  createTime?: string
  updateTime?: string
}

export interface TestProjectCreateReqVO {
  factoryId: number
  name: string
  code: string
  categoryId: number
  description?: string
  isEnabled: boolean
}

export interface TestProjectUpdateReqVO {
  id: number
  factoryId: number
  name: string
  code: string
  categoryId: number
  description?: string
  isEnabled: boolean
}

// 检测指标相关类型
export interface TestIndicatorVO {
  id?: number
  factoryId: number
  name: string
  code: string
  projectId: number
  method: string
  unit: string
  standardMin?: number
  standardMax?: number
  equipment: string
  precisionLimit?: string
  sampleVolume?: number
  detectionTimeMinutes?: number
  isEnabled: boolean
  createTime?: string
  updateTime?: string
}

export interface TestIndicatorCreateReqVO {
  factoryId: number
  name: string
  code: string
  projectId: number
  method: string
  unit: string
  standardMin?: number
  standardMax?: number
  equipment: string
  precisionLimit?: string
  sampleVolume?: number
  detectionTimeMinutes?: number
  isEnabled: boolean
}

export interface TestIndicatorUpdateReqVO {
  id: number
  factoryId: number
  name: string
  code: string
  projectId: number
  method: string
  unit: string
  standardMin?: number
  standardMax?: number
  equipment: string
  precisionLimit?: string
  sampleVolume?: number
  detectionTimeMinutes?: number
  isEnabled: boolean
}

// 树形结构类型
export interface TestTreeNodeVO {
  id: number
  factoryId: number
  name: string
  code: string
  type: 'category' | 'project' | 'indicator'
  description?: string
  isEnabled: boolean
  hasChildren?: boolean
  children?: TestTreeNodeVO[]
  // 类型特有字段
  categoryId?: number // project 和 indicator 有
  projectId?: number // indicator 有
  method?: string // indicator 有
  unit?: string // indicator 有
  standardMin?: number // indicator 有
  standardMax?: number // indicator 有
  equipment?: string // indicator 有
  precisionLimit?: string // indicator 有
  sampleVolume?: number // indicator 有
  detectionTimeMinutes?: number // indicator 有
}

export interface TestTreePageReqVO extends PageParam {
  factoryId: number
  categoryId?: number
  name?: string
  isEnabled?: boolean
}

// 采样点相关类型
export interface SamplingPointVO {
  id?: number
  factoryId: number
  name: string
  code: string
  type: 'inlet' | 'outlet'
  location?: string
  managerId?: number
  isEnabled: boolean
  remark?: string
  createTime?: string
  updateTime?: string
}

export interface SamplingPointCreateReqVO {
  factoryId: number
  name: string
  code: string
  type: 'inlet' | 'outlet'
  location?: string
  managerId?: number
  isEnabled: boolean
  remark?: string
}

export interface SamplingPointUpdateReqVO {
  id: number
  factoryId: number
  name: string
  code: string
  type: 'inlet' | 'outlet'
  location?: string
  managerId?: number
  isEnabled: boolean
  remark?: string
}

export interface SamplingPointPageReqVO extends PageParam {
  factoryId: number
  name?: string
  type?: string
  isEnabled?: boolean
  managerId?: number
}

// 精简列表类型
export interface SimpleListVO {
  id: number
  factoryId: number
  name: string
  code: string
  type?: string
}

// ==================== API 接口 ====================

/**
 * 基础信息管理模块API
 */
export const AssayBasicApi = {
  // ==================== 检测项目类型管理 ====================
  
  /**
   * 创建检测项目类型
   */
  createTestCategory: async (data: TestCategoryCreateReqVO) => {
    return await request.post({
      url: '/admin-api/assay/test-category/create',
      data
    })
  },

  /**
   * 更新检测项目类型
   */
  updateTestCategory: async (data: TestCategoryUpdateReqVO) => {
    return await request.put({
      url: '/admin-api/assay/test-category/update',
      data
    })
  },

  /**
   * 删除检测项目类型
   */
  deleteTestCategory: async (id: number, factoryId: number) => {
    return await request.delete({
      url: '/admin-api/assay/test-category/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测项目类型详情
   */
  getTestCategory: async (id: number, factoryId: number) => {
    return await request.get<TestCategoryVO>({
      url: '/admin-api/assay/test-category/get',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测项目类型列表
   */
  getTestCategoryList: async (factoryId: number, name?: string, isEnabled?: boolean) => {
    return await request.get<TestCategoryVO[]>({
      url: '/admin-api/assay/test-category/list',
      params: { factoryId, name, isEnabled }
    })
  },

  /**
   * 获取检测项目类型精简列表
   */
  getTestCategorySimpleList: async (factoryId: number) => {
    return await request.get<SimpleListVO[]>({
      url: '/admin-api/assay/test-category/simple-list',
      params: { factoryId }
    })
  },

  // ==================== 检测项目管理 ====================
  
  /**
   * 创建检测项目
   */
  createTestProject: async (data: TestProjectCreateReqVO) => {
    return await request.post({
      url: '/admin-api/assay/test-project/create',
      data
    })
  },

  /**
   * 更新检测项目
   */
  updateTestProject: async (data: TestProjectUpdateReqVO) => {
    return await request.put({
      url: '/admin-api/assay/test-project/update',
      data
    })
  },

  /**
   * 删除检测项目
   */
  deleteTestProject: async (id: number, factoryId: number) => {
    return await request.delete({
      url: '/admin-api/assay/test-project/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 根据类型获取检测项目列表
   */
  getTestProjectListByCategory: async (categoryId: number, factoryId: number) => {
    return await request.get<TestProjectVO[]>({
      url: '/admin-api/assay/test-project/list-by-category',
      params: { categoryId, factoryId }
    })
  },

  // ==================== 检测指标管理 ====================

  /**
   * 创建检测指标
   */
  createTestIndicator: async (data: TestIndicatorCreateReqVO) => {
    return await request.post({
      url: '/admin-api/assay/test-indicator/create',
      data
    })
  },

  /**
   * 更新检测指标
   */
  updateTestIndicator: async (data: TestIndicatorUpdateReqVO) => {
    return await request.put({
      url: '/admin-api/assay/test-indicator/update',
      data
    })
  },

  /**
   * 删除检测指标
   */
  deleteTestIndicator: async (id: number, factoryId: number) => {
    return await request.delete({
      url: '/admin-api/assay/test-indicator/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 根据项目获取检测指标列表
   */
  getTestIndicatorListByProject: async (projectId: number, factoryId: number) => {
    return await request.get<TestIndicatorVO[]>({
      url: '/admin-api/assay/test-indicator/list-by-project',
      params: { projectId, factoryId }
    })
  },

  /**
   * 获取检测项目树形结构
   */
  getTestIndicatorTree: async (params: TestTreePageReqVO) => {
    return await request.get<PageResult<TestTreeNodeVO>>({
      url: '/admin-api/assay/test-indicator/tree',
      params
    })
  },

  // ==================== 采样点管理 ====================

  /**
   * 创建采样点
   */
  createSamplingPoint: async (data: SamplingPointCreateReqVO) => {
    return await request.post({
      url: '/admin-api/assay/sampling-point/create',
      data
    })
  },

  /**
   * 更新采样点
   */
  updateSamplingPoint: async (data: SamplingPointUpdateReqVO) => {
    return await request.put({
      url: '/admin-api/assay/sampling-point/update',
      data
    })
  },

  /**
   * 删除采样点
   */
  deleteSamplingPoint: async (id: number, factoryId: number) => {
    return await request.delete({
      url: '/admin-api/assay/sampling-point/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 获取采样点分页列表
   */
  getSamplingPointPage: async (params: SamplingPointPageReqVO) => {
    return await request.get<PageResult<SamplingPointVO>>({
      url: '/admin-api/assay/sampling-point/page',
      params
    })
  },

  /**
   * 根据类型获取采样点列表
   */
  getSamplingPointListByType: async (type: string, factoryId: number) => {
    return await request.get<SamplingPointVO[]>({
      url: '/admin-api/assay/sampling-point/list-by-type',
      params: { type, factoryId }
    })
  },

  /**
   * 获取采样点精简列表
   */
  getSamplingPointSimpleList: async (factoryId: number) => {
    return await request.get<SimpleListVO[]>({
      url: '/admin-api/assay/sampling-point/simple-list',
      params: { factoryId }
    })
  }
}
