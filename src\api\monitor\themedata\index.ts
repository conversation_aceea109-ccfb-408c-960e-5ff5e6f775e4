import request from '@/config/axios'

// 分页查询
export const getThemePage = (params) => {
  return request.post({ url: '/monitor/theme/page', data: params })
}

// 新增
export const addTheme = (data) => {
  return request.post({ url: '/monitor/theme/add', data })
}

// 修改
export const updateTheme = (data) => {
  return request.post({ url: '/monitor/theme/update', data })
}

// 删除
export const deleteTheme = (id) => {
  return request.delete({ url: `/monitor/theme/delete/${id}` })
}

// 详情
export const getTheme = (id) => {
  return request.get({ url: `/monitor/theme/get/${id}` })
}

// ================= 主题指标管理 =================

// 分页查询指标列表
export const getIndicatorPage = (params) => {
  return request.post({ url: '/monitor/themeIndicator/page', data: params })
}

// 新增指标
export const addIndicator = (data) => {
  return request.post({ url: '/monitor/themeIndicator/add', data })
}

// 修改指标
export const updateIndicator = (data) => {
  return request.post({ url: '/monitor/themeIndicator/update', data })
}

// 删除指标
export const deleteIndicator = (id) => {
  return request.get({ url: `/monitor/themeIndicator/delete/${id}` })
}

// 获取指标详情
export const getIndicator = (id) => {
  return request.get({ url: `/monitor/themeIndicator/get/${id}` })
}