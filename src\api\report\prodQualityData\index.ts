import request from '@/config/axios'


// 数据集管理 API
export const ProdQualityDataAPI = {
  // 查询数据集管理分页
  queryDataByFactoryIdAndDate: async (data : any) => {
    return await request.post({url: `/report/prod-quality-data/list`,data})
  }  ,

  // 提交更新
  saveOrUpdate: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-quality-data/saveOrUpdateBatch`,data})
  },

  // 暂存数据
  saveTemporarily: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-quality-data/saveTemporarily`,data})
  },

  // 补录数据
  additionalRecording: async (data : any) => {
    return await request.postOriginal({url: `/report/prod-quality-data/additional-recording`,data})
  },

  // 提交更新
  queryInOutCollectData: async (params : any) => {
    return await request.getOriginal({url: `/report/prod-quality-data/stat/all`, params})
  },
  
  // 获取统计数据（平均值、最大值、最小值、累计值）
  getQualityStat: async (params: any) => {
    return await request.get({url: `/report/prod-quality-data/quality-stat`, params})
  }
}
