import request from '@/config/axios'

export interface AssetQuery {
  pageNum?: number
  pageSize?: number
  assetCode?: string
  assetName?: string
  assetTypeId?: number
  statusId?: number
  isFixed?: boolean
  usageDeptId?: number
  usageUserId?: number
  location?: string
  purchaseDate?: string
  originalValue?: number
}

// 资产管理 VO
export interface AssetVO {
  id: number // 主键ID
  assetCode: string // 资产编码
  assetName: string // 资产名称
  assetTypeId: number // 资产类型ID
  statusId: number // 资产状态ID
  isFixed: boolean // 是否固定资产(0=否,1=是)
  usageDeptId: number // 使用部门ID
  usageUserId: number // 使用人ID
  location: string // 存放地点
  purchaseDate: Date // 购置日期
  originalValue: number // 资产原值/采购金额
  remark: string // 备注
  sewagePlantId: number // 污水处理厂ID
}

// 资产管理 API
export const AssetApi = {
  // 查询资产管理分页
  getAssetPage: async (data: AssetQuery) => {
    return await request.post({ url: `/asset/asset/page`, data })
  },

  // 查询资产管理详情
  getAsset: async (id: number) => {
    return await request.get({ url: `/asset/asset/get/${id}`})
  },

  // 新增资产管理
  createAsset: async (data: AssetVO) => {
    return await request.post({ url: `/asset/asset/create`, data })
  },

  // 修改资产管理
  updateAsset: async (data: AssetVO) => {
    return await request.put({ url: `/asset/asset/update`, data })
  },

  // 删除资产管理
  deleteAsset: async (id: number) => {
    return await request.delete({ url: `/asset/asset/delete/${id}`})
  },

  // 导出资产管理 Excel
  exportAsset: async (params) => {
    return await request.download({ url: `/asset/asset/export`, params })
  },


}
