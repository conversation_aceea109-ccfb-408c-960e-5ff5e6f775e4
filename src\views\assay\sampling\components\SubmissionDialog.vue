<template>
  <el-dialog
    v-model="dialogVisible"
    title="送检确认"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="submission-form"
    >
      <!-- 送检记录信息展示 -->
      <div class="info-section">
        <h4>送检记录信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="送检单号">
              <span>{{ currentRecord?.submissionCode || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品编号">
              <span>{{ currentRecord?.sampleCode || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采样点">
              <span>{{ currentRecord?.samplingPoint || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测项目">
              <span>{{ currentRecord?.testItems || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 送检信息填写 -->
      <div class="form-section">
        <h4>送检信息</h4>
        <el-form-item label="送检人" prop="submitterName">
          <el-select v-model="formData.submitterName" placeholder="请选择送检人" filterable>
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
            <el-option label="赵六" value="赵六" />
          </el-select>
        </el-form-item>

        <el-form-item label="送检日期" prop="submissionDate">
          <el-date-picker
            v-model="formData.submissionDate"
            type="date"
            placeholder="请选择送检日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="送检实验室" prop="submissionLab">
          <el-select v-model="formData.submissionLab" placeholder="请选择送检实验室" filterable>
            <el-option label="第三方检测实验室A" value="第三方检测实验室A" />
            <el-option label="第三方检测实验室B" value="第三方检测实验室B" />
            <el-option label="市环保局检测中心" value="市环保局检测中心" />
            <el-option label="省质检院" value="省质检院" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认送检
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// 组件引用
const formRef = ref<FormInstance>()
const dialogVisible = ref(false)
const loading = ref(false)
const currentRecord = ref<any>(null)

// 表单数据
const formData = reactive({
  submitterName: '',
  submissionDate: '',
  submissionLab: '',
  remark: ''
})

// 表单验证规则
const formRules: FormRules = {
  submitterName: [
    { required: true, message: '请选择送检人', trigger: 'change' }
  ],
  submissionDate: [
    { required: true, message: '请选择送检日期', trigger: 'change' }
  ],
  submissionLab: [
    { required: true, message: '请选择送检实验室', trigger: 'change' }
  ],
  remark: [
    { max: 1000, message: '长度不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 打开对话框
const open = (record: any) => {
  currentRecord.value = record
  
  // 初始化表单数据
  formData.submitterName = ''
  formData.submissionDate = new Date().toISOString().split('T')[0] // 默认今天
  formData.submissionLab = ''
  formData.remark = ''
  
  dialogVisible.value = true
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认送检
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 构建送检数据
    const submissionData = {
      recordId: currentRecord.value.id,
      submitterName: formData.submitterName,
      submissionDate: formData.submissionDate,
      submissionLab: formData.submissionLab,
      submissionTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      remark: formData.remark
    }
    
    // 这里应该调用API提交送检
    // await submitRecord(submissionData)
    
    // 模拟API调用
    setTimeout(() => {
      ElMessage.success('送检成功')
      loading.value = false
      dialogVisible.value = false
      resetForm()
      
      // 触发父组件刷新
      emit('success', submissionData)
    }, 1000)
    
  } catch (error) {
    loading.value = false
    console.error('送检失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  currentRecord.value = null
}

// 暴露方法给父组件
defineExpose({
  open
})

// 定义事件
const emit = defineEmits<{
  success: [data: any]
}>()
</script>

<script lang="ts">
export default {
  name: 'SubmissionDialog'
}
</script>

<style scoped>
.submission-form {
  max-height: 60vh;
  overflow-y: auto;
}

.info-section {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.info-section h4 {
  margin: 0 0 1rem 0;
  color: #409eff;
  font-size: 1rem;
  font-weight: 600;
}

.form-section h4 {
  margin: 0 0 1rem 0;
  color: #303133;
  font-size: 1rem;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
