<template>
  <Dialog v-model="dialogVisible" title="完成采样 - 记录样品信息" width="900px">
    <div class="status-transition-info mb-4">
      <el-alert 
        title="状态变更提示" 
        type="success" 
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="status-flow">
            <span class="status-item current">采样中</span>
            <el-icon class="arrow"><ArrowRight /></el-icon>
            <span class="status-item next">已完成</span>
          </div>
          <p class="mt-2 text-sm">完成采样后，任务状态将变更为"已完成"，请填写样品实际信息</p>
        </template>
      </el-alert>
    </div>

    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="任务信息" class="task-info">
        <div class="info-card">
          <p><strong>任务名称：</strong>{{ taskInfo.name }}</p>
          <p><strong>采样点：</strong>{{ taskInfo.samplingPointName }}</p>
          <p><strong>检测项目：</strong>{{ taskInfo.testItemName }}</p>
        </div>
      </el-form-item>

      <el-divider content-position="left">样品实际信息</el-divider>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="实际样品外观" prop="actualSampleAppearance">
            <el-input 
              v-model="formData.actualSampleAppearance" 
              placeholder="如：微黄色，略浑浊"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际样品数量" prop="actualSampleQuantity">
            <el-input-number
              v-model="formData.actualSampleQuantity"
              :min="1"
              :max="100"
              placeholder="mL"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="样品状态" prop="sampleStatus">
        <el-radio-group v-model="formData.sampleStatus">
          <el-radio label="正常">正常</el-radio>
          <el-radio label="异常">异常</el-radio>
          <el-radio label="需复检">需复检</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="完成时间" prop="completeTime">
        <el-date-picker
          v-model="formData.completeTime"
          type="datetime"
          placeholder="请选择完成时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DDTHH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :loading="formLoading" type="primary" @click="submitForm">
        完成采样
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'

defineOptions({ name: 'CompleteSamplingDialog' })

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref<FormInstance>()

interface TaskInfo {
  id: number
  name: string
  samplingPointName: string
  testItemName: string
}

const taskInfo = ref<TaskInfo>({
  id: 0,
  name: '',
  samplingPointName: '',
  testItemName: ''
})

const formData = reactive({
  taskId: 0,
  actualSampleAppearance: '',
  actualSampleQuantity: 1,
  sampleStatus: '正常',
  completeTime: '',
  remark: ''
})

const formRules: FormRules = {
  actualSampleAppearance: [
    // 放宽必填限制，但保留长度限制
    { max: 1000, message: '长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  actualSampleQuantity: [
    // 放宽必填限制，但保留数值范围限制
    { type: 'number', min: 0.1, max: 10000, message: '数量范围为 0.1-10000mL', trigger: 'blur' }
  ],
  sampleStatus: [
    // 放宽必填限制
  ],
  completeTime: [
    // 保留完成时间的必填要求，这是关键业务字段
    { required: true, message: '请选择完成时间', trigger: 'change' }
  ]
}

// 打开对话框
const open = (task: any) => {
  taskInfo.value = {
    id: task.id,
    name: task.name,
    samplingPointName: task.samplingPointName || '未指定',
    testItemName: task.testItemName || '未指定'
  }
  
  formData.taskId = task.id
  formData.actualSampleAppearance = ''
  formData.actualSampleQuantity = 1
  formData.sampleStatus = '正常'
  formData.completeTime = ''
  formData.remark = ''
  
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 调用完成采样API
    const requestData = {
      taskId: formData.taskId,
      actualSampleAppearance: formData.actualSampleAppearance,
      actualSampleQuantity: formData.actualSampleQuantity,
      sampleStatus: formData.sampleStatus,
      completeTime: formData.completeTime
    }
    
    // TODO: 替换为真实API调用
    // await completeSamplingApi(requestData)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('完成采样成功，任务状态已变更为"已完成"')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('完成采样失败:', error)
    ElMessage.error('完成采样失败，请重试')
  } finally {
    formLoading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.status-transition-info {
  margin-bottom: 1rem;
}

.status-flow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-item {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-item.current {
  background-color: #fff3cd;
  color: #856404;
}

.status-item.next {
  background-color: #d4edda;
  color: #155724;
}

.arrow {
  color: #666;
}

.task-info .info-card {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #67c23a;
}

.task-info .info-card p {
  margin: 0.25rem 0;
  color: #606266;
}
</style>
