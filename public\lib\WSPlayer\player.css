.ws-player {
  position: relative;
  background-color: #1c2834;
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 100%;
}
.ws-player .player-wrapper {
  width: 100%;
  height: calc(100% - 60px);
  overflow: hidden;
  /* 位置通过定位实现 */
  position: relative;
  user-select: none;
}
.ws-player .player-wrapper.nocontrol {
  height: 100%;
}
.ws-player .wsplayer-item {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  /*overflow: hidden;*/
}
.fullplayer .wsplayer-item{
  position: absolute;
}
.ws-player .wsplayer-item.selected {
  border: 1px solid #009cff;
  transition: all cubic-bezier(0.19, 1, 0.22, 1) .3s;
}
.ws-player .wsplayer-item.unselected {
  border: 1px solid #161A1E;
  transition: all cubic-bezier(0.19, 1, 0.22, 1) .3s;
}
.ws-player.fullplayer .player-wrapper .wsplayer-item.selected {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.ws-player.fullplayer .player-wrapper .wsplayer-item.unselected {
  display: none;
}
.ws-player .kind-stream-canvas {
  width: 100%;
  height: 100%;
  position: absolute;
}
.ws-player .ws-full-content {
  width: 100%;
  height: 100%;
}
.ws-player .default-status {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 10;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  transition: 0.5s;
}
.ws-player .player-control {
  position: absolute;
  width: 100%;
  background: rgba(0, 0, 0, .85);
  visibility: hidden;
  user-select: none;
}
.ws-player .top-control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 35;
  top: 0;
  height: 40px;
  color: #fff;
  overflow-x: clip;
}

.ws-player .custom-division-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 64px);
  z-index: 120;
}

.ws-player .record-control-bar {
  display: none;
  z-index: 20;
  bottom: 0;
  height: 39px;
  visibility: visible;
  color: #fff;
}
.ws-player .stream-info {
  font-size: 12px;
  height: 100%;
  letter-spacing: 0;
  line-height: 40px;
  color: #fff;
  white-space: nowrap;
}
.ws-player .record-control-right,
.ws-player .record-control-left {
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ws-player .record-control-left {
  float: left;
}
.ws-player .record-control-right {
  float: right;
}
.ws-player .wsplayer-progress-bar {
  position: relative;
  height: 5px;
  margin: 0 10px;
  cursor: pointer;
}
.ws-player .progress-bar_background {
  position: absolute;
  background: rgba(231, 231, 231, 0.3);
  height: 100%;
  width: 100%;
}
.ws-player .progress-bar_light {
  position: absolute;
  height: 100%;
  background: #459DF5;
  z-index: 2;
}
.ws-player .progress-bar_hover_light {
  position: absolute;
  cursor: pointer;
  background: rgba(231, 231, 231, 0.7);
  height: 100%;
}
.ws-player .error {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 30;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  user-select: none;
  color: rgba(255,255,255, .8);
  font-size: 30px;
}
.ws-player .play-pause-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 30;
  width: 100%;
  height: 100%;
  visibility: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, .3);
}
.ws-player .center-play-icon {
  width: 60px;
  height: 60px;
  background-image: url(./icon/play-n.png);
}
.ws-player .center-play-icon:hover {
  background-image: url(./icon/play-h.png);
}
.ws-player .opt-icons {
  display: flex;
  margin-right: 10px;
  justify-content: flex-end;
  flex: 1;
}
.ws-player .opt-icon {
  width: 20px;
  height: 20px;
  margin: 0 4px;
  cursor: pointer;
  flex-shrink: 0;
}
.ws-player .ws-talking {
  visibility: hidden;
  position: absolute;
  top: 40px;
  left: 10px;
  z-index: 10;
  color: #27D05A;
  font-size: 28px;
  user-select: none;
}
.ws-player .draw-triangle-icon.off {
  background-image: url(./icon/draw-triangle_off.svg);
}
.ws-player .draw-triangle-icon.off:hover {
  background-image: url(./icon/draw-triangle_off_hover.svg);
}
.ws-player .draw-triangle-icon.on {
  background-image: url(./icon/draw-triangle_on.svg);
}
.ws-player .draw-triangle-icon.on:hover {
  background-image: url(./icon/draw-triangle_on_hover.svg);
}
.ws-player .talk-icon.off {
  background-image: url(./icon/talk-off.svg);
}
.ws-player .talk-icon.off:hover {
  background-image: url(./icon/talk-off-hover.svg);
}
.ws-player .talk-icon.on {
  background-image: url(./icon/talk-on.svg);
}
.ws-player .audio-icon.off {
  background-image: url(./icon/voice-null.svg);
}
.ws-player .audio-icon.off:hover {
  background-image: url(./icon/voice-null_hover.svg);
}
.ws-player .audio-icon.on {
  background-image: url(./icon/voice-have_hover.svg);
}
.ws-player .refresh-replay-icon {
  background-image: url(./icon/refresh-replay.svg);
}
.ws-player .refresh-replay-icon:hover {
  background-image: url(./icon/refresh-replay_hover.svg);
}

.ws-player .hidden-deep{
  display: none !important;
}

/*.ws-player .audio-icon.on:hover {*/
/*  background-image: url(./icon/voice-have_hover.svg);*/
/*}*/

.ws-player .capture-icon {
  background-image: url(./icon/snapshot.svg);
}
.ws-player .capture-icon:hover {
  background-image: url(./icon/snapshot_hover.svg);
}
.ws-player .close-icon {
  background-image: url(./icon/close.svg);
}
.ws-player .close-icon:hover {
  background-image: url(./icon/close_hover.svg);
}
.ws-player .record-icon {
  background-image: url(./icon/RecordNormal.svg);
}
.ws-player .record-icon:hover {
  background-image: url(./icon/RecordHover.svg);
}
.ws-player .record-icon.recording {
  background-image: url(./icon/Recording.svg);
  background-repeat: no-repeat;
  background-size: 20px 20px;
}
.ws-player .more-icon {
  background-image: url(./icon/more.svg);
}
.ws-player .ws-more{
  position: relative;
}

.ws-player .ws-refresh-replay:hover .refresh-replay-icon {
  background-image: url(./icon/refresh-replay_hover.svg);
}
.ws-player .ws-draw-triangle:hover .draw-triangle-icon.off {
  background-image: url(./icon/draw-triangle_off_hover.svg);
}
.ws-player .ws-draw-triangle:hover .draw-triangle-icon.on {
  background-image: url(./icon/draw-triangle_on_hover.svg);
}
.ws-player .ws-talk:hover .talk-icon.off {
  background-image: url(./icon/talk-off-hover.svg);
}
.ws-player .ws-record:hover .record-icon {
  background-image: url(./icon/RecordHover.svg);
}
.ws-player .ws-record.recording .record-icon {
  background-image: url(./icon/Recording.svg);
}
.ws-player .ws-audio:hover .audio-icon.off {
  background-image: url(./icon/voice-null_hover.svg);
}
.ws-player .ws-audio:hover .audio-icon.on {
  background-image: url(./icon/voice-have_hover.svg);
}
.ws-player .ws-capture:hover .capture-icon {
  background-image: url(./icon/snapshot_hover.svg);
}

.ws-player .ws-refresh-replay:hover .ws-icon-tip,
.ws-player .ws-draw-triangle:hover .ws-icon-tip,
.ws-player .ws-talk:hover .ws-icon-tip,
.ws-player .ws-record:hover .ws-icon-tip,
.ws-player .ws-audio:hover .ws-icon-tip,
.ws-player .ws-capture:hover .ws-icon-tip {
  color: #3d7ccb;
}

.ws-player .opt-icons-vertical{
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 0;
  background: #000;
  opacity: 0.9;
  top: 30px;
  visibility: hidden;
}

.ws-player .opt-icons-vertical > div{
  width: 80px;
  display: flex;
  height: 32px;
  align-items: center;
  justify-content: flex-start;
}

.ws-player .opt-icons-vertical > div:lang(foreign){
  width: 130px;
  justify-content: flex-start;
}

.ws-player .opt-icons-vertical .ws-icon-tip{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ws-player .ws-control {
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
  align-items: center;
  height: 60px;
  width: 100%;
  background: #121a23;
  overflow-x: clip;
}
.ws-control .ws-select-self-adaption{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 32px;
  z-index: 105;
  border-radius: 4px;
  border: solid 1px #565965;
  cursor: pointer;
}
.ws-control .ws-select-self-adaption .ws-select-show{
  display: flex;
  color: #b2bac2;
  user-select: none;
}
.ws-control .ws-select-self-adaption .ws-self-adaption-type{
  position: absolute;
}
.ws-control .ws-select-self-adaption .ws-select-ul{
  padding: 0;
  width: 120px;
  background: #2a2b37;
  position: absolute;
  bottom: 16px;
  margin-bottom: 0;
  left: -60px;
  z-index: 20;
}
.ws-control .ws-select-self-adaption .ws-select-ul .ws-select-type-item{
  height: 32px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  margin-top: 4px;
  font-size: 14px;
  list-style: none;
  color: #b2bac2;
  width: 100%;
  padding: 0 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ws-control .ws-select-self-adaption .ws-select-ul .ws-select-type-item:hover{
  opacity: 0.7;
}
.ws-player .ws-record-control{
  position: relative;
  height: 60px;
  cursor: pointer;
  background-image: linear-gradient(0deg, #445160 0%, #1c2834 100%);
}
.ws-player .ws-timeline{
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.ws-player .ws-timeline-group{
  width: 100%;
  height: 8px;
  display: flex;
  justify-content: space-between;
  word-wrap: initial;
}
.ws-player .ws-time-space{
  height: 4px;
  border-left: 1px solid #909aa5;
}
.ws-player .ws-time-space-long{
  height: 8px;
}
.ws-player .ws-time-point{
  color: #909aa5;
  width: 0;
  position: relative;
  left: -20px;
  top: 0;
}
.ws-player .ws-time-point:first-child{
  left: 0;
}
.ws-player .ws-time-point:last-child{
  left: -40px;
}
.ws-player #ws-cursor{
  height: 40px;
  border-left: 1px solid #FFF;
  position: absolute;
  top: 0;
  z-index: 20;
}
.ws-player .ws-cursor-time{
  position: relative;
}
.ws-player .ws-cursor-time span{
  position: absolute;
  top: 40px;
  left: -32px;
  color: #909aa5;
}
.ws-player #ws-record-time-box{
  height: 22px;
  border-left: 1px solid #FFF;
  position: absolute;
  top: 0;
  z-index: 20;
}
.ws-player .ws-record-time{
  position: relative;
  color: #FFF;
}
.ws-player .ws-record-time span{
  position: absolute;
  top: 22px;
  left: -32px;
}
.ws-player .ws-record-area{
  position: absolute;
  height: 100%;
  width: 100%;
}
.ws-player .ws-ctrl-icon {
  width: 21px;
  height: 20px;
  margin: 0 8px;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}
.ws-player .ws-ctrl-btn-spread{
  border-left: 1px solid #FFF;
  margin: 0 8px;
  height: 8px;
}
.ws-player .full-screen-icon {
  margin-right: 16px;
  background-image: url(./icon/fullscreen.svg);
}
.ws-player .full-screen-icon:hover {
  background-image: url(./icon/fullscreen-hover.svg);
}
.ws-player .close-all-video {
  background-image: url(./icon/close-all.svg);
}
.ws-player .close-all-video:hover {
  background-image: url(./icon/close-all-hover.svg);
}
/* 一分屏 */
.ws-player .one-screen-icon {
  background-image: url(./icon/screen_1.svg);
}
.ws-player .one-screen-icon.active {
  background-image: url(./icon/screen_1_hover.svg);
}
.ws-player .one-screen-icon:hover {
  background-image: url(./icon/screen_1_hover.svg);
}
/* 二分屏 */
.ws-player .two-screen-icon {
  background-image: url(./icon/screen_2.svg);
}
.ws-player .two-screen-icon.active {
  background-image: url(./icon/screen_2_hover.svg);
  fill:#1c6bd6;
}
.ws-player .two-screen-icon:hover {
  background-image: url(./icon/screen_2_hover.svg);
  fill:#1c6bd6;
}

/* 三分屏 */
.ws-player .three-screen-icon {
  background-image: url(./icon/screen_3.svg);
}
.ws-player .three-screen-icon.active {
  background-image: url(./icon/screen_3_hover.svg);
}
.ws-player .three-screen-icon:hover {
  background-image: url(./icon/screen_3_hover.svg);
}

/* 四分屏 */
.ws-player .four-screen-icon {
  background-image: url(./icon/screen_4.svg);
}
.ws-player .four-screen-icon.active {
  background-image: url(./icon/screen_4_hover.svg);
}
.ws-player .four-screen-icon:hover {
  background-image: url(./icon/screen_4_hover.svg);
}

/* 六分屏 */
.ws-player .six-screen-icon {
  background-image: url(./icon/screen_6.svg);
}
.ws-player .six-screen-icon.active {
  background-image: url(./icon/screen_6_hover.svg);
}
.ws-player .six-screen-icon:hover {
  background-image: url(./icon/screen_6_hover.svg);
}

/* 八分屏 */
.ws-player .eight-screen-icon {
  background-image: url(./icon/screen_8.svg);
}
.ws-player .eight-screen-icon.active {
  background-image: url(./icon/screen_8_hover.svg);
}
.ws-player .eight-screen-icon:hover {
  background-image: url(./icon/screen_8_hover.svg);
}

/* 九分屏 */
.ws-player .nine-screen-icon {
  background-image: url(./icon/screen_9.svg);
}
.ws-player .nine-screen-icon.active {
  background-image: url(./icon/screen_9_hover.svg);
}
.ws-player .nine-screen-icon:hover {
  background-image: url(./icon/screen_9_hover.svg);
}

/* 十六分屏 */
.ws-player .sixteen-screen-icon {
  background-image: url(./icon/screen_16.svg);
}
.ws-player .sixteen-screen-icon.active {
  background-image: url(./icon/screen_16_hover.svg);
}
.ws-player .sixteen-screen-icon:hover {
  background-image: url(./icon/screen_16_hover.svg);
}

/* 二十五分屏 */
.ws-player .twenty-five-screen-icon {
  background-image: url(./icon/screen_25.svg);
}
.ws-player .twenty-five-screen-icon.active {
  background-image: url(./icon/screen_25_hover.svg);
}
.ws-player .twenty-five-screen-icon:hover {
  background-image: url(./icon/screen_25_hover.svg);
}

/* 自定义分屏 */
.ws-player .custom-screen-icon {
  background-image: url(./icon/screen_custom_division.svg)
}

.ws-player .custom-screen-icon:hover {
  background-image: url(./icon/screen_custom_division_hover.svg)
}

/* 省略号 */
.ws-player .ws-ctrl-ellipsis {
  position: absolute;
  right: 0;
  bottom: 20px;
  width: 40px;
  height: 20px;
  background-color: #121a23;
  background-image: url(./icon/ws-ctrl-ellipsis.svg);
  background-repeat: no-repeat;
  user-select: none;
}

.ws-player .ws-ctrl-ellipsis-list {
  display: none;
  position: relative;
  color: #fff;
  width: 130px;
  height: 150px;
  overflow-y: auto;
  transform: translate(-75px, -170px);
  z-index: 100;
}

.ws-player .ws-ctrl-ellipsis-list li:hover {
  background-color: #a6acb3;
  cursor: pointer;
}

.ws-player .ws-ctrl-ellipsis:hover {
  background-image: url(./icon/ws-ctrl-ellipsis-hover.svg);
  cursor: pointer;
}

.ws-player .ws-flex {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ws-player .ws-flex-end {
  width: 100%;
  justify-content: flex-end;
}

.ws-player .ws-flex-left {
  justify-content: flex-start;
}
.ws-player .ws-ctrl-record-icon {
  width: 24px;
  height: 24px;
  border-radius: 100%;
  margin: 0 8px;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}
.ws-player .ws-record-speed-txt{
  color: #b2bac2;
  text-align: center;
  margin: 0;
  width: 40px;
}
.ws-player .ws-record-play {
  background-image: url(./icon/record_play.svg);
}
.ws-player .ws-record-pause {
  background-image: url(./icon/record_pause.svg);
}
.ws-player .ws-record-speed-sub {
  background-image: url(./icon/speed_left.svg);
}
.ws-player .ws-record-speed-add {
  background-image: url(./icon/speed_right.svg);
}

/* spinner 动画 */
@keyframes spinner-line-fade-more {
  0%, 100% {
    opacity: 0; /* minimum opacity */
  }
  1% {
    opacity: 1;
  }
}

@keyframes spinner-line-fade-quick {
  0%, 39%, 100% {
    opacity: 0.25; /* minimum opacity */
  }
  40% {
    opacity: 1;
  }
}

@keyframes spinner-line-fade-default {
  0%, 100% {
    opacity: 0.22; /* minimum opacity */
  }
  1% {
    opacity: 1;
  }
}

@keyframes spinner-line-shrink {
  0%, 25%, 100% {
    /* minimum scale and opacity */
    transform: scale(0.5);
    opacity: 0.25;
  }
  26% {
    transform: scale(1);
    opacity: 1;
  }
}

.wsplayer-item .stream {
  margin-left: 6px;
  display: flex;
  overflow-x: clip;
}

.wsplayer-item .stream-type .stream-type-select{
  background: #1A78EA;
  color: #FFF;
}

.wsplayer-item .stream-type .stream-type-item:last-child {
  border-right: 0;
}

.wsplayer-item .select-container {
  margin-right: 8px;
  position: relative;
  user-select: none
}

.wsplayer-item .select-show {
  width: 120px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: rgba(0, 0, 0, .85);
  color: #ffffff;
  font-size: 14px;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1px 0 8px;
}

.wsplayer-item .select-show:lang(zh-cn) {
  width: 80px;
}

.wsplayer-item .select-show .code-stream {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wsplayer-item .select-ul {
  background-color: #2a2b37;
  color: white;
  width: 100px;
  position: absolute;
  left: 0;
  top: 40px;
  box-sizing: border-box;
}

.wsplayer-item .select-ul:lang(zh-cn){
  width: 80px;
}

.wsplayer-item .select-ul li {
  height: 32px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  margin-top: 4px;
  font-size: 14px;
  list-style: none;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wsplayer-item .select-ul li:hover {
  background-color: #3b414d;
}

.wsplayer-item .select-ul li:active {
  background-color: #1c6bd6;
}

.wsplayer-item ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

/**
  云台样式
 */
.ws-pan-tilt-control{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  justify-content: flex-end;
  border: 1px solid #e9ebee;
  padding-top: 10px;
  position: relative;
}

.ws-pan-tilt-circle-wrapper{
  flex: 1;
  display: flex;
  align-items: center;
}

.ws-pan-tilt-circle{
  width: 180px;
  height: 180px;
  background-color: #f5f6f9;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  transform: rotate(-45deg);
}

.ws-pan-tilt-direction-item{
  position: absolute;
  width: 90px;
  height: 90px;
  transform-origin: 100% 100%;
  transition: border 0.3s ease;
  cursor: pointer;
  user-select: none;
}

.ws-pan-tilt-direction-item:active{
  background-image: linear-gradient(150deg, rgba(28, 121, 244, 0.2), rgba(255, 255, 255, 0));
}

.ws-pan-tilt-circle-rotate{
  transform: rotate(45deg);
  overflow: hidden;
}

.ws-pan-tilt-direction-item:nth-child(1){
  transform: rotate(111deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(1) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(-66deg) translate(-1px, -8px);
  top: 55px;
  left: 52px;
}
.ws-pan-tilt-direction-item:nth-child(2){
  transform: rotate(156deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(2) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(-111deg) translate(1px, 2px);
  top: 55px;
  left: 44px;
}
.ws-pan-tilt-direction-item:nth-child(3){
  transform: rotate(201deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(3) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(-156deg) translate(0px, 3px);
  top: 55px;
  left: 44px;
}
.ws-pan-tilt-direction-item:nth-child(4){
  transform: rotate(-114deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(4) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(159deg) translate(0px, 1px);
  top: 55px;
  left: 46px;
}
.ws-pan-tilt-direction-item:nth-child(5){
  transform: rotate(-69deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(5) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(114deg) translate(2px, -2px);
  top: 51px;
  left: 48px;
}
.ws-pan-tilt-direction-item:nth-child(6){
  transform: rotate(-24deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(6) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(69deg) translate(2px, 2px);
  top: 52px;
  left: 50px;
}
.ws-pan-tilt-direction-item:nth-child(7){
  transform: rotate(21deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(7) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(24deg) translate(2px, 2px);
  top: 50px;
  left: 48px;
}
.ws-pan-tilt-direction-item:nth-child(8){
  transform: rotate(66deg) skew(45deg);
}
.ws-pan-tilt-direction-item:nth-child(8) img{
  width: 16px;
  position: absolute;
  transform: skew(-45deg) rotate(-21deg) translate(0px, 2px);
  top: 52px;
  left: 49px;
}
.ws-pan-tilt-inner-circle{
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: white;
  position: absolute;
  top: 28%;
  left: 28%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ws-pan-tilt-pzt-select{
  cursor: pointer;
}

.ws-cloud-control-wrapper{
  display: flex;
  width: 100%;
  height: 42px;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #e9ebee;
  margin-top: 10px;
}

.ws-cloud-control-wrapper .ws-pan-tilt-control-item img{
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin: 0 8px;
}

.ws-cloud-control-wrapper .cloud-control-separate{
  border-left: 1px solid #e9ebee;
  height: 20px;
}
.ws-pan-tilt-mask {
  position: absolute;
  z-index: 100;
  cursor: not-allowed;
  background-color: #ccc;
  opacity: 0.4;
}
.ws-pan-tilt-mask-direction {
  top: 0;
  width: 100%;
  height: calc(100% - 42px);
}
.ws-pan-tilt-mask-position{
  width: 80px;
  height: 80px;
  border-radius: 100%;
  top: calc((100% - 42px) / 2);
  transform: translate(-50%, -50%);
  left: 50%;
  display: none;
}
.ws-pan-tilt-mask-zoom {
  left: 0;
  bottom: 0;
  height: 42px;
  width: calc(50% + 36px);
}
.ws-pan-tilt-mask-aperture {
  right: 0;
  bottom: 0;
  height: 42px;
  width: calc(50% - 36px);
}

.ws-select-show-option {
  font-size: 14px;
  width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.ws-tag-container{
  position: absolute;
  height: 66px;
  color: #fff;
}
.ws-tag-container .ws-tag-content{
  position: absolute;
  padding: 16px;
  box-sizing: border-box;
  top: 64px;
  left: 42px;
  min-width: 160px;
  max-width: max-content;
  text-align: left;
  word-break: keep-all;
  visibility: visible;
  font-size: 12px;
  background: url(./icon/IotComboBackGround.png) no-repeat;
  background-size: 100% 100%;
}

.ws-tag-container .ws-tag-content p{
  white-space: nowrap;
}

.ws-tag-container .ws-tag-content-alarm{
  background: url(./icon/IotComboBackGround-alarm.png) no-repeat !important;
  background-size: 100% 100% !important;
}

.ws-tag-container p{
  margin: 0;
  padding: 0;
  line-height: 24px;
}
.ws-tag-container .ws-tag-title{
  line-height: 56px;
  min-width: 180px;
  font-size: 14px !important;
  cursor: pointer;
  position: relative;
  max-width: 960px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 36px 0 60px;
  height: 80px;
}
.ws-tag-container .ws-tag-title span{
  position: relative;
  z-index: 30;
}
.ws-tag-container .ws-left-png{
  position: absolute;
  left: 0;
}
.ws-tag-container .ws-middle-png{
  position: absolute;
  width: calc(100% - 171px);
  left: 95px;
}
.ws-tag-container .ws-right-png{
  position: absolute;
  right: 0;
}

.cRaw-container {
  overflow: hidden;
}

.ws-custom-dom-style {
  position: absolute;
  width: 100%;
  height: 100%;
}