!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.PlaySDKInterface=t():e.PlaySDKInterface=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}if(n.r(t),void 0===o)var o={};o.MD5=function(e){function t(e){var t=(e>>>0).toString(16);return"00000000".substr(0,8-t.length)+t}function n(e,t,n){return e&t|~e&n}function o(e,t,n){return n&e|~n&t}function a(e,t,n){return e^t^n}function i(e,t,n){return t^(e|~n)}function l(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]}function u(e){for(var t=[],n=0;n<e.length;n++)if(e.charCodeAt(n)<=127)t.push(e.charCodeAt(n));else for(var r=encodeURIComponent(e.charAt(n)).substr(1).split("%"),o=0;o<r.length;o++)t.push(parseInt(r[o],16));return t}function s(e){for(var t=new Array(e.length),n=0;n<e.length;n++)t[n]=e[n];return t}var c=null,f=null;function p(e,t){return 4294967295&e+t}return"string"==typeof e?c=u(e):e.constructor==Array?0===e.length?c=e:"string"==typeof e[0]?c=function(e){for(var t=[],n=0;n<e.length;n++)t=t.concat(u(e[n]));return t}(e):"number"==typeof e[0]?c=e:f=r(e[0]):"undefined"!=typeof ArrayBuffer?e instanceof ArrayBuffer?c=s(new Uint8Array(e)):e instanceof Uint8Array||e instanceof Int8Array?c=s(e):e instanceof Uint32Array||e instanceof Int32Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Float32Array||e instanceof Float64Array?c=s(new Uint8Array(e.buffer)):f=r(e):f=r(e),f&&alert("MD5 type mismatch, cannot process "+f),function(){function e(e,t,n,r){var o,a,i=v;v=y,y=h,h=p(h,(o=p(S,p(e,p(t,n))))<<(a=r)&4294967295|o>>>32-a),S=i}var r=c.length;c.push(128);var u=c.length%64;if(u>56){for(var s=0;s<64-u;s++)c.push(0);u=c.length%64}for(s=0;s<56-u;s++)c.push(0);c=c.concat(function(e){for(var t=[],n=0;n<8;n++)t.push(255&e),e>>>=8;return t}(8*r));var f=1732584193,d=4023233417,m=2562383102,g=271733878,S=0,h=0,y=0,v=0;for(s=0;s<c.length/64;s++){S=f;var _=64*s;e(n(h=d,y=m,v=g),3614090360,l(c,_),7),e(n(h,y,v),3905402710,l(c,_+4),12),e(n(h,y,v),606105819,l(c,_+8),17),e(n(h,y,v),3250441966,l(c,_+12),22),e(n(h,y,v),4118548399,l(c,_+16),7),e(n(h,y,v),1200080426,l(c,_+20),12),e(n(h,y,v),2821735955,l(c,_+24),17),e(n(h,y,v),4249261313,l(c,_+28),22),e(n(h,y,v),1770035416,l(c,_+32),7),e(n(h,y,v),2336552879,l(c,_+36),12),e(n(h,y,v),4294925233,l(c,_+40),17),e(n(h,y,v),2304563134,l(c,_+44),22),e(n(h,y,v),1804603682,l(c,_+48),7),e(n(h,y,v),4254626195,l(c,_+52),12),e(n(h,y,v),2792965006,l(c,_+56),17),e(n(h,y,v),1236535329,l(c,_+60),22),e(o(h,y,v),4129170786,l(c,_+4),5),e(o(h,y,v),3225465664,l(c,_+24),9),e(o(h,y,v),643717713,l(c,_+44),14),e(o(h,y,v),3921069994,l(c,_),20),e(o(h,y,v),3593408605,l(c,_+20),5),e(o(h,y,v),38016083,l(c,_+40),9),e(o(h,y,v),3634488961,l(c,_+60),14),e(o(h,y,v),3889429448,l(c,_+16),20),e(o(h,y,v),568446438,l(c,_+36),5),e(o(h,y,v),3275163606,l(c,_+56),9),e(o(h,y,v),4107603335,l(c,_+12),14),e(o(h,y,v),1163531501,l(c,_+32),20),e(o(h,y,v),2850285829,l(c,_+52),5),e(o(h,y,v),4243563512,l(c,_+8),9),e(o(h,y,v),1735328473,l(c,_+28),14),e(o(h,y,v),2368359562,l(c,_+48),20),e(a(h,y,v),4294588738,l(c,_+20),4),e(a(h,y,v),2272392833,l(c,_+32),11),e(a(h,y,v),1839030562,l(c,_+44),16),e(a(h,y,v),4259657740,l(c,_+56),23),e(a(h,y,v),2763975236,l(c,_+4),4),e(a(h,y,v),1272893353,l(c,_+16),11),e(a(h,y,v),4139469664,l(c,_+28),16),e(a(h,y,v),3200236656,l(c,_+40),23),e(a(h,y,v),681279174,l(c,_+52),4),e(a(h,y,v),3936430074,l(c,_),11),e(a(h,y,v),3572445317,l(c,_+12),16),e(a(h,y,v),76029189,l(c,_+24),23),e(a(h,y,v),3654602809,l(c,_+36),4),e(a(h,y,v),3873151461,l(c,_+48),11),e(a(h,y,v),530742520,l(c,_+60),16),e(a(h,y,v),3299628645,l(c,_+8),23),e(i(h,y,v),4096336452,l(c,_),6),e(i(h,y,v),1126891415,l(c,_+28),10),e(i(h,y,v),2878612391,l(c,_+56),15),e(i(h,y,v),4237533241,l(c,_+20),21),e(i(h,y,v),1700485571,l(c,_+48),6),e(i(h,y,v),2399980690,l(c,_+12),10),e(i(h,y,v),4293915773,l(c,_+40),15),e(i(h,y,v),2240044497,l(c,_+4),21),e(i(h,y,v),1873313359,l(c,_+32),6),e(i(h,y,v),4264355552,l(c,_+60),10),e(i(h,y,v),2734768916,l(c,_+24),15),e(i(h,y,v),1309151649,l(c,_+52),21),e(i(h,y,v),4149444226,l(c,_+16),6),e(i(h,y,v),3174756917,l(c,_+44),10),e(i(h,y,v),718787259,l(c,_+8),15),e(i(h,y,v),3951481745,l(c,_+36),21),f=p(f,S),d=p(d,h),m=p(m,y),g=p(g,v)}return function(e,n,r,o){for(var a="",i=0,l=0,u=3;u>=0;u--)i=255&(l=arguments[u]),i<<=8,i|=255&(l>>>=8),i<<=8,i|=255&(l>>>=8),i<<=8,a+=t(i|=l>>>=8);return a}(g,m,d,f).toUpperCase()}()};var a=function(e){return o.MD5(e)},i=0;function l(e){return s(u(c(e)))}function u(e){return p(d(f(e),8*e.length))}function s(e){for(var t,n=i?"0123456789ABCDEF":"0123456789abcdef",r="",o=0;o<e.length;o++)t=e.charCodeAt(o),r+=n.charAt(t>>>4&15)+n.charAt(15&t);return r}function c(e){for(var t,n,r="",o=-1;++o<e.length;)t=e.charCodeAt(o),n=o+1<e.length?e.charCodeAt(o+1):0,55296<=t&&t<=56319&&56320<=n&&n<=57343&&(t=65536+((1023&t)<<10)+(1023&n),o++),t<=127?r+=String.fromCharCode(t):t<=2047?r+=String.fromCharCode(192|t>>>6&31,128|63&t):t<=65535?r+=String.fromCharCode(224|t>>>12&15,128|t>>>6&63,128|63&t):t<=2097151&&(r+=String.fromCharCode(240|t>>>18&7,128|t>>>12&63,128|t>>>6&63,128|63&t));return r}function f(e){for(var t=Array(e.length>>2),n=0;n<t.length;n++)t[n]=0;for(n=0;n<8*e.length;n+=8)t[n>>5]|=(255&e.charCodeAt(n/8))<<24-n%32;return t}function p(e){for(var t="",n=0;n<32*e.length;n+=8)t+=String.fromCharCode(e[n>>5]>>>24-n%32&255);return t}function d(e,t){e[t>>5]|=128<<24-t%32,e[15+(t+64>>9<<4)]=t;for(var n=Array(80),r=1732584193,o=-271733879,a=-1732584194,i=271733878,l=-1009589776,u=0;u<e.length;u+=16){for(var s=r,c=o,f=a,p=i,d=l,y=0;y<80;y++){n[y]=y<16?e[u+y]:h(n[y-3]^n[y-8]^n[y-14]^n[y-16],1);var v=S(S(h(r,5),m(y,o,a,i)),S(S(l,n[y]),g(y)));l=i,i=a,a=h(o,30),o=r,r=v}r=S(r,s),o=S(o,c),a=S(a,f),i=S(i,p),l=S(l,d)}return Array(r,o,a,i,l)}function m(e,t,n,r){return e<20?t&n|~t&r:e<40?t^n^r:e<60?t&n|t&r|n&r:t^n^r}function g(e){return e<20?1518500249:e<40?1859775393:e<60?-1894007588:-899497514}function S(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function h(e,t){return e<<t|e>>>32-t}var y,v=l,_=function(e){e=e;var t=!1,n="",r="",o="",i=null,l=null,u=null,s=0,c=0,f=!1,p=!1,d=0,m=!1,g="",S={videoSSRC:0,audio1SSRC:0,audio2SSRC:0},h=!1,y=200,_=401,P=404,E=457,A=503,b="",w='Authorization: WSSE profile="UsernameToken"',C=[],T=1,M={},R="",D=null,I=null,U=null,H={},k={},L=null,F=null,O=null,B=null,G=null,V=null,x="play",N=null,W={},Y=[],z=!1,q=!1,j=0,K=0,X=3,J=0,Z=8,Q=!1;function $(){}function ee(e,t){var n=e.split("#")[0];if(n){var r=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),o=n.match(r);if(null!==o)return unescape(o[2])}return null}function te(i,l,u,s,c,f){return t?function(t,n,o,a,i,l){var u="";switch(t){case"OPTIONS":case"TEARDOWN":case"GET_PARAMETER":case"SET_PARAMETERS":u=t+" "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\n"+b+"\r\n";break;case"DESCRIBE":if(u=t+" "+r+" RTSP/1.0\r\nCSeq: "+T,!0===p&&-1===r.indexOf("subtype=5")){u+="\r\nRequire: www.onvif.org/ver20/backchannel"}u=u+"\r\n"+b+"\r\n",ue(),se();break;case"SETUP":e.log("trackID: "+n),u=t+" "+r+"/trackID="+n+" RTSP/1.0\r\nCSeq: "+T+"\r\n"+b+"Transport: DH/AVP/TCP;unicast;interleaved="+2*n+"-"+(2*n+1)+"\r\n",u+=0!=I?"Session: "+I+"\r\n\r\n":"\r\n",ue(),se();break;case"PLAY":u=t+" "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\nSession: "+I+";timeout=60\r\n",null!=a&&a>=0&&(u+=null!=i&&i>=0?"Range: npt="+a+"-"+i+"\r\n":"Range: npt="+a+"-\r\n"),null!=l&&-1!=l&&(u+=l<8?"Speed: "+l+"\r\n":"Scale: "+l+"\r\n"),u+=b+"\r\n",ue(),se(),Q||(J=setTimeout((function(){J&&(clearTimeout(J),J=0),j&&(clearTimeout(j),j=0),K&&(clearTimeout(K),K=0),G({errorCode:"409",description:"Rtsp Not Response"})}),1e3*Z));break;case"PAUSE":u=t+" "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\nSession: "+I+"\r\n\r\n";break;case"SCALE":u="PLAY "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\nSession: "+I+"\r\n",u+=l<8?"Speed: "+l+"\r\n":"Scale: "+l+"\r\n",u+=b+"\r\n"}return u}(i,l,0,s,c,f):function(e,t,i,l,u,s){var c="",f="",g=a(k.username+"::"+k.password),S=n.split("://")[1].split("/")[0].split(":")[0]+":8086",h=!1;if(m){var y=ee(o,"beginTime"),_=ee(o,"endTime"),P=y?y.replace(/-|:|\s/g,"_"):"",E=_?_.replace(/-|:|\s/g,"_"):"";0!=P.length&&0!=E.length&&(h=!0)}var A=o&&o.replace(/&beginTime=\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}&endTime=\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/,"");switch(e){case"PLAY":var C=function(){for(var e=[],t=0;t<32;t++)e.push(String.fromCharCode(256*Math.random()|0));return a(e.join(""))}(),M=(I=new Date,U=I.getTimezoneOffset(),H=I.getTime()+60*U*1e3,L=new Date(H),F=L.getFullYear(),O=L.getMonth()+1,B=L.getDate(),G=L.getHours(),V=L.getMinutes(),x=L.getSeconds(),F+"-"+(N=function(e){return e<10?"0"+e:e})(O)+"-"+N(B)+"T"+N(G)+":"+N(V)+":"+N(x)+"Z"),D=function(e){var t,n,r,o,a,i,l,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s="",c=0;for(;c<e.length;)t=e[c++],n=e[c++],r=e[c++],o=t>>2,a=(3&t)<<4|n>>4,i=(15&n)<<2|r>>6,l=63&r,isNaN(n)?i=l=64:isNaN(r)&&(l=64),s=s+u.charAt(o)+u.charAt(a)+u.charAt(i)+u.charAt(l);return s}(function(e){var t,n=e.trim(),r="0x"===n.substr(0,2).toLowerCase()?n.substr(2):n,o=r.length;if(o%2!=0)return"";for(var a=[],i=0;i<o;i+=2)t=parseInt(r.substr(i,2),16),a.push(t);return a}(v(C+M+g.toUpperCase())));c="GET "+r+(h?"&starttime=".concat(P,"&endtime=").concat(E,"&"):"&")+"method=0"+(A.length>0?"?sourceId=".concat(A):"")+" HTTP/1.1\r\n",c+="Accept-Sdp: Private\r\n",c+=b+"\r\n",c+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+S+"\r\n",null!=l&&l>=0?c+=null!=u&&u>=0?"Range: npt="+l+"-"+u+"\r\n":"Range: npt="+l+"-\r\n":d?c+="Range: npt="+d+"-\r\n":"ContinuePlay"!==R&&(c+="Range: npt=0.000000-\r\n"),null!=s&&-1!=s&&(c+=s<8?"Speed: "+s+"\r\n":"Scale: "+s+"\r\n"),p&&(f+="v=0\r\n",f+="o=- 2208989105 2208989105 IN IP4 0.0.0.0\r\n",f+="s=Media Server\r\n",f+="c=IN IP4 0.0.0.0\r\n",f+="t=0 0\r\n",f+="a=control:*\r\n",f+="a=packetization-supported:DH\r\n",f+="a=rtppayload-supported:DH\r\n",f+="a=range:npt=now-\r\n",f+="m=audio 0 RTP/AVP 8\r\n",f+="a=control:trackID=5\r\n",f+="a=rtpmap:8 PCMA/16000\r\n",c+="Private-Length: "+(f+="a=sendonly\r\n").length+"\r\n",c+="Private-Type: application/sdp\r\n"),c+=b===w?'WSSE: UsernameToken Username="'+k.username+'", PasswordDigest="'+D+'", Nonce="'+C+'", Created="'+M+'"\r\n\r\n':"\r\n",c+=f;break;case"PAUSE":c="GET "+r+(h?"&starttime=".concat(P,"&endtime=").concat(E,"&"):"&")+"method=1"+(A.length>0?"?sourceId=".concat(A):"")+" HTTP/1.1\r\n",c+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+S+"\r\n\r\n";break;case"KEEP_LIVE":c="GET "+r+(h?"&starttime=".concat(P,"&endtime=").concat(E,"&"):"&")+"method=2"+(A.length>0?"?sourceId=".concat(A):"")+" HTTP/1.1\r\n",c+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+S+"\r\n\r\n";break;case"STOP":case"TEARDOWN":c="GET "+r+(h?"&starttime=".concat(P,"&endtime=").concat(E,"&"):"&")+"method=3?sourceId="+A+" HTTP/1.1\r\n",c+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+S+"\r\n\r\n";break;case"SCALE":c="GET "+r+(h?"&starttime=".concat(P,"&endtime=").concat(E,"&"):"&")+"method=0"+(A.length>0?"?sourceId=".concat(A):"")+" HTTP/1.1\r\n",c+=s<8?"Speed: "+s+"\r\n":"Scale: "+s+"\r\n",c+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+S+"\r\n\r\n"}var I,U,H,L,F,O,B,G,V,x,N;return c}(i,0,0,s,c,f)}function ne(n){e.log(n);var o,l=n.search("CSeq: ")+5;if(T=parseInt(n.slice(l,l+10))+1,(o=function(e){var t={},n=0,r=0,o=null,a=null;if(-1!==e.search("Content-Type: application/sdp")){var i=e.split("\r\n\r\n");a=i[0]}else a=e;var l=a.split("\r\n"),u=l[0].split(" ");u.length>2&&(t.ResponseCode=parseInt(u[1]),t.ResponseMessage=u[2]);if(t.ResponseCode===y){for(n=1;n<l.length;n++)if("Public"===(o=l[n].split(":"))[0])t.MethodsSupported=o[1].split(",");else if("CSeq"===o[0])t.CSeq=parseInt(o[1]);else if("Content-Type"===o[0])t.ContentType=o[1],-1!==t.ContentType.search("application/sdp")&&(t.SDPData=fe(e));else if("Content-Length"===o[0])t.ContentLength=parseInt(o[1]);else if("Content-Base"===o[0]){var s=l[n].search("Content-Base:");-1!==s&&(t.ContentBase=l[n].substr(s+13))}else if("Session"===o[0]){var c=o[1].split(";");t.SessionID=parseInt(c[0])}else if("Transport"===o[0]){var f=o[1].split(";");for(r=0;r<f.length;r++){var p=f[r].search("interleaved=");if(-1!==p){var d=f[r].substr(p+12).split("-");d.length>1&&(t.RtpInterlevedID=parseInt(d[0]),t.RtcpInterlevedID=parseInt(d[1]),F&&F(t.RtpInterlevedID))}}}else if("RTP-Info"===o[0]){o[1]=l[n].substr(9);var m=o[1].split(",");for(t.RTPInfoList=[],r=0;r<m.length;r++){var g=m[r].split(";"),S={},h=0;for(h=0;h<g.length;h++){var v=g[h].search("url=");-1!==v&&(S.URL=g[h].substr(v+4)),-1!==(v=g[h].search("seq="))&&(S.Seq=parseInt(g[h].substr(v+4)))}t.RTPInfoList.push(S)}}}else if(t.ResponseCode===_)for(n=1;n<l.length;n++)if("CSeq"===(o=l[n].split(":"))[0])t.CSeq=parseInt(o[1]);else if("WWW-Authenticate"===o[0]){var P=o[1].split(",");for(r=0;r<P.length;r++){var E=P[r].search("Digest realm=");if(-1!==E){var A=P[r].substr(E+13).split('"');t.Realm=A[1]}if(-1!==(E=P[r].search("nonce="))){var b=P[r].substr(E+6).split('"');t.Nonce=b[1]}}}return t}(n)).ResponseCode===_&&(t&&""===b||!t&&b===w))!function(e){var n,o,i=k.username,l=k.password;n={Method:R.toUpperCase(),Realm:e.Realm,Nonce:e.Nonce,Uri:r},o=function(e,t,n,r,o,i){var l,u;return l=a(e+":"+r+":"+t).toLowerCase(),u=a(i+":"+n).toLowerCase(),a(l+":"+o+":"+u).toLowerCase()}(i,l,n.Uri,n.Realm,n.Nonce,n.Method),b='Authorization: Digest username="'+i+'", realm="'+n.Realm+'",',b+=' nonce="'+n.Nonce+'", uri="'+n.Uri+'", response="'+o+'"',t?(b+="\r\n",oe(te("OPTIONS",null))):oe(te("PLAY",null))}(o);else if(o.ResponseCode===y)if(t||(h=!0),"UnAuthorized"===R){M=fe(n),void 0!==o.ContentBase&&(M.ContentBase=o.ContentBase);var u=0;for(u=0;u<M.Sessions.length;u+=1){var s={};"JPEG"===M.Sessions[u].CodecMime||"H264"===M.Sessions[u].CodecMime||"H265"===M.Sessions[u].CodecMime||"H264-SVC"==M.Sessions[u].CodecMime||"RAW"==M.Sessions[u].CodecMime?(s.codecName=M.Sessions[u].CodecMime,"H264-SVC"===M.Sessions[u].CodecMime&&(s.codecName="H264"),"H265"===M.Sessions[u].CodecMime||M.Sessions[u].CodecMime,s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),void 0!==M.Sessions[u].Framerate&&(s.Framerate=parseInt(M.Sessions[u].Framerate)),C.push(s)):"PCMU"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G726-16")||-1!==M.Sessions[u].CodecMime.search("G726-24")||-1!==M.Sessions[u].CodecMime.search("G726-32")||-1!==M.Sessions[u].CodecMime.search("G726-40")||"PCMA"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G723.1")||-1!==M.Sessions[u].CodecMime.search("G729")||-1!==M.Sessions[u].CodecMime.search("MPA")||-1!==M.Sessions[u].CodecMime.search("L16")?("PCMU"===M.Sessions[u].CodecMime?s.codecName="G.711Mu":"G726-16"===M.Sessions[u].CodecMime?s.codecName="G.726-16":"G726-24"===M.Sessions[u].CodecMime?s.codecName="G.726-24":"G726-32"===M.Sessions[u].CodecMime?s.codecName="G.726-32":"G726-40"===M.Sessions[u].CodecMime?s.codecName="G.726-40":"PCMA"===M.Sessions[u].CodecMime?s.codecName="G.711A":"G723.1"===M.Sessions[u].CodecMime?s.codecName="G.723":"G729"===M.Sessions[u].CodecMime?s.codecName="G.729":"MPA"===M.Sessions[u].CodecMime?s.codecName="mpeg2":"L16"===M.Sessions[u].CodecMime&&(s.codecName="PCM"),s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"mpeg4-generic"===M.Sessions[u].CodecMime||"MPEG4-GENERIC"===M.Sessions[u].CodecMime?(s.codecName="mpeg4-generic",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"vnd.onvif.metadata"===M.Sessions[u].CodecMime?(s.codecName="MetaData",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):"stream-assist-frame"===M.Sessions[u].CodecMime?(s.codecName="stream-assist-frame",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):("audio"===M.Sessions[u].Type&&G({errorCode:201,description:"Audio format not supported"}),e.log("Unknown codec type:",M.Sessions[u].CodecMime,M.Sessions[u].ControlURL))}for(var c=0;c<C.length;c++)C[c].RtpInterlevedID=2*(C[c].trackID.split("=")[1]-0);D=0,clearInterval(U),U=setInterval((function(){return"GET_PARAMETER"===x&&"PAUSE"!=R?f++:f=0,6===f&&(i.fileOver=!0,pe(),O()),oe(te("KEEP_LIVE",null))}),4e4),R="Playing"}else{if("Options"===R)return R="Describe",te("DESCRIBE",null);if("Describe"===R){M=fe(n),void 0!==o.ContentBase&&(M.ContentBase=o.ContentBase);u=0;for(u=0;u<M.Sessions.length;u+=1){s={};"JPEG"===M.Sessions[u].CodecMime||"H264"===M.Sessions[u].CodecMime||"H265"===M.Sessions[u].CodecMime||"H264-SVC"==M.Sessions[u].CodecMime?(s.codecName=M.Sessions[u].CodecMime,"H264-SVC"==M.Sessions[u].CodecMime&&(s.codecName="H264"),M.Sessions[u].CodecMime,s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),void 0!==M.Sessions[u].Framerate&&(s.Framerate=parseInt(M.Sessions[u].Framerate)),C.push(s)):"PCMU"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G726-16")||-1!==M.Sessions[u].CodecMime.search("G726-24")||-1!==M.Sessions[u].CodecMime.search("G726-32")||-1!==M.Sessions[u].CodecMime.search("G726-40")||"PCMA"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G723.1")||-1!==M.Sessions[u].CodecMime.search("G729")||-1!==M.Sessions[u].CodecMime.search("MPA")||-1!==M.Sessions[u].CodecMime.search("L16")?("PCMU"===M.Sessions[u].CodecMime?s.codecName="G.711Mu":"G726-16"===M.Sessions[u].CodecMime?s.codecName="G.726-16":"G726-24"===M.Sessions[u].CodecMime?s.codecName="G.726-24":"G726-32"===M.Sessions[u].CodecMime?s.codecName="G.726-32":"G726-40"===M.Sessions[u].CodecMime?s.codecName="G.726-40":"PCMA"===M.Sessions[u].CodecMime?s.codecName="G.711A":"G723.1"===M.Sessions[u].CodecMime?s.codecName="G.723":"G729"===M.Sessions[u].CodecMime?s.codecName="G.729":"MPA"===M.Sessions[u].CodecMime?s.codecName="mpeg2":"L16"===M.Sessions[u].CodecMime&&(s.codecName="PCM"),s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"mpeg4-generic"===M.Sessions[u].CodecMime||"MPEG4-GENERIC"===M.Sessions[u].CodecMime?(s.codecName="mpeg4-generic",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"vnd.onvif.metadata"===M.Sessions[u].CodecMime?(s.codecName="MetaData",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):"stream-assist-frame"===M.Sessions[u].CodecMime?(s.codecName="stream-assist-frame",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):("audio"===M.Sessions[u].Type&&G({errorCode:201,description:"Audio format not supported"}),e.log("Unknown codec type:",M.Sessions[u].CodecMime,M.Sessions[u].ControlURL))}return D=0,R="Setup",Y.length?te("SETUP",Y[D].split("=")[1]-0):te("SETUP",0)}if("Setup"===R)return I=o.SessionID,D<Y.length?(null!=C[D]&&(C[D].RtpInterlevedID=o.RtpInterlevedID,C[D].RtcpInterlevedID=o.RtcpInterlevedID),I?Y[D+=1]?te("SETUP",Y[D].split("=")[1]):(R="Play",te("PLAY",null)):te("SETUP",Y[D].split("=")[1])):I?(R="Play",te("PLAY",null,0,d)):(D+=1,te("SETUP",0));if("Play"===R){I=o.SessionID,clearInterval(U),U=setInterval((function(){return"GET_PARAMETER"===x&&"PAUSE"!=R?f++:f=0,6===f&&(i.fileOver=!0,pe(),O()),oe(te("GET_PARAMETER",null))}),4e4);var f=0;R="Playing",h=!0}else"ContinuePlay"===R?R="Playing":"Playing"===R||e.log("unknown rtsp state:"+R)}else if(o.ResponseCode===A)if("Setup"===R&&(t&&"sendonly"===C[D].TalkTransType||!t&&-1!==C[D].trackID.search("trackID=t"))){if(C[D].RtpInterlevedID=-1,C[D].RtcpInterlevedID=-1,D+=1,p=!1,G({errorCode:"504",description:"Talk Service Unavilable",place:"RtspClient.js"}),D<C.length)return te("SETUP",C[D].trackID);R="Play",te("PLAY",null,0,d)}else G({errorCode:"503",description:"Service Unavilable"});else if(o.ResponseCode===P){if("Describe"===R||"Options"===R)return void G({errorCode:404,description:"rtsp not found"})}else if(o.ResponseCode===E)return void e.log("RTP disconnection detect!!!")}function re(t){null!==i&&i.readyState===WebSocket.OPEN?h&&i.send(t):e.log("SendRtpData - Websocket does not exist")}function oe(n){if(null!=n&&null!=n&&""!=n)if(null!==i&&i.readyState===WebSocket.OPEN){if(t&&!f)-1!==n.search("DESCRIBE")&&(z=!0,f=!0);null!=n&&i.send(ae(n))}else e.log("websocket未连接")}function ae(e){for(var t=e.length,n=new Uint8Array(new ArrayBuffer(t)),r=0;r<t;r++)n[r]=e.charCodeAt(r);return n}function ie(e){var t=e.split("//")[1].split("/")[0];if(n.includes("?serverIp="))n=n.slice(0,n.indexOf("serverIp="))+"serverIp="+t;else{var r="https:"===location.protocol?"wss":"ws";n=n.includes("8554")?"".concat(r,"://").concat(t):e.replace("rtsp",r)}}function le(e){var a=new Uint8Array(e.data),f=new Uint8Array(a.length);for(f.set(a,0),s=f.length,j&&(clearTimeout(j),j=0),K&&(clearTimeout(K),K=0);s>0;)if(36!==f[0]){var p=String.fromCharCode.apply(null,f),d=null;if(p.includes("302 Moved"))return ie(r=p.slice(p.indexOf("rtsp://"),p.indexOf("\r\n\r\n"))),$.prototype.disconnect(),void $.prototype.connect(n,r,o,W);if((-1!==p.indexOf("OffLine:File Over")||-1!==p.indexOf("OffLine: File Over")||-1!==p.indexOf("OffLine:Internal Error")||p.includes("is_session_end: true"))&&(i.fileOver=!0,B()),-1!==p.indexOf("OffLine:Internal Error")&&G({errorCode:500,description:"Internal Error"}),-1!==p.indexOf("OffLine:KmsUnavailable")&&G({errorCode:203,description:"KmsUnavailable"}),"Describe"===R&&-1!==p.indexOf("a=control:trackID=")&&(Y=p.split("a=control:").filter((function(e){return e.startsWith("trackID=")})).map((function(e){return e.split("\r\n")[0]}))||[]),-1!==p.indexOf("Range: npt=")){var m=p.split("Range: ")[1].split("\r\n")[0],h=m.split("npt=")[1].split("-")[0],y=m.split("-")[1];V(y-h)}var v=p.indexOf("interleaved=");if(-1!=v){var _=p.slice(v),P=_.indexOf("-");if(-1!=P){var E=_.slice(12,P);if(E/=2,-1!=(v=p.indexOf("ssrc="))){var A=p.slice(v+5,v+5+8),b=parseInt(A,16);0!=b&&(0==E?S.videoSSRC=b:1==E?S.audio1SSRC=b:2==E&&(S.audio2SSRC=b))}}}z||!q&&-1!==p.indexOf("200 OK")?(z&&(g=p),d=p.lastIndexOf("\r\n"),z=!1,q=!0):d=p.search("\r\n\r\n");var w=-1;if(-1===(w=t?p.search("RTSP"):p.search("HTTP")))return void(f=new Uint8Array);if(-1===d)return void(s=f.length);l=f.subarray(w,d+6),f=f.subarray(d+6),oe(ne(String.fromCharCode.apply(null,l))),s=f.length}else{if(x="PLAY",clearTimeout(N),N=setTimeout((function(){x="GET_PARAMETER"}),1e4),u=f.subarray(0,6),!((c=u[2]<<24|u[3]<<16|u[4]<<8|u[5])+6<=f.length))return void(s=f.length);var C=f.subarray(6,c+6);ce(u,C),f=f.subarray(c+6),s=f.length}}function ue(){j&&clearTimeout(j),J&&(clearTimeout(J),J=0),j=setTimeout((function(){G({errorCode:"407",description:"Request Timeout"})}),3e4)}function se(){K&&clearTimeout(K),K=setTimeout((function(){J&&(clearTimeout(J),J=0),G({errorCode:"408",description:"Short Request Timeout"})}),1e3*X)}function ce(e,t){J&&(clearTimeout(J),J=0),L(t),!0}function fe(e){var n={};n.Sessions=[];var r=null;-1!==e.search("Content-Type: application/sdp")||-1!==e.search("Private-Type: application/sdp")?r=e.split("\r\n\r\n")[1]:r=e;var o=r.split("\r\n"),a=0,i=!1;for(a=0;a<o.length;a++){var l=o[a].split("=");if(l.length>0)switch(l[0]){case"a":var u=l[1].split(":");if(u.length>1){if("control"===u[0]){var s=o[a].search("control:");!0===i?-1!==s&&(n.Sessions[n.Sessions.length-1].ControlURL=o[a].substr(s+8)):-1!==s&&(n.BaseURL=o[a].substr(s+8))}else if("rtpmap"===u[0]){var c=u[1].split(" ");n.Sessions[n.Sessions.length-1].PayloadType=c[0];var f=c[1].split("/");n.Sessions[n.Sessions.length-1].CodecMime=f[0],f.length>1&&(n.Sessions[n.Sessions.length-1].ClockFreq=f[1])}else if("framesize"===u[0]){var d=u[1].split(" ");if(d.length>1){var m=d[1].split("-");n.Sessions[n.Sessions.length-1].Width=m[0],n.Sessions[n.Sessions.length-1].Height=m[1]}}else if("framerate"===u[0])n.Sessions[n.Sessions.length-1].Framerate=u[1];else if("fmtp"===u[0]){var g=o[a].split(" ");if(g.length<2)continue;for(var S=1;S<g.length;S++){var h=g[S].split(";"),y=0;for(y=0;y<h.length;y++){var v=h[y].search("mode=");if(-1!==v&&(n.Sessions[n.Sessions.length-1].mode=h[y].substr(v+5)),-1!==(v=h[y].search("config="))&&(n.Sessions[n.Sessions.length-1].config=h[y].substr(v+7),H.config=n.Sessions[n.Sessions.length-1].config,H.clockFreq=n.Sessions[n.Sessions.length-1].ClockFreq,H.bitrate=n.Sessions[n.Sessions.length-1].Bitrate),-1!==(v=h[y].search("sprop-vps="))&&(n.Sessions[n.Sessions.length-1].VPS=h[y].substr(v+10)),-1!==(v=h[y].search("sprop-sps="))&&(n.Sessions[n.Sessions.length-1].SPS=h[y].substr(v+10)),-1!==(v=h[y].search("sprop-pps="))&&(n.Sessions[n.Sessions.length-1].PPS=h[y].substr(v+10)),-1!==(v=h[y].search("sprop-parameter-sets="))){var _=h[y].substr(v+21).split(",");_.length>1&&(n.Sessions[n.Sessions.length-1].SPS=_[0],n.Sessions[n.Sessions.length-1].PPS=_[1])}}}}}else 1===u.length&&("recvonly"===u[0]?n.Sessions[n.Sessions.length-1].TalkTransType="recvonly":"sendonly"===u[0]&&(n.Sessions[n.Sessions.length-1].TalkTransType="sendonly"));break;case"m":var P=l[1].split(" "),E={};E.Type=P[0],E.Port=P[1],E.Payload=P[3],n.Sessions.push(E),i=!0;break;case"b":if(!0===i){var A=l[1].split(":");n.Sessions[n.Sessions.length-1].Bitrate=A[1]}}}if(!t&&p)for(var b=0;b<n.Sessions.length;b+=1)"1"!==n.Sessions[b].ControlURL.split("=")[1]&&"2"!==n.Sessions[b].ControlURL.split("=")[1]||n.Sessions.splice(b,1);return n}function pe(){oe(te("TEARDOWN",null)),clearInterval(U),U=null,null!==i&&(i.onerror=null),null!==i&&i.readyState===WebSocket.OPEN&&(i.close(),i=null,I=null)}return $.prototype={connect:function(e,a,l,u){W=u,t=u.bRtspFlag,m=u.bPlayBack,Q=u.bBroadcast,X=u.nShortTimeout||3,Z=u.nRtspResponseTimeout||8,R=t?"Options":"UnAuthorized",i||(n=e,r=a,o=l,p=u.bTalkService,d=u.nRange,(i=new WebSocket(n)).binaryType="arraybuffer",i.fileOver=!1,i.addEventListener("message",le,!1),i.onopen=function(){if(t){var e="OPTIONS "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\n";e+="User-Agent: PlaySDK Client/1.0\r\n";var n=ae(e+="\r\n");i.send(n)}else b=w,oe(te("PLAY",null,null))},i.onerror=function(e){G({errorCode:202,description:"WebSocket Error"})},i.onclose=function(e){i&&!i.fileOver&&G({errorCode:204,description:"WebSocket Close"})})},disconnect:function(){j&&(clearTimeout(j),j=0),K&&(clearTimeout(K),K=0),oe(te("TEARDOWN",null,null)),clearInterval(U),U=null,null!==i&&(i.onerror=null),null!==i&&i.readyState===WebSocket.OPEN&&(i.close(),i=null,I=null)},controlPlayer:function(t){var n="";switch(t.command,t.command){case"PLAY":R="Play",n=te("PLAY",null,null);break;case"PLAY_SEEK":n=te("PLAY",null,null,t.startTime,t.endTime,t.speed);break;case"PAUSE":if("PAUSE"===R)break;R="PAUSE",n=te("PAUSE",null,null);break;case"SCALE":n=te("SCALE",null,null,-1,-1,t.speed);break;case"TEARDOWN":n=te("TEARDOWN",null,null);break;default:e.log("未知指令: "+t.command)}""!=n&&oe(n)},setRTSPURL:function(e){r=e},setCallback:function(e,t){switch(e){case"Disconnect":O=t;break;case"PlayBackStreamRange":V=t;break;case"Error":G=t}},setUserInfo:function(e,t){k.username=e,k.password=t},setRtpDataCallback:function(e){L=e},setRtpInterlevedIDCallback:function(e){F=e},setStreamFinishCallback:function(e){B=e},sendRtpData:function(e){re(e)},GetSdpInfo:function(){return g},GetSSRC:function(){return S}},new $};function P(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],u=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return E(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?E(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function A(e,t,n,r){return y._RTSP_PutStream(e,t,n,r)}function b(e,t,n,r){return y._RTSV_PutStream(e,t,n,r)}var w=function(){var e=null,t=0,n=!1,r=null,o=null,a=null,i=null;function l(){}return l.prototype={SetLogLevel:function(e){var t;t=e,(y=window.SCModule)._GLOBAL_SetLogLevel(t)},StartStream:function(l,u){return null==l.strDeviceID&&(l.strDeviceID=0),0==(t=u)?function(e,t,n,r,o,a,i){var l=y.intArrayFromString(t).concat(0),u=y._malloc(l.length);y.HEAPU8.set(l,u);var s=y.intArrayFromString(n).concat(0),c=y._malloc(s.length);y.HEAPU8.set(s,c);var f=y.intArrayFromString(o).concat(0),p=y._malloc(f.length);y.HEAPU8.set(f,p);var d=y._RTSP_StartStream(e,u,c,r,p,a,i);y._free(u),y._free(c),y._free(p)}(e=(y=window.SCModule)._RTSP_InitSession(),l.strRtspvUrl,l.strRtspvUri,l.bCustom,l.strDeviceID,l.bTalkService,!1):1==t?(null!=l.nAudioType&&null!=l.nFrequency&&null!=l.nChannelNum||(l.nAudioType=14,l.nFrequency=8e3,l.nChannelNum=1),function(e,t,n,r,o,a,i,l,u,s,c){var f=y.intArrayFromString(t).concat(0),p=y._malloc(f.length);y.HEAPU8.set(f,p);var d=y.intArrayFromString(n).concat(0),m=y._malloc(d.length);y.HEAPU8.set(d,m);var g=y.intArrayFromString(r).concat(0),S=y._malloc(g.length);if(y.HEAPU8.set(g,S),o&&y._RTSV_InitTalkInfo(e,a,i,l),u){var h=y.intArrayFromString(s).concat(0),v=y._malloc(h.length);y.HEAPU8.set(h,v);var _=y.intArrayFromString(c).concat(0),P=y._malloc(_.length);y.HEAPU8.set(_,P),y._RTSV_SetWSSEInfo(e,u,v,P),y._free(v),y._free(P)}var E=y._RTSV_StartStream(e,p,m,S);y._free(p),y._free(m),y._free(S)}(e=(y=window.SCModule)._RTSV_InitSession(),l.strRtspvUrl,l.strRtspvUri,l.strDeviceID,l.bTalkService,l.nAudioType,l.nFrequency,l.nChannelNum,!1,null,null)):2==t&&e&&function(e,t,n){var r=y.intArrayFromString(t).concat(0),o=y._malloc(r.length);y.HEAPU8.set(r,o);var a=y._VNSP_StartStream(e,o,n);y._free(o)}(e,l.strRtspvUri,l.nStreamType),(n=l.bTalkService)&&(r=y._malloc(10240),o=new Uint8Array(y.HEAPU8.buffer,r,10240),a=y._malloc(1048576),i=new Uint8Array(y.HEAPU8.buffer,a,1048576)),e},SetLoginParam:function(t){return function(e,t,n,r,o,a){var i=y.intArrayFromString(n).concat(0),l=y._malloc(i.length);y.HEAPU8.set(i,l);var u=y.intArrayFromString(r).concat(0),s=y._malloc(u.length);y.HEAPU8.set(u,s);var c=y.intArrayFromString(o).concat(0),f=y._malloc(c.length);y.HEAPU8.set(c,f);var p=y.intArrayFromString(a).concat(0),d=y._malloc(p.length);y.HEAPU8.set(p,d);var m=y._VNSP_SetLoginParam(e,t,l,s,f,d);y._free(l),y._free(s),y._free(f),y._free(d)}(e=(y=window.SCModule)._VNSP_InitSession(),t.playerType,t.deviceId,t.vnspType,t.clientMac,t.vnspVersion),e},StopStream:function(){var l;0==t?(l=e,y._RTSP_StopStream(l)):1==t?function(e){y._RTSV_StopStream(e)}(e):2==t&&function(e){y._VNSP_StopStream(e)}(e),e=null,n&&(o=null,y._free(r),i=null,y._free(a))},PauseStream:function(n){var r;n?0==t?(r=e,y._RTSP_PauseStream(r)):1==t?function(e){y._RTSV_PauseStream(e)}(e):2==t&&function(e){y._VNSP_PauseStream(e)}(e):0==t?function(e){y._RTSP_ResumeStream(e)}(e):1==t?function(e){y._RTSV_ResumeStream(e)}(e):2==t&&function(e){y._VNSP_ResumeStream(e)}(e)},PlayControl:function(n,r,o,a){var i,l,u=!1;o>=8&&(u=!0),void 0===a&&(a=-1),0==t?function(e,t,n,r,o){y._RTSP_PlayControl(e,t,n,r,o)}(e,n,r,o,u):1==t?function(e,t,n,r,o){y._RTSV_PlayControl(e,t,n,r,o)}(e,n,r,o,u):2==t&&(-1==n?(i=e,l=o,y._VNSP_FastPlay(i,l)):function(e,t,n,r){var o=y.intArrayFromString(n).concat(0),a=y._malloc(o.length);y.HEAPU8.set(o,a);var i=y.intArrayFromString(r).concat(0),l=y._malloc(i.length);y.HEAPU8.set(i,l);var u=y._VNSP_SetPlayRange(e,t,a,l);y._free(a),y._free(l)}(e,a,n,r))},PutStream:function(n,r){if(null!=o){var a=n.subarray(6);if(5==r)o.set(a),0==t?A(e,o.byteOffset,a.length,r):1==t?b(e,o.byteOffset,a.length,r):2==t&&function(e,t,n){y._VNSP_PutStream(e,t,n)}(e,o.byteOffset,a.length);else{if(a.length>1048576)return void console.warn("StreamClient buffer not enough, DataLen:"+a.length);i.set(a),0==t?A(e,i.byteOffset,a.length,r):1==t&&b(e,i.byteOffset,a.length,r)}}},GetPlayInfo:function(){var n=0;if(0==t){var r=P(function(e){var t=y._malloc(8),n=y._malloc(8);y._RTSP_GetPlayInfo(e,t,n);var r=y.HEAPF64[t>>3],o=y.HEAPF64[n>>3];return y._free(t),y._free(n),[r,o]}(e),2),o=r[0];n=r[1]-o}return n},SetMsgWaitTimeout:function(n){0==t?function(e,t){y._RTSP_SetMsgTimeout(e,t)}(e,n):1==t&&function(e,t){y._RTSV_SetMsgTimeout(e,t)}(e,n)}},new l},C="Opera",T="Chrome",M="Firefox",R="Edge",D="Edg",I="IE",U="Safari";function H(){var e=navigator.userAgent;return e.includes("Edge")?R:e.includes("Edg")?D:e.includes("Firefox")?M:e.includes("Chrome")?T:e.includes("Safari")?U:e.includes("compatible")&&e.includes("MSIE")&&e.includes("Opera")?I:e.includes("Opera")?C:""}function k(e){return navigator.userAgent.split(e)[1].split(".")[0].slice(1)}var L=function(){var e=2;function t(){}return t.prototype={setPrintLogLevel:function(t){e=t},fatal:function(t){e>=1&&console.error(t)},error:function(t){e>=2&&console.error(t)},warn:function(t){e>=3&&console.warn(t)},info:function(t){e>=4&&console.info(t)},trace:function(t){e>=5&&console.log(t)},log:function(t){e>=6&&console.log(t)}},new t};function F(){var e=navigator.userAgent.toLowerCase(),t=navigator.appName,n=null;return"Microsoft Internet Explorer"===t||e.indexOf("trident")>-1||e.indexOf("edge/")>-1?(n="ie","Microsoft Internet Explorer"===t?(e=/msie ([0-9]{1,}[\.0-9]{0,})/.exec(e),n+=parseInt(e[1])):e.indexOf("trident")>-1?n+=11:e.indexOf("edge/")>-1&&(n="edge")):e.indexOf("safari")>-1?n=e.indexOf("chrome")>-1?"chrome":"safari":e.indexOf("firefox")>-1&&(n="firefox"),n}(function(){function e(){}e.createFromElementId=function(t){for(var n=document.getElementById(t),r="",o=n.firstChild;o;)3===o.nodeType&&(r+=o.textContent),o=o.nextSibling;var a=new e;return a.type=n.type,a.source=r,a},e.createFromSource=function(t,n){var r=new e;return r.type=t,r.source=n,r}})(),function(){function e(e){this.gl=e,this.program=this.gl.createProgram()}e.prototype={attach:function(e){this.gl.attachShader(this.program,e.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(e){return this.gl.getAttribLocation(this.program,e)},setMatrixUniform:function(e,t){var n=this.gl.getUniformLocation(this.program,e);this.gl.uniformMatrix4fv(n,!1,t)}}}(),function(){var e=null;function t(e,t,n){this.gl=e,this.size=t,this.texture=e.createTexture(),e.bindTexture(e.TEXTURE_2D,this.texture),this.format=n||e.LUMINANCE,e.texImage2D(e.TEXTURE_2D,0,this.format,t.w,t.h,0,this.format,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)}t.prototype={fill:function(e,t){var n=this.gl;n.bindTexture(n.TEXTURE_2D,this.texture),t?n.texSubImage2D(n.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,n.UNSIGNED_BYTE,e):n.texImage2D(n.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,n.UNSIGNED_BYTE,e)},bind:function(t,n,r){var o=this.gl;e||(e=[o.TEXTURE0,o.TEXTURE1,o.TEXTURE2]),o.activeTexture(e[t]),o.bindTexture(o.TEXTURE_2D,this.texture),o.uniform1i(o.getUniformLocation(n.program,r),t)}}}();function O(e){this.buffer=e,this.previous=null,this.next=null}var B=function(e){O.call(this,e)};function G(e){var t=e||25;function n(){this.first=null,this.size=0}return n.prototype={enqueue:function(e){this.size>=t&&this.clear();var n=new B(e);if(null===this.first)this.first=n;else{for(var r=this.first;null!==r.next;)r=r.next;r.next=n}return this.size+=1,n},dequeue:function(){var e=null;return null!==this.first&&(e=this.first,this.first=this.first.next,this.size-=1),e},clear:function(){for(var e=null;null!==this.first;)e=this.first,this.first=this.first.next,this.size-=1,e.buffer=null,e=null;this.size=0,this.first=null}},new n}var V=function(e){var t=[],n={},r=e;function o(){for(var e in t)t[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)];0,1==r?n.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]):2==r&&(n.FTYP=new Uint8Array([105,115,111,109,0,0,2,0,105,115,111,109,105,115,111,50,97,118,99,49,109,112,52,49])),n.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),n.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),n.STSC=n.STCO=n.STTS,n.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),n.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),n.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),n.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),n.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}t={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],hev1:[],hvcC:[]};var a=function(e){for(var t=8,n=Array.prototype.slice.call(arguments,1),r=0;r<n.length;r++)t+=n[r].byteLength;var o=new Uint8Array(t),a=0;o[a++]=t>>>24&255,o[a++]=t>>>16&255,o[a++]=t>>>8&255,o[a++]=255&t,o.set(e,a),a+=4;for(r=0;r<n.length;r++)o.set(n[r],a),a+=n[r].byteLength;return o},i=function(e){return a(t.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),(n=e.config,r=n.length,o=new Uint8Array([0,0,0,0,3,23+r,0,1,0,4,15+r,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([r]).concat(n).concat([6,1,2])),a(t.esds,o)));var n,r,o},l=function(e){return"audio"===e.type?a(t.stsd,n.STSD_PREFIX,i(e)):a(t.stsd,n.STSD_PREFIX,function(e){var n=e.vps||[],o=e.sps||[],i=e.pps||[],l=[],u=[],s=[],c=0;for(c=0;c<n.length;c++)l.push((65280&n[c].byteLength)>>>8),l.push(255&n[c].byteLength),l=l.concat(Array.prototype.slice.call(n[c]));for(c=0;c<o.length;c++)u.push((65280&o[c].byteLength)>>>8),u.push(255&o[c].byteLength),u=u.concat(Array.prototype.slice.call(o[c]));for(c=0;c<i.length;c++)s.push((65280&i[c].byteLength)>>>8),s.push(255&i[c].byteLength),s=s.concat(Array.prototype.slice.call(i[c]));return 1==r?a(t.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a(t.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([o.length]).concat(u).concat([i.length]).concat(s)))):2==r?a(t.hev1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a(t.hvcC,new Uint8Array([1,e.general_profile_flag,(4278190080&e.general_profile_compatibility_flags)>>>24,(16711680&e.general_profile_compatibility_flags)>>>16,(65280&e.general_profile_compatibility_flags)>>>8,255&e.general_profile_compatibility_flags,(0xff0000000000&e.general_constraint_indicator_flags)>>>40,(0xff00000000&e.general_constraint_indicator_flags)>>>32,(4278190080&e.general_constraint_indicator_flags)>>>24,(16711680&e.general_constraint_indicator_flags)>>>16,(65280&e.general_constraint_indicator_flags)>>>8,255&e.general_constraint_indicator_flags,e.general_level_idc,240,0,252,252|e.chroma_format_idc,248|e.bitDepthLumaMinus8,248|e.bitDepthChromaMinus8,0,0,e.rate_layers_nested_length,3].concat([32,0,1]).concat(l).concat([33,0,1]).concat(u).concat([34,0,1]).concat(s)))):void 0}(e))},u=function(e){var r=null;return r="audio"===e.type?a(t.smhd,n.SMHD):a(t.vmhd,n.VMHD),a(t.minf,r,a(t.dinf,a(t.dref,n.DREF)),function(e){return a(t.stbl,l(e),a(t.stts,n.STTS),a(t.stsc,n.STSC),a(t.stsz,n.STSZ),a(t.stco,n.STCO))}(e))},s=function(e){return a(t.mdia,function(e){var n=e.timescale,r=e.duration;return a(t.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,85,196,0,0]))}(e),function(e){var r=null;return r="audio"===e.type?n.HDLR_AUDIO:n.HDLR_VIDEO,a(t.hdlr,r)}(e),u(e))},c=function(e){return a(t.trak,function(e){var n=e.id,r=e.duration,o=e.width,i=e.height;return a(t.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,o>>>8&255,255&o,0,0,i>>>8&255,255&i,0,0]))}(e),s(e))},f=function(e){return a(t.mvex,function(e){var n=e.id,r=new Uint8Array([0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return a(t.trex,r)}(e))},p=function(e){var n,r,o=(n=e.timescale,r=e.duration,a(t.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))),i=c(e),l=f(e);return a(t.moov,o,i,l)},d=function(e,n){return"audio"===e.type?audioTrun(e,n):function(e,n){var r,o=null,i=null,l=0,u=n;if(null===(r=e.samples||[])[0].frameDuration)for(u+=24+4*r.length,o=trunHeader(r,u),l=0;l<r.length;l++)i=r[l],o=o.concat([(4278190080&i.size)>>>24,(16711680&i.size)>>>16,(65280&i.size)>>>8,255&i.size]);else for(o=function(e,t){return[0,0,3,5,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t,0,0,0,0]}(r,u+=24+4*r.length+4*r.length),l=0;l<r.length;l++)i=r[l],o=o.concat([(4278190080&i.frameDuration)>>>24,(16711680&i.frameDuration)>>>16,(65280&i.frameDuration)>>>8,255&i.frameDuration,(4278190080&i.size)>>>24,(16711680&i.size)>>>16,(65280&i.size)>>>8,255&i.size]);return a(t.trun,new Uint8Array(o))}(e,n)},m=function(e,n){return a(t.moof,function(e){var n=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return a(t.mfhd,n)}(e),function(e){var n,r,o;return n=a(t.tfhd,new Uint8Array([0,2,0,0,0,0,0,1])),r=a(t.tfdt,new Uint8Array([0,0,0,0,e.baseMediaDecodeTime>>>24&255,e.baseMediaDecodeTime>>>16&255,e.baseMediaDecodeTime>>>8&255,255&e.baseMediaDecodeTime])),72,o=d(e,72),a(t.traf,n,r,o)}(n))};return o.prototype={initSegment:function(e){var r=a(t.ftyp,n.FTYP),o=p(e),i=new Uint8Array(r.byteLength+o.byteLength);return i.set(r,0),i.set(o,r.byteLength),i},mediaSegment:function(e,n,r,o){var i=m(e,n),l=function(e){return a(t.mdat,e)}(r),u=null;return(u=new Uint8Array(i.byteLength+l.byteLength)).set(i),u.set(l,i.byteLength),u}},new o};function x(e){var t=0,n=null,r=e;function o(){t=0,n=new W}function a(e,n){var r=n,o=t+r>>3;return r=t+n&7,e[o]>>7-(7&r)&1}function i(e,n){var r=t>>3,o=8*(r+1)-t;if(o<8)for(var a=0;a<3;a++){var i=e[r+a];i=0==a?i>>o<<o:2==a?i&255>>8-o|1<<o:0,e.set([i],r+a)}else e.set([0],r),e.set([1],r+1)}function l(e,t){if(t<=25)var n=u(e,t);else n=u(e,16)<<t-16|u(e,t-16);return n}function u(e,n){var r=0;if(1===n)r=a(e,0);else for(var o=0;o<n;o++)r=(r<<1)+a(e,o);return t+=n,r}function s(e,n){for(var r=0,o=0,i=n;t+i<8*e.length&&!a(e,i++);)r++;if(0===r)return t+=1,0;o=1<<r;for(var l=r-1;l>=0;l--,i++)o|=a(e,i)<<l;return t+=2*r+1,o-1}function c(e,t){var n=s(e,t);return 1&n?(n+1)/2:-n/2}function f(e){n.put("cpb_cnt_minus1",s(e,0)),n.put("bit_rate_scale",u(e,4)),n.put("cpb_size_scale",u(e,4));for(var t=n.get("cpb_cnt_minus1"),r=new Array(t),o=new Array(t),a=new Array(t),i=0;i<=t;i++)r[i]=s(e,0),o[i]=s(e,0),a[i]=u(e,1);n.put("bit_rate_value_minus1",r),n.put("cpb_size_value_minus1",o),n.put("cbr_flag",a),n.put("initial_cpb_removal_delay_length_minus1",u(e,5)),n.put("cpb_removal_delay_length_minus1",u(e,5)),n.put("dpb_output_delay_length_minus1",u(e,5)),n.put("time_offset_length",u(e,5))}function p(e,t){var n=Number(e).toString(16);for(t=null==t?t=2:t;n.length<t;)n="0"+n;return n}return o.prototype={parse:function(e){if(t=0,n.clear(),1==r){n.put("forbidden_zero_bit",u(e,1)),n.put("nal_ref_idc",u(e,2)),n.put("nal_unit_type",u(e,5)),n.put("profile_idc",u(e,8)),n.put("profile_compatibility",u(e,8)),n.put("level_idc",u(e,8)),n.put("seq_parameter_set_id",s(e,0));var o=n.get("profile_idc");if((100===o||110===o||122===o||244===o||44===o||83===o||86===o||118===o||128===o||138===o||139===o||134===o)&&(n.put("chroma_format_idc",s(e,0)),3===n.get("chroma_format_idc")&&n.put("separate_colour_plane_flag",u(e,1)),n.put("bit_depth_luma_minus8",s(e,0)),n.put("bit_depth_chroma_minus8",s(e,0)),n.put("qpprime_y_zero_transform_bypass_flag",u(e,1)),n.put("seq_scaling_matrix_present_flag",u(e,1)),n.get("seq_scaling_matrix_present_flag"))){for(var a=3!==n.get("chroma_format_idc")?8:12,p=new Array(a),d=0;d<a;d++)if(p[d]=u(e,1),p[d])for(var m=d<6?16:64,g=8,S=8,h=0;h<m;h++)S&&(S=(g+c(e,0)+256)%256),g=0===S?g:S;n.put("seq_scaling_list_present_flag",p)}if(n.put("log2_max_frame_num_minus4",s(e,0)),n.put("pic_order_cnt_type",s(e,0)),0===n.get("pic_order_cnt_type"))n.put("log2_max_pic_order_cnt_lsb_minus4",s(e,0));else if(1===n.get("pic_order_cnt_type")){n.put("delta_pic_order_always_zero_flag",u(e,1)),n.put("offset_for_non_ref_pic",c(e,0)),n.put("offset_for_top_to_bottom_field",c(e,0)),n.put("num_ref_frames_in_pic_order_cnt_cycle",s(e,0));for(var y=0;y<n.get("num_ref_frames_in_pic_order_cnt_cycle");y++)n.put("num_ref_frames_in_pic_order_cnt_cycle",c(e,0))}n.put("num_ref_frames",s(e,0)),n.put("gaps_in_frame_num_value_allowed_flag",u(e,1)),n.put("pic_width_in_mbs_minus1",s(e,0)),n.put("pic_height_in_map_units_minus1",s(e,0)),n.put("frame_mbs_only_flag",u(e,1)),0===n.get("frame_mbs_only_flag")&&n.put("mb_adaptive_frame_field_flag",u(e,1)),n.put("direct_8x8_interence_flag",u(e,1)),n.put("frame_cropping_flag",u(e,1)),1===n.get("frame_cropping_flag")&&(n.put("frame_cropping_rect_left_offset",s(e,0)),n.put("frame_cropping_rect_right_offset",s(e,0)),n.put("frame_cropping_rect_top_offset",s(e,0)),n.put("frame_cropping_rect_bottom_offset",s(e,0))),n.put("vui_parameters_present_flag",u(e,1)),n.get("vui_parameters_present_flag")&&function(e){n.put("aspect_ratio_info_present_flag",u(e,1)),n.get("aspect_ratio_info_present_flag")&&(n.put("aspect_ratio_idc",u(e,8)),255===n.get("aspect_ratio_idc")&&(i(e),n.put("sar_width",u(e,16)),i(e),n.put("sar_height",u(e,16)))),n.put("overscan_info_present_flag",u(e,1)),n.get("overscan_info_present_flag")&&n.put("overscan_appropriate_flag",u(e,1)),n.put("video_signal_type_present_flag",u(e,1)),n.get("video_signal_type_present_flag")&&(n.put("video_format",u(e,3)),n.put("video_full_range_flag",u(e,1)),n.put("colour_description_present_flag",u(e,1)),n.get("colour_description_present_flag")&&(n.put("colour_primaries",u(e,8)),n.put("transfer_characteristics",u(e,8)),n.put("matrix_coefficients",u(e,8)))),n.put("chroma_loc_info_present_flag",u(e,1)),n.get("chroma_loc_info_present_flag")&&(n.put("chroma_sample_loc_type_top_field",s(e,0)),n.put("chroma_sample_loc_type_bottom_field",s(e,0))),n.put("timing_info_present_flag",u(e,1)),n.get("timing_info_present_flag")&&(n.put("num_units_in_tick",u(e,32)),n.put("time_scale",u(e,32)),n.put("fixed_frame_rate_flag",u(e,1))),n.put("nal_hrd_parameters_present_flag",u(e,1)),n.get("nal_hrd_parameters_present_flag")&&f(e),n.put("vcl_hrd_parameters_present_flag",u(e,1)),n.get("vcl_hrd_parameters_present_flag")&&f(e),(n.get("nal_hrd_parameters_present_flag")||n.get("vcl_hrd_parameters_present_flag"))&&n.put("low_delay_hrd_flag",u(e,1)),n.put("pic_struct_present_flag",u(e,1)),n.put("bitstream_restriction_flag",u(e,1)),n.get("bitstream_restriction_flag")&&(n.put("motion_vectors_over_pic_boundaries_flag",u(e,1)),n.put("max_bytes_per_pic_denom",s(e,0)),n.put("max_bits_per_mb_denom",s(e,0)))}(e)}else if(2==r){var v=new ArrayBuffer(256),_=new Uint8Array(v);!function(e,t,n,r){for(var o=0,a=0;o+2<t&&a+2<r;++o)0==e[o]&&0==e[o+1]&&3==e[o+2]?(n[a++]=e[o++],n[a++]=e[o++]):n[a++]=e[o];for(;o<t&&a<r;)n[a++]=e[o++]}(e,e.length,_,256);var P=[],E=[];u(_,4);var A=u(_,3);for(n.put("temporalIdNested",u(_,1)),n.put("general_profile_space",u(_,2)),n.put("general_tier_flag",u(_,1)),n.put("general_profile_idc",u(_,5)),n.put("general_profile_compatibility_flags",l(_,32)),n.put("general_constraint_indicator_flags",(b=_,(w=48)<=32?l(b,w):l(b,w-32)<<32|l(b,32))),n.put("general_level_idc",u(_,8)),d=0;d<A&&d<6;d++)P[d]=u(_,1),E[d]=u(_,1);if(A>0)for(d=A;d<8;d++)u(_,2);for(d=0;d<A&&d<6;d++)E[d]&&u(_,8);s(_,0);n.put("chroma_format_idc",s(_,0));s(_,0),s(_,0);u(_,1),s(_,0),s(_,0),s(_,0),s(_,0),n.put("bitDepthLumaMinus8",s(_,0)+8),n.put("bitDepthChromaMinus8",s(_,0)+8),v=null,_=null}var b,w;return!0},getSizeInfo:function(){var e=0,t=0;0===n.get("chroma_format_idc")?e=t=0:1===n.get("chroma_format_idc")?e=t=2:2===n.get("chroma_format_idc")?(e=2,t=1):3===n.get("chroma_format_idc")&&(0===n.get("separate_colour_plane_flag")?e=t=1:1===n.get("separate_colour_plane_flag")&&(e=t=0));var r=n.get("pic_width_in_mbs_minus1")+1,o=n.get("pic_height_in_map_units_minus1")+1,a=(2-n.get("frame_mbs_only_flag"))*o,i=0,l=0,u=0,s=0;1===n.get("frame_cropping_flag")&&(i=n.get("frame_cropping_rect_left_offset"),l=n.get("frame_cropping_rect_right_offset"),u=n.get("frame_cropping_rect_top_offset"),s=n.get("frame_cropping_rect_bottom_offset"));var c=16*r*(16*a);return{width:16*r-e*(i+l),height:16*a-t*(2-n.get("frame_mbs_only_flag"))*(u+s),decodeSize:c}},getSpsValue:function(e){return n.get(e)},getCodecInfo:function(){if(1==r)return"avc1."+n.get("profile_idc").toString(16)+(n.get("profile_compatibility")<15?"0"+n.get("profile_compatibility").toString(16):n.get("profile_compatibility").toString(16))+n.get("level_idc").toString(16);if(2==r){var e="hev1.";switch(n.get("general_profile_space")){case 0:e+="";break;case 1:e+="A";break;case 2:e+="B";break;case 3:e+="C"}e+=n.get("general_profile_idc"),e+=".";for(var t=n.get("general_profile_compatibility_flags"),o=0,a=0;a<32&&(o|=1&t,31!=a);a++)o<<=1,t>>=1;e+=p(o,0),e+=".",0===n.get("general_tier_flag")?e+="L":e+="H",e+=n.get("general_level_idc");var i=n.get("general_constraint_indicator_flags"),l=[(65280&i)>>>8,(16711680&i)>>>16,(4278190080&i)>>>24,(0xff00000000&i)>>>32,(0xff0000000000&i)>>>40],u=!1,s="";for(a=5;0<=a;a--)(l[a]||u)&&(s="."+p(l[a],0)+s,u=!0);return e+=s}}},new o}var N,W=function(){this.map={}};W.prototype={put:function(e,t){this.map[e]=t},get:function(e){return this.map[e]},containsKey:function(e){return e in this.map},containsValue:function(e){for(var t in this.map)if(this.map[t]===e)return!0;return!1},isEmpty:function(e){return 0===this.size()},clear:function(){for(var e in this.map)delete this.map[e]},remove:function(e){delete this.map[e]},keys:function(){var e=new Array;for(var t in this.map)e.push(t);return e},values:function(){var e=new Array;for(var t in this.map)e.push(this.map[t]);return e},size:function(){var e=0;for(var t in this.map)e++;return e}};function Y(e){return N._RENDER_Destroy(e)}var z;function q(e,t,n){return N._RENDER_AlgoCommand(e,t,n)}var j=5,K=6,X=7,J=16,Z=18,Q=19,$=21,ee=22,te=25,ne=28,re=1,oe=2,ae=3,ie=4;function le(e){z._DRAW_SetLanguageEnvi(e)}function ue(e,t){z._DRAW_CleanScreen(e),z._DRAW_DrawByRenderHandle(e,0,t)}function se(e){this.buffer=e,this.previous=null,this.next=null}var ce=function(e,t,n,r,o,a){a=a;var i=n,l=e,u=r,s=null,c=0,f=0,p=!1,d=null,m=0,g=0,S=!1,h=!1,y=1,v=!1,_=0,P=0,E=0,A=0,b=o,w=-1,C=0,T=!1,M=null,R=!1,D=!1,I={},U=120,H=80,k=240,L=null,F=function(e){var t=3;switch(e.primaries){case"bt709":t=3;break;case"bt470bg":t=1;break;case"smpte170m":t=2;break;case"bt2020":t=4;break;case"smpte432":t=5;break;default:t=3}return t},O=function(e){var t=5;switch(e.transfer){case"bt709":case"smpte170m":t=5;break;case"iec61966-2-1":t=2;break;case"linear":t=1;break;case"pq":t=6;break;case"hlg":t=8;break;default:t=5}return t},B=function(e){var t=2;switch(e.matrix){case"rgb":case"bt709":t=2;break;case"bt470bg":t=1;break;case"bt2020-ncl":t=3;break;default:t=2}return t};function V(){d=new G(i),p=!1}var x=function(e){if(null!==s){if(!T){a.log("Single SW Play Start."),T=!0;var t={decodeMode:"canvas"};t.width=_,t.height=P,1==C?t.encodeMode="H264":2==C&&(t.encodeMode="H265"),M&&M(t)}return w=e.buffer.nFrameID,function(e,t,n,r){var o,a=N._malloc(80),i=0,l=null,u=0,s=null,c=0,f=null;9!=t.ImageFormat&&(i=t.width0*t.height0,l=N._malloc(i),N.writeArrayToMemory(t.DataY,l),u=t.width1*t.height1,s=N._malloc(u),N.writeArrayToMemory(t.DataU,s),c=t.width2*t.height2,f=N._malloc(c),N.writeArrayToMemory(t.DataV,f)),N.HEAP32[a/4+0]=0,N.HEAP32[a/4+1]=t.ImageFormat,N.HEAP32[a/4+2]=0,N.HEAP32[a/4+3]=0,9!=t.ImageFormat?(N.HEAP32[a/4+4]=l,N.HEAP32[a/4+5]=s,N.HEAP32[a/4+6]=f):(N.HEAP32[a/4+4]=t.DataY,N.HEAP32[a/4+5]=0,N.HEAP32[a/4+6]=0),N.HEAP32[a/4+7]=t.width0,N.HEAP32[a/4+8]=t.width1,N.HEAP32[a/4+9]=t.width2,N.HEAP32[a/4+10]=t.height0,N.HEAP32[a/4+11]=t.height1,N.HEAP32[a/4+12]=t.height2,N.HEAP32[a/4+13]=t.width0,N.HEAP32[a/4+14]=t.width1,N.HEAP32[a/4+15]=t.width2,N.HEAP32[a/4+16]=t.height0,N.HEAP32[a/4+17]=t.height1,N.HEAP32[a/4+18]=t.height2,N.HEAP32[a/4+19]=0,N.HEAPU8[a+80]=t.nColorPrimaries,N.HEAPU8[a+81]=t.nColorTransfer,N.HEAPU8[a+82]=t.nColorSpace,N.HEAPU8[a+83]=t.bColorFull;var p=0,d=0;if(0!==n&&null!=n){p=N._malloc(16),N.HEAP32[p/4+0]=n.left,N.HEAP32[p/4+1]=n.top,N.HEAP32[p/4+2]=n.right,N.HEAP32[p/4+3]=n.bottom}if(0!==r&&null!=r){d=N._malloc(16),N.HEAP32[d/4+0]=r.left,N.HEAP32[d/4+1]=r.top,N.HEAP32[d/4+2]=r.right,N.HEAP32[d/4+3]=r.bottom}o=N._RENDER_DrawImage(e,a,p,d),9!=t.ImageFormat&&(N._free(l),N._free(s),N._free(f)),N._free(a),0!==p&&N._free(p),0!==d&&N._free(d)}(s,e.buffer.Image,L,null),v&&ue(E,e.buffer.nFrameID),delete e.buffer,e.buffer=null,e.previous=null,e.next=null,e=null,R&&!D&&(0==q(s,1,!0)&&0==function(e,t){var n=N._malloc(20),r=null;null!==t.PanoARCustomParams&&(r=N._malloc(12),N.HEAPF32[r/4+0]=t.PanoARCustomParams.VerFieldViewAngle,N.HEAPF32[r/4+1]=t.PanoARCustomParams.HoriFieldViewAngle,N.HEAPF32[r/4+2]=t.PanoARCustomParams.DownPressAngle),N.HEAP32[n/4+0]=t.PanoARMode,N.HEAP32[n/4+1]=t.ImageStride,N.HEAP32[n/4+2]=t.ImageWidth,N.HEAP32[n/4+3]=t.ImageHeight,N.HEAP32[n/4+4]=r;var o=N._RENDER_SetAlgoParams(e,n);return N._free(n),null!==r&&N._free(r),o}(s,I)?console.log("[Trace]RENDER_AlgoCommand and RENDER_SetAlgoParams success!"):console.log("[Error]RENDER_AlgoCommand or RENDER_SetAlgoParams failed!"),D=!0),n=s,N._RENDER_Present(n),!0}var n;return!1},W=function(e){if(!0===p){if(0===c||e-c<U){if(0===c&&(c=e),null!==d){if(!S)null!==(t=d.dequeue())&&null!==t.buffer&&null!==t.buffer.dataY&&(S=!0,x(t));window.requestAnimationFrame(W)}return}if(u)return null!==(t=d.dequeue())&&null!==t.buffer&&null!==t.buffer.Image.DataY&&x(t),void window.requestAnimationFrame(W);0===f&&(f=e);var t,n=e-f;if(n>g)null!==(t=d.dequeue())&&null!==t.buffer&&null!==t.buffer.Image.DataY&&(h&&(m=n-g),g=t.buffer.nCostTime,g-=m,x(t),f=e,h=!0);window.requestAnimationFrame(W)}};return V.prototype={draw:function(e,t,n,r,o){if(!0===p){if(b)return(a={}).Image={ImageFormat:0,DataY:e,DataU:t,DataV:n,width0:_,width1:_/2,width2:_/2,height0:P,height1:P/2,height2:P/2,nColorPrimaries:r.nColorPrimaries,nColorTransfer:r.nColorTransfer,nColorSpace:r.nColorSpace,bColorFull:r.bColorFull},a.nFrameID=r.nFrameID,void x(new se(a));if(null!==d)if(document.hidden&&d.size>=25)d.clear();else{var a;(a={}).Image={ImageFormat:0,DataY:e,DataU:t,DataV:n,width0:_,width1:_/2,width2:_/2,height0:P,height1:P/2,height2:P/2,nColorPrimaries:r.nColorPrimaries,nColorTransfer:r.nColorTransfer,nColorSpace:r.nColorSpace,bColorFull:r.bColorFull},a.nFrameID=r.nFrameID,0==o&&(o=25);var i=1e3/o,l=d.size*i;u||(y=l>k?1.2:l<H?.8:1),a.nCostTime=1e3/o/y,a.nCostTime<20&&(a.nCostTime=20),d.enqueue(a)}}},drawWebCodecs:function(e,t,n,r,o,a){if(!0===p){if(b)return(i={}).Image={ImageFormat:9,DataY:e,DataU:0,DataV:0,width0:r,width1:0,width2:0,height0:o,height1:0,height2:0,nColorPrimaries:F(a),nColorTransfer:O(a),nColorSpace:B(a),bColorFull:a.fullRange},i.nFrameID=t,void x(new se(i));if(null!==d)if(document.hidden&&d.size>=25)d.clear();else{var i;(i={}).Image={ImageFormat:9,DataY:e,DataU:0,DataV:0,width0:r,width1:0,width2:0,height0:o,height1:0,height2:0,nColorPrimaries:F(a),nColorTransfer:O(a),nColorSpace:B(a),bColorFull:a.fullRange},i.nFrameID=t,0==n&&(n=25);var l=1e3/n,s=d.size*l;u||(y=s>k?1.2:s<H?.8:1),i.nCostTime=1e3/n/y,i.nCostTime<20&&(i.nCostTime=20),d.enqueue(i)}}},resize:function(e,t){this.stopRendering(),null!==d&&(d.clear(),d=null),null!==s&&(Y(s),s=null),l&&(s=function(e){var t=(N=window.REModule)._malloc(12),n=N.allocateUTF8(e);N.HEAP32[t/4+0]=12,N.HEAP32[t/4+1]=n,N.HEAP32[t/4+2]=0;var r=N._RENDER_Create(t);return N._free(n),N._free(t),r}(l.id)),d=new G(i),this.startRendering(),_=e,P=t},initStartTime:function(){0===c&&this.startRendering()},startRendering:function(){0===c&&(p=!0,window.requestAnimationFrame(W))},pause:function(){p=!1},play:function(){p=!0},stopRendering:function(){p=!1,c=0},setPlaySpeed:function(e){y=e},setEncodeType:function(e){C=e},setBeginDrawCallback:function(e){M=e},terminate:function(){p=!1,c=0,T=!1,null!==d&&(d.clear(),d=null),s&&Y(s),s=null,w=-1},getVideoBufferQueueSize:function(){return d.size},OpenIVS:function(e,t){(function(e){(z=window.IVSModule)._DRAW_Open(e)})(E=e),function(e,t){var n=z.allocateUTF8(t);z._DRAW_SetWebCanvas(e,n),z._free(n)}(E,t),le(A),v=!0},CloseIVS:function(){var e;v&&(v=!1,e=E,z._DRAW_Clean(e),function(e){z._DRAW_Close(e)}(E))},SetIvsEnable:function(e,t){v&&function(e,t,n){z._DRAW_SetEnable(e,t,n)}(E,e,t)},SetIvsLanguageEnvi:function(e){A=e,v&&le(e)},CleanScreen:function(e,t,n,r){null!=s&&(d.clear(),function(e,t,n,r,o){var a=N._malloc(16);N.HEAP32[a/4+0]=t,N.HEAP32[a/4+1]=n,N.HEAP32[a/4+2]=r,N.HEAP32[a/4+3]=o;var i=N._RENDER_ClearScreen(e,a);N._free(a)}(s,e,t,n,r))},DrawIVS:function(e,t,n,r){if(v){var o=function(e){var t=0,n=0;switch(e){case J:t=te,n=1;break;case Z:t=ne,n=1}return{bDeal:n,nIvsDrawType:t}}(t),a=o.bDeal,i=o.nIvsDrawType;if(a)!function(e,t,n,r,o){var a=z._malloc(r);z.writeArrayToMemory(n,a);var i=z._DRAW_InputIVSData(e,t,a,r,o);z._free(a)}(E,i,e,n,r);else switch(t){case j:!function(e,t,n,r){var o=z._malloc(n);z.writeArrayToMemory(t,o);var a=z._DRAW_InputJsonData(e,o,n,r);z._free(o)}(E,e,n,r);break;case K:!function(e,t,n,r,o){var a=z._malloc(r);z.writeArrayToMemory(n,a);var i=z._DRAW_InputTrackData(e,t,a,r,o);z._free(a)}(E,0,e,n,r);break;case X:case Q:case $:case ee:!function(e,t,n,r,o){var a=!1,i=z._malloc(r);if(Q==t){var l=0,u=new Array(512);z.HEAPU16[i/2+0]=n.NumberStat,z.HEAPU16[i/2+1]=n.nIntelFlowPlanNum;for(var s=z._malloc(12*n.nIntelFlowPlanNum),c=s,f=0;f<n.nIntelFlowPlanNum;f++){z.HEAPU16[c/2+0]=n.pIntelFlowPlan[f].PlanId,z.HEAPU16[c/2+1]=n.pIntelFlowPlan[f].RuleType,z.HEAPU16[c/2+4]=n.pIntelFlowPlan[f].RegionNum,u[l]=z._malloc(12*n.pIntelFlowPlan[f].RegionNum);for(var p=u[l],d=0;d<n.pIntelFlowPlan[f].RegionNum;d++)z.HEAPU16[p/2+0]=n.pIntelFlowPlan[f].pRegion[d].RegionId,z.HEAPU16[p/2+1]=n.pIntelFlowPlan[f].pRegion[d].State,z.HEAP32[p/4+1]=n.pIntelFlowPlan[f].pRegion[d].PeopleNum,p+=12;if(z.HEAP32[c/4+1]=u[l],++l>=512)break;c+=12}z.HEAP32[i/4+1]=s,a=z._DRAW_InputTrackDataEx2(e,t,i,r,o);for(var m=0;m<l;m++)z._free(u[l]);z._free(s)}else if(ee==t){z.HEAP32[i/4+0]=n.nId,z.HEAP16[i/2+2]=n.wCustom,z.HEAPU8[i+6]=n.chState,z.HEAPU8[i+7]=n.chCount;for(var g=z._malloc(12*n.chCount),S=g,h=new Array(n.chCount),y=new Array(n.chCount),v=new Array(n.chCount),_=0;_<n.chCount;_++){if(z.HEAP32[S/4+0]=n.pElement[_].nStructType,z.HEAP32[S/4+1]=n.pElement[_].nStructLength,v[_]=z._malloc(n.pElement[_].nStructLength),re==n.pElement[_].nStructType)z.HEAPU8[v[_]+0]=n.pElement[_].pStruct.chType,z.HEAPU8[v[_]+1]=n.pElement[_].pStruct.chWidth,z.HEAPU8[v[_]+2]=n.pElement[_].pStruct.chStyle,z.HEAP16[v[_]/2+2]=n.pElement[_].pStruct.wRadius,z.HEAP16[v[_]/2+4]=n.pElement[_].pStruct.positionCircle.x,z.HEAP16[v[_]/2+5]=n.pElement[_].pStruct.positionCircle.y,z.HEAPU8[v[_]+12]=n.pElement[_].pStruct.chLineA,z.HEAPU8[v[_]+13]=n.pElement[_].pStruct.chLineR,z.HEAPU8[v[_]+14]=n.pElement[_].pStruct.chLineG,z.HEAPU8[v[_]+15]=n.pElement[_].pStruct.chLineB,z.HEAPU8[v[_]+16]=n.pElement[_].pStruct.chRegA,z.HEAPU8[v[_]+17]=n.pElement[_].pStruct.chRegR,z.HEAPU8[v[_]+18]=n.pElement[_].pStruct.chRegG,z.HEAPU8[v[_]+19]=n.pElement[_].pStruct.chRegB;else if(oe==n.pElement[_].nStructType){if(z.HEAPU8[v[_]+0]=n.pElement[_].pStruct.chType,z.HEAPU8[v[_]+1]=n.pElement[_].pStruct.chCount,z.HEAPU8[v[_]+2]=n.pElement[_].pStruct.chWidth,z.HEAPU8[v[_]+3]=n.pElement[_].pStruct.chStyle,z.HEAPU8[v[_]+4]=n.pElement[_].pStruct.chLineA,z.HEAPU8[v[_]+5]=n.pElement[_].pStruct.chLineR,z.HEAPU8[v[_]+6]=n.pElement[_].pStruct.chLineG,z.HEAPU8[v[_]+7]=n.pElement[_].pStruct.chLineB,n.pElement[_].pStruct.chCount>0){h[_]=z._malloc(4*n.pElement[_].pStruct.chCount);for(var P=0;P<n.pElement[_].pStruct.chCount;P++)z.HEAPU16[h[_]/2+2*P]=n.pElement[_].pStruct.pPoints[P].x,z.HEAPU16[h[_]/2+2*P+1]=n.pElement[_].pStruct.pPoints[P].y;z.HEAPU32[v[_]/4+2]=h[_]}}else if(ae==n.pElement[_].nStructType){if(z.HEAPU8[v[_]+0]=n.pElement[_].pStruct.chType,z.HEAPU8[v[_]+1]=n.pElement[_].pStruct.chCount,z.HEAPU8[v[_]+2]=n.pElement[_].pStruct.chWidth,z.HEAPU8[v[_]+3]=n.pElement[_].pStruct.chStyle,z.HEAPU8[v[_]+4]=n.pElement[_].pStruct.chLineA,z.HEAPU8[v[_]+5]=n.pElement[_].pStruct.chLineR,z.HEAPU8[v[_]+6]=n.pElement[_].pStruct.chLineG,z.HEAPU8[v[_]+7]=n.pElement[_].pStruct.chLineB,z.HEAPU8[v[_]+8]=n.pElement[_].pStruct.chRegA,z.HEAPU8[v[_]+9]=n.pElement[_].pStruct.chRegR,z.HEAPU8[v[_]+10]=n.pElement[_].pStruct.chRegG,z.HEAPU8[v[_]+11]=n.pElement[_].pStruct.chRegB,n.pElement[_].pStruct.chCount>0){h[_]=z._malloc(4*n.pElement[_].pStruct.chCount);for(P=0;P<n.pElement[_].pStruct.chCount;P++)z.HEAPU16[h[_]/2+2*P]=n.pElement[_].pStruct.pPoints[P].x,z.HEAPU16[h[_]/2+2*P+1]=n.pElement[_].pStruct.pPoints[P].y;z.HEAPU32[v[_]/4+3]=h[_]}}else ie==n.pElement[_].nStructType&&(z.HEAPU8[v[_]+0]=n.pElement[_].pStruct.chType,z.HEAPU8[v[_]+1]=n.pElement[_].pStruct.chCharset,z.HEAPU16[v[_]/2+2]=n.pElement[_].pStruct.stringPos.x,z.HEAPU16[v[_]/2+3]=n.pElement[_].pStruct.stringPos.y,z.HEAPU8[v[_]+8]=n.pElement[_].pStruct.chLineA,z.HEAPU8[v[_]+9]=n.pElement[_].pStruct.chLineR,z.HEAPU8[v[_]+10]=n.pElement[_].pStruct.chLineG,z.HEAPU8[v[_]+11]=n.pElement[_].pStruct.chLineB,z.HEAPU8[v[_]+12]=n.pElement[_].pStruct.chFontSize,z.HEAPU8[v[_]+13]=n.pElement[_].pStruct.chFontAlign,z.HEAPU16[v[_]/2+6]=n.pElement[_].pStruct.wTxtLen,y[_]=z._malloc(n.pElement[_].pStruct.wTxtLen),z.writeArrayToMemory(n.pElement[_].pStruct.stringDataArray,y[_]),z.HEAPU32[v[_]/4+4]=y[_]);z.HEAP32[S/4+2]=v[_],S+=12}if(z.HEAP32[i/4+2]=g,z.HEAP16[i/2+6]=n.wInfoLen,n.wInfoLen>0){var E=z._malloc(n.wInfoLen);z.writeArrayToMemory(n.pInfo,E),z.HEAP32[i/4+4]=E}a=z._DRAW_InputTrackDataEx2(e,t,i,r,o);for(_=0;_<n.chCount;_++)re==n.pElement[_].nStructType||(oe==n.pElement[_].nStructType||ae==n.pElement[_].nStructType?n.pElement[_].pStruct.chCount>0&&z._free(h[_]):ie==n.pElement[_].nStructType&&z._free(y[_])),z._free(v[_]);z._free(g),n.wInfoLen>0&&z._free(InfoPtr)}else z.writeArrayToMemory(n,i),a=z._DRAW_InputTrackDataEx2(e,t,i,r,o);z._free(i)}(E,t,e,n,r)}}},SetLifeCount:function(e){v&&function(e,t){z._DRAW_SetLifeCount(e,2,t)}(E,e)},DrawDrawIVS:function(e){v&&ue(E,e)},SetPanoVRMode:function(e,t,n,r){return 0===e?q(s,1,!1):(R=!0,D=!1,I={PanoARMode:e,ImageStride:n,ImageWidth:n,ImageHeight:r,PanoARCustomParams:t}),!0},GetModelRotate:function(){return function(e){var t=N._malloc(4),n=N._malloc(4),r=N._malloc(4);N._RENDER_3DGetModelRotate(e,t,n,r);var o=N.HEAPF32[t/4],a=N.HEAPF32[n/4],i=N.HEAPF32[r/4];return N._free(t),N._free(n),N._free(r),{x:o,y:a,z:i}}(s)},SetModelRotate:function(e,t,n){return function(e,t,n,r){return N._RENDER_3DSetModelRotate(e,t,n,r)}(s,e,t,n)},SetStereoPerspectiveFovy:function(e){return function(e,t){return N._RENDER_3DSetPerspectiveFovy(e,t)}(s,e)},GetVRCoord2DTrans:function(e,t){return function(e,t,n){var r=N._malloc(4),o=N._malloc(4);N._RENDER_GetPanoARCoord2DTrans(e,t,n,r,o);var a=N.HEAPF32[r/4],i=N.HEAPF32[o/4];return N._free(r),N._free(o),{x:a,y:i}}(s,e,t)},GetVRCoord3DTrans:function(e,t){return function(e,t,n){var r=N._malloc(4),o=N._malloc(4);N._RENDER_GetPanoARCoord3DTrans(e,t,n,r,o);var a=N.HEAP32[r/4],i=N.HEAP32[o/4];return N._free(r),N._free(o),{x:a,y:i}}(s,e,t)},ResetPlayState:function(){T=!1},GetCurrentFrameID:function(){return w},SetPrintLogLevel:function(e){!function(e){null==N&&null!=window.REModule&&(N=window.REModule),N._RENDER_SetPrintLogLevel(e)}(e),function(e){null==z&&null!=window.IVSModule&&(z=window.IVSModule),z._DRAW_SetPrintLogLevel(e)}(e)},SetPlayMethod:function(e,t,n){U=e,H=t,k=n},SetColor:function(e,t,n,r){return function(e,t,n,r,o){N._RENDER_ColorAdjust(e,4*t-256,2*n,r,45*o/16-180)}(s,e,t,n,r)},SetDisplayRegion:function(e,t){L=t?e:null}},new V};var fe=function(){var e=null,t=null,n=null,r=0,o=0,a=!1,i=0,l=0,u=null,s=!1,c=new Float32Array(8e4),f=0,p=null,d=!1,m=0,g=0,S=null,h=!1,y=.4;function v(t,r){if(d){var a=r-i;(a>200||a<0)&&(o=0,f=0,s=!0,null!==p&&p.stop()),o-e.currentTime<0&&(o=0),i=r;for(var l=new Int16Array(t.buffer,t.byteOffset,t.byteLength/Int16Array.BYTES_PER_ELEMENT),g=new Float32Array(l.length),h=0;h<l.length;h++)g[h]=l[h]/Math.pow(2,15);if(S&&S.InputPlayData(g),c=function(e,t,n){var r=e;n+t.length>=r.length&&(r=new Float32Array(r.length+8e4)).set(r,0);return r.set(t,n),r}(c,g,f),f+=g.length,!s){var y=0;if(f/g.length>1&&(null!==u&&(y=u*m),y>=f||null===u))return void(f=0);var v=null;(v=e.createBuffer(1,f-y,m)).getChannelData(0).set(c.subarray(y,f)),f=0,p=null,(p=e.createBufferSource()).buffer=v,p.connect(n),o||(o=e.currentTime+.01),p.start(o),o+=v.duration}}else o=0}function _(){}return _.prototype={audioInit:function(r){if(o=0,null!==e);else try{return window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(e=new AudioContext).onstatechange=function(){e&&"running"===e.state&&(a=!0)},t=e.createGain(),(n=e.createBiquadFilter()).connect(t),n.type="lowshelf",n.frequency.value=500,n.gain.value=-5,t.connect(e.destination),this.setVolume(r),!0}catch(e){return!1}},play:function(){this.setVolume(r)},stop:function(){r=0,t.gain.value=0,o=0,e=null},bufferAudio:function(t,n){if(!h&&null!==e){var i=o-e.currentTime;(S&&i>y||i>10)&&(this.terminate(),this.audioInit(r))}a&&v(t,0)},setSoundState:function(e){d=e},setVolume:function(e){r=e;var n=e/1;n<=0?(t.gain.value=0,o=0):t.gain.value=n>=1?1:n},setPureAudioFlag:function(e){h=e},getVolume:function(){return r},Mute:function(e){if(e)t.gain.value=0,o=0;else{var n=r/1;n<=0?(t.gain.value=0,o=0):t.gain.value=n>=1?1:n}},terminate:function(){r=0,f=0,o=0,a=!1,h=!1,e&&"closed"!==e.state&&(p=null,n=null,t=null,e.close(),e=null)},setBufferingFlag:function(e,t){"init"===t?l=e:s&&(0===e||null==e?u=null:(u=e-l,l=0),s=!1)},getBufferingFlag:function(){return s},setInitVideoTimeStamp:function(e){l=e},getInitVideoTimeStamp:function(){return l},setSampleBits:function(e,t){m=e,g=t,S&&S.SetPlayParam(m,g)},getAudioBufTime:function(){if(null!==e){var t=o-e.currentTime;return t>0?t*=1e3:t=0,t}return 0},setAudioProcesser:function(e){(S=e)&&S.SetPlayParam(m,g)},setFrameRate:function(e){e<5&&e>0&&(y=2e3/e/1e3)}},new _};var pe=function(e){e=e;var t=null,n="",r=null,o=null,a=null,i=null,l=null,u=null,s=null,c=null,f=1,p={timestamp:0,timestamp_usec:0,timezone:0},d={timestamp:0,timestamp_usec:0,timezone:0},m=null,g=!1,S=null,h=null,y=null,v=!1,_=!0,P=0,E=!1,A=[],b=.5,w=null,C=null,T=null,M=0,R=0,D=!1,I=null,U="png",H=1,k=F(),L=null,O=0,B=0,G=0,V=null,x=!1,N=!1,W=[],Y={},z=25,q=.5,j=!1,K=!1;function X(){}function J(){Q(),K=!0}function Z(){var e=0;if(null!==S)for(e=0;e<S.length;e++)T.updating||T.removeEventListener(S[e].type,S[e].function);if(null!==y)for(e=0;e<y.length;e++)C.removeEventListener(y[e].type,y[e].function);if(null!==h)for(e=0;e<h.length;e++)w.removeEventListener(h[e].type,h[e].function)}function Q(){if(null===C||"ended"===C.readyState)return function(e){(y=[]).push({type:"sourceopen",function:J}),y.push({type:"error",function:ae});for(var t=0;t<y.length;t++)e.addEventListener(y[t].type,y[t].function)}(C=new MediaSource),w.src=window.URL.createObjectURL(C),e.log("videoMediaSource::appendInitSegment new MediaSource()"),!1;if(e.log("videoMediaSource::appendInitSegment start"),0===C.sourceBuffers.length){C.duration=0;var r='video/mp4; codecs="'+n+'"';if(0==n.length)return!1;if(!MediaSource.isTypeSupported(r))return e.log("not support"+r),V&&V({errorCode:101}),s&&s("InitError"),!1;!function(e){(S=[]).push({type:"error",function:ie}),S.push({type:"updateend",function:re}),S.push({type:"update",function:oe});for(var t=0;t<S.length;t++)e.updating||e.addEventListener(S[t].type,S[t].function)}(T=C.addSourceBuffer(r))}var o=t();return null===o?(C.endOfStream("network"),!1):(T.updating?A.push(o):T.appendBuffer(o),e.log("videoMediaSource::appendInitSegment end, codecInfo = "+n),!0)}function $(){w.paused&&(a(),v||x||w.play())}function ee(){w.paused||_||(e.log("pause"),w.pause())}function te(){W.length&&function(t){if(!j&&K&&(j=Q()),null!==T&&"closed"!==C.readyState&&"ended"!==C.readyState)try{if(A.length>0)return e.log("segmentWaitDecode.length: "+A.length),void A.push(t);T.updating?(e.log("updating.........."),A.push(t)):(T.appendBuffer(t),x&&(Y.buffer=t))}catch(t){e.log("videoMediaSource::appendNextMediaSegment error >> initVideo"),A.length=0,V&&V({errorCode:102})}}(W.shift())}function ne(){if(null!==C)try{if(T&&T.buffered.length>0){if(function(){var e=1*T.buffered.start(T.buffered.length-1),t=1*T.buffered.end(T.buffered.length-1);t-e>60&&T.remove(e,t-10)}(),N&&!x||w.duration>q&&(w.currentTime=(w.duration-q).toFixed(3),q+=z<10?.5:.1),w&&w.duration-w.currentTime>8){if(V&&V({errorCode:103}),T.updating)return;var t=w.duration-.1;w.currentTime=t.toFixed(3)}if(E&&!g){var n=1*T.buffered.start(T.buffered.length-1),r=1*T.buffered.end(T.buffered.length-1);if((0===w.currentTime?r-n:r-w.currentTime)>=q+.2){if(e.log("跳秒"),T.updating)return;var o=r-.1;w.currentTime=o.toFixed(3)}}}}catch(t){e.error("sourceBuffer has been removed")}}function re(){A.length>0&&T&&!T.updating&&(T.appendBuffer(A[0]),A.shift())}function oe(){}function ae(){console.log("videoMediaSource::onSourceError"),s&&s("SourceError")}function ie(){console.log("videoMediaSource::onSourceBufferErrormsg"),s&&s("SourceBufferError")}function le(){console.log("videoMediaSource::onError"),ee(),V&&V({errorCode:104}),s&&s("Error")}function ue(){if(v=!0,_=!1,N=!0,!D){e.log("HW Play Start."),D=!0;var t={decodeMode:"video"};t.width=w.videoWidth,t.height=w.videoHeight,1==r?t.encodeMode="H264":2==r&&(t.encodeMode="H265"),l&&l(t)}}function se(){v=!1,_=!0,e.log("硬解码暂停播放")}function ce(){var e=parseInt(C.duration,10),t=parseInt(w.currentTime,10),n={timestamp:p.timestamp-f*(e-t+(1!==f?1:0)),timestamp_usec:0,timezone:p.timezone};0===t||isNaN(e)||!g&&Math.abs(e-t)>4&&1===f||w.paused||(null===m?(m=n,i(0,"currentTime")):(m.timestamp<=n.timestamp&&f>=1||m.timestamp>n.timestamp&&f<1)&&(m=n,++P>4&&i(n.timestamp,"currentTime")))}function fe(){null!=T&&($(),ne())}function pe(){a()}function de(){$()}function me(){if(e.log("需要缓冲下一帧"),E=!1,0==R)M=Date.now(),R++;else{R++;var t=Date.now()-M;e.log("diffTime: "+t+"  Count: "+R),R>=5&&t<6e4&&b<=1&&(b+=.1,R=0,M=0,e.log("delay + 0.1 = "+b))}}function ge(){e.log("Can play !")}function Se(){e.log("Can play without waiting"),E=!0}function he(){e.log("loadedmetadata")}function ye(e,t){var n=document.createElement("canvas");n.width=w.videoWidth,n.height=w.videoHeight;var r=n.getContext("2d");r.drawImage(w,0,0,n.width,n.height);for(var o=0;o<e.length;o++)e[o]&&r.drawImage(e[o],0,0,n.width,n.height);for(var a=n.toDataURL(),i=atob(a.substring("data:image/jpg;base64,".length)),l=new Uint8Array(i.length),s=0,c=i.length;s<c;++s)l[s]=i.charCodeAt(s);u&&u(l);var f=new Blob([l.buffer],{type:"image/jpg"});Ie(f,t),f=null}X.prototype={init:function(t){c=F(),e.log("videoMediaSource::init browserType = "+c),(w=t).autoplay="safari"!==c,w.controls=!1,w.preload="auto",function(e){(h=[]).push({type:"durationchange",function:fe}),h.push({type:"playing",function:ue}),h.push({type:"error",function:le}),h.push({type:"pause",function:se}),h.push({type:"timeupdate",function:ce}),h.push({type:"resize",function:pe}),h.push({type:"seeked",function:de}),h.push({type:"waiting",function:me}),h.push({type:"canplaythrough",function:Se}),h.push({type:"canplay",function:ge}),h.push({type:"loadedmetadata",function:he});for(var t=0;t<h.length;t++)e.addEventListener(h[t].type,h[t].function)}(w),Q()},setInitSegmentFunc:function(e){t=e},getVideoElement:function(){return w},setCodecInfo:function(e){n=e},setMediaSegment:function(e,n){if(n){var r=t();null!==r&&W.push(r)}W.push(e),x||te()},capturePic:function(e,t){I&&clearInterval(I),E||"edge"===k?ye(e,t):I=setInterval((function(){E&&(ye(e,t),clearInterval(I))}),200)},getCapture:function(e,t,n){I&&clearInterval(I),H=n||1,U="png","jpg"!==t&&"jpeg"!==t||(U="jpeg");var r=document.createElement("canvas"),o=null;return r.width=w.videoWidth,r.height=w.videoHeight,(E||"edge"===k||E)&&(r.getContext("2d").drawImage(w,0,0,r.width,r.height),o=r.toDataURL("image/"+U,H)),o},setInitSegment:function(){Q()},ResetInitSegmentFlag:function(){j=!1},setTimeStamp:function(e,t){o=e},setVideoSizeCallback:function(e){a=e},setAudioStartCallback:function(e){i=e},setMseErrorCallback:function(e){s=e},getPlaybackTimeStamp:function(){return o},setPlaySpeed:function(e){f!=e&&(f=e,w.playbackRate=e)},setvideoTimeStamp:function(e){var t=Math.abs(p.timestamp-e.timestamp)>3;d.timestamp,!0===t&&(P=0,i((d=e).timestamp,"init"),0!==p.timestamp&&g&&(w.currentTime=C.duration-.1),m=null),p=e},pause:function(){x=!0,ee()},play:function(){x=!1},setPlaybackFlag:function(e){g=e},setTimeStampInit:function(){m=null,d={timestamp:0,timestamp_usec:0,timezone:0}},close:function(){Z(),ee()},setBeginDrawCallback:function(e){l=e},setCapturePicDataCallBack:function(e){u=e},setErrorCallback:function(e){V=e},terminate:function(){null!==w&&(Z(),"open"===C.readyState&&(T&&C.removeSourceBuffer(T),C.endOfStream()),T=null,C=null,w=null,I&&(clearInterval(I),I=null),L&&(clearInterval(L),L=null),G=0,B=0,O=0,j=!1,K=!1,D=!1)},getDuration:function(){return w.duration-w.currentTime},setFPS:function(e){e&&(z=e)},setRtspOver:function(){w.duration.toFixed(4)-0==w.currentTime.toFixed(4)-0||(O=parseInt(w.currentTime),B=parseInt(w.duration),L=setInterval((function(){O===parseInt(w.currentTime)&&B===parseInt(w.duration)?G++>10&&(L&&clearInterval(L),L=null):parseInt(w.currentTime)>=parseInt(w.duration)?(L&&clearInterval(L),L=null):(O=parseInt(w.currentTime),B=parseInt(w.duration),G=0)}),150))},getVideoBufferQueueSize:function(){return W.length},playNextFrame:function(){te()},getCurFrameInfo:function(){var e;return Y.src=((e=document.createElement("canvas")).width=w.videoWidth,e.height=w.videoHeight,e.getContext("2d").drawImage(w,0,0,e.width,e.height),e.toDataURL()),Y},setDecodeType:function(e){r=e},ResetPlayState:function(){D=!1}};var ve,_e,Pe,Ee,Ae,be,we,Ce,Te,Me,Re,De,Ie=(ve=window,_e=ve.document,Pe=function(){return ve.URL||ve.webkitURL||ve},Ee=_e.createElementNS("http://www.w3.org/1999/xhtml","a"),Ae="download"in Ee,be=/constructor/i.test(ve.HTMLElement),we=/CriOS\/[\d]+/.test(navigator.userAgent),Ce=function(e){(ve.setImmediate||ve.setTimeout)((function(){throw e}),0)},Te=function(e){setTimeout((function(){"string"==typeof e?Pe().revokeObjectURL(e):e.remove()}),4e4)},Me=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},De=(Re=function(e,t,n){n||(e=Me(e));var r,o=this,a="application/octet-stream"===e.type,i=function(){!function(e,t,n){for(var r=(t=[].concat(t)).length;r--;){var o=e["on"+t[r]];if("function"==typeof o)try{o.call(e,n||e)}catch(e){Ce(e)}}}(o,"writestart progress write writeend".split(" "))};if(o.readyState=o.INIT,Ae)return r=Pe().createObjectURL(e),void setTimeout((function(){var e,n;Ee.href=r,Ee.download=t,e=Ee,n=new MouseEvent("click"),e.dispatchEvent(n),i(),Te(r),o.readyState=o.DONE}));!function(){if((we||a&&be)&&ve.FileReader){var t=new FileReader;return t.onloadend=function(){var e=we?t.result:t.result.replace(/^data:[^;]*;/,"data:attachment/file;");ve.open(e,"_blank")||(ve.location.href=e),e=void 0,o.readyState=o.DONE,i()},t.readAsDataURL(e),void(o.readyState=o.INIT)}r||(r=Pe().createObjectURL(e)),a?ve.location.href=r:ve.open(r,"_blank")||(ve.location.href=r),o.readyState=o.DONE,i(),Te(r)}()}).prototype,"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=Me(e)),navigator.msSaveOrOpenBlob(e,t)}:(De.readyState=De.INIT=0,De.WRITING=1,De.DONE=2,De.error=De.onwritestart=De.onprogress=De.onwrite=De.onabort=De.onerror=De.onwriteend=null,function(e,t,n){return null==t||null==t?null:new Re(e,t||e.name||"download",n)}));return new X};function de(e){return(de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function me(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ge(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?me(Object(n),!0).forEach((function(t){Se(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):me(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Se(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=de(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=de(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==de(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function he(e,t){return t=(t=t.toLowerCase())[0].toUpperCase()+t.substr(1),Object.prototype.toString.call(e)==="[object "+t+"]"}function ye(e,t,n){if(void 0===n&&(n=2),void 0===t&&(t=0),(e=e.toString()).length>=n)return e;var r=n-e.length;return new Array(r).fill(String(t)).join("")+e}function ve(e,t){return void 0!==e&&e?(t=t||new Date,e=(e=(e=(e=(e=(e=e.replace(/y/gi,ye(t.getFullYear()),0)).replace(/m/gi,ye(t.getMonth()+1),0)).replace(/d/gi,ye(t.getDate()),0)).replace(/h/gi,ye(t.getHours()),0)).replace(/i/gi,ye(t.getMinutes()),0)).replace(/s/gi,ye(t.getSeconds()),0)):""}function _e(e,t){var n=(e=e||{}).nameFormat||["ymd_his"];t=t||new Date;var r="";if(he(n,"string"))n=[n,{}];else{if(!he(n,"array"))return void function(e){throw new Error(e)}("name format must be string or array");he(n[0],"string")||(n[0]="ymd_his"),he(n[1],"object")||(n[1]={})}var o=n[0].split(/\{(?:[^{}]+)\}/),a=n[1];n[0].replace(/\{([^{}]*)\}/g,(function(e,t,n){o.shift();r+=ve(),r+=t in a?a[t]:e}));var i=o.shift();return r+=ve(i,t)}function Pe(e,t){this.name=e,this.allowUpDateName=!0,this.byteLength=0,this.options=t,this.startTime=(new Date).toLocaleString()}Pe.prototype.setEndTime=function(){this.endTime=(new Date).toLocaleString()},Pe.prototype.updateNameByStream=function(e,t){if(this.allowUpDateName){var n=new Uint8Array(t),r=(n[19]<<24)+(n[18]<<16)+(n[17]<<8)+n[16]>>>0,o="20"+(r>>26)+"/"+(r>>22&15)+"/"+(r>>17&31)+" "+(r>>12&31)+":"+(r>>6&63)+":"+(63&r);this.name=_e(e,new Date(o)),this.allowUpDateName=!1,n=null}t=null};var Ee=new function(){var e={count:0,total:0,group:[]},t=function(){};return t.prototype.add=function(t){e.count++,e.total+=t.byteLength,e.group.push(t)},t.prototype.get=function(t){return t in e?e[t]:e},new t};var Ae,be=function(){var e=1048576,t=null,n=null,r=0,o=void 0,a=null,i=0,l=null,u=!1;function s(){this.onMessage=function(){},this.postMessage=function(e){this.__onMessage(e)},this.__postMessage=function(e){this.onMessage(e)}}return s.prototype.__onMessage=function(e){var t=e;switch(t.type){case"init":this.init(t.options);break;case"addBuffer":this.addBuffer(t);break;case"close":this.close()}},s.prototype.init=function(t){this.fullSize=t.fullSize||1/0,this.singleSize=t.singleSize+20*e||520*e,o="init",u=!1,l=t.recordName,this.limitOptions=Object.assign({limitBy:"fullSize"},t.limitOptions),this.nameOptions=Object.assign({namedBy:"date",nameFormat:["ymd_his",{}]},t.nameOptions)},s.prototype._malloc=function(e){t&&n&&(n=null,t=null),t=new ArrayBuffer(e),n=new DataView(t);var r=this.nameOptions,o="";if(null!=l)o=l;else switch(this.nameOptions.namedBy.toLowerCase()){case"date":o=_e(r);break;default:o=_e()}a=new Pe(o)},s.prototype._initVideoMem=function(){!t&&this.singleSize&&this._malloc(this.singleSize)},s.prototype.appendVideoBuf=function(t,o,a){var l=t.byteLength,s=0;if(5==i){s=o+l;for(var c=o;c<s;c++)n.setUint8(c,t[c-o]);s>r&&(r=s)}else{s=r+l;for(c=r;c<s;c++)n.setUint8(c,t[c-r]);r=s}this.__postMessage({type:"pendding",size:r,total:this.singleSize}),s>this.singleSize-20*e&&!u&&(u=!0,this.__postMessage({type:"close"}))},s.prototype.addBuffer=function(e){if("closed"!==o){var t=e.buffer,n=e.offset;i=e.recordType,this._initVideoMem(),o="addBuffer";var a=t.length,l=0;l=5==i?n+a:r+a,Ee.get("total")+l>this.fullSize?this.close():this.appendVideoBuf(t,n)}},s.prototype.inNodePlace=function(){if("addBuffer"===o){o="download",a.updateNameByStream(this.nameOptions,t.slice(0,20)),a.byteLength=r,a.setEndTime(),Ee.add(a);var e=t.slice(0,r);if(this.reset(),this.__postMessage({type:"download",data:ge(ge({},a),{},{buffer:e})}),e=null,"count"===this.limitOptions.limitBy){var n=this.limitOptions.count;n&&n===Ee.get("count")&&this.close()}}},s.prototype.reset=function(){r=0,this._malloc(this.singleSize)},s.prototype.close=function(){this.inNodePlace(),"closed"!==o&&void 0!==o&&(o="closed",this.__postMessage({type:"closed",message:"record was closed"}),t=null,n=null)},new s},we=function(e){var t=e,n=[255,511,1023,2047,4095,8191,16383,32767];function r(){}function o(e){var t,r;return e<0?(e=132-e,t=127):(e+=132,t=255),(r=a(e,n))>=8?127^t:(r<<4|e>>r+3&15)^t}function a(e,t){for(var n=0,r=t.length;n<r;n++)if(e<=t[n])return n;return t.length}function i(e){var t,r,o;return e>=0?t=213:(t=85,e=-e-8),(r=a(e,n))>=8?127^t:(o=r<<4,(o|=r<2?e>>4&15:e>>r+3&15)^t)}return r.prototype={setSampleRate:function(e){e},encode:function(e){for(var n=new Int16Array(e.buffer),r=new Uint8Array(n.length),a=0;a<n.length;a++)r[a]=0==t?i(n[a]):o(n[a]);return r}},new r},Ce=function(e,t){var n=e,r=t,o=null,a=null,i=[36,n,0,0,0,0],l=[68,72,65,86],u=[100,104,97,118],s=245,c=0,f=null;function p(e,t,n){var r=[],o=t||4;if(!0===n)for(var a=0;a<o;a++)r[a]=e>>>8*(o-1-a)&255;else for(var i=0;i<o;i++)r[i]=e>>>8*i&255;return r}function d(){a=new we(r)}return d.prototype={setSampleRate:function(e){a.setSampleRate(e)},setRtpInterlevedID:function(e){n=e},getRTPPacket:function(e){var t=a.encode(e),d=0;(o=new Uint8Array(i.length+40+t.length+8)).set([36,n],d),d+=2,o.set(p(40+t.length+8,4,!0),d),d+=4,o.set(l,d),d+=4,o.set([240],d),d+=1,o.set([0],d),d+=1,o.set([1],d),d+=1,o.set([0],d),d+=1,s>65535&&(s=240),o.set(p(s),d),d+=4,s++;var m=p(40+t.length+8);o.set(m,d),d+=4;var g=new Date,S=(g.getFullYear()-2e3<<26)+(g.getMonth()+1<<22)+(g.getDate()<<17)+(g.getHours()<<12)+(g.getMinutes()<<6)+g.getSeconds(),h=g.getTime(),y=null===f?0:h-f;f=h,(c+=y)>65535&&(c=65535-c),o.set(p(S),d),d+=4,o.set(p(c,2),d),d+=2,o.set([16],d),d+=1;var v=function(e,t){for(var n=0,r=t;r<e.length;r++)n+=e[r];return n}(o,6);o.set([v],d),d+=1;var _=[131,1,0==r?14:10,2];o.set(_,d),d+=4;o.set([150,1,0,0],d),d+=4;var P=function(e,t){for(var n=0,r=0;r<t;r++)n+=e[r]<<r%4*8;return n}(t,t.length);return o.set([136],d),d+=1,o.set(p(P),d),d+=4,o.set([0,0,0],d),d+=3,o.set(t,d),d+=t.length,o.set(u,d),d+=4,o.set(m,d),o},terminate:function(){a=null}},new d(e)};function Te(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],u=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Me(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Me(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Me(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Re=function(e){var t=e,n=null,r=null,o=null,a=8e3,i=16,l=0,u=0,s=null,c=null,f=null,p={};p.pBuffer=new Uint8Array(320),p.nByteOffset=0;var d={};d.pBuffer=new Uint8Array(320),d.nByteOffset=0;var m=null,g=null,S=null,h=null,y=null,v=!1,_=!1;function P(){f=new G(20),c=new G(20)}function E(e){var t=new ArrayBuffer(2*e.length);return function(e,t,n){for(var r=0;r<n.length;r++,t+=2){var o=Math.max(-1,Math.min(1,n[r])),a=o<0?32768*o:32767*o;e.setInt16(t,a,!0)}}(new DataView(t),0,e),t}function A(e,t,n){var r=Te(function(e,t,n){var r=null;if(n===t)return[e,r];if(n>t)return console.error("The dstRate show be smaller than srcRate"),[e,r];for(var o=t/n,a=Math.floor(e.length/o),i=new Float32Array(a),l=0,u=0;l<i.length;){for(var s=Math.round((l+1)*o),c=0,f=0,p=u,d=e.length;p<s&&p<d;p++)c+=e[p],f++;i[l]=c/f,l++,u=s}if(Math.round(l*o)!==e.length){var m=Math.round(l*o);r=new Float32Array(e.subarray(m,e.length))}return[i,r]}(e,t,n),2);return[r[0],r[1]]}return P.prototype={SetPlayParam:function(e,t){0!=e&&0!=t&&(l=e,u=t,n&&this.StopProcess(),this.Init(),this.Config(a,i,l,u),this.StartProcess())},SetCaptureParam:function(e,t){a=e,i=t},Init:function(){var e;return e=(Ae=window.ASPLiteModule)._malloc(4),0!=Ae._Audio_Framework_Init(e)&&(Ae._free(e),e=null),null!=(n=e)},Config:function(e,t,a,i){var l=Te(function(e,t,n,r,o){if(null==e)return[null,null];var a=Ae._malloc(96),i=Ae._malloc(668),l=new Uint8Array(Ae.HEAPU8.buffer),u=0;"ASPLite.cfg".split("").forEach((function(e,t){l[i+u+4>>0]=e.charCodeAt(0),u++}));var s=t;return t>r&&(s=r),Ae.HEAP32[i/4+129]=2,Ae.HEAP32[i/4+130]=s,Ae.HEAP32[i/4+132]=1,Ae.HEAP32[i/4+133]=16,Ae.HEAP32[i/4+138]=1,Ae.HEAP32[i/4+140]=0,Ae.HEAP32[i/4+141]=0,0!=Ae._Audio_Framework_Config(e,i)&&(Ae._free(i),i=null,Ae._free(a),a=null),[i,a]}(n,e,0,a),2);r=l[0],o=l[1]},DeInit:function(){var e,t,a;t=r,a=o,null!=(e=n)&&(Ae._Audio_Framework_DeInit(e,t,a),Ae._free(e),Ae._free(t),Ae._free(a)),n=null,r=null,o=null,v=!1},InputCaptureData:function(e,r){var o=null;null!==m?((o=new Float32Array(e.length+m.length)).set(m,0),o.set(e,m.length)):o=e;var a=Te(A(o,r,8e3),2),i=a[0],l=a[1];m=l;new Uint8Array(i.buffer);var u=E(i),s=new Uint8Array(u);null!=n&&t?function e(t,n){if(n<=0)return;var r=320-p.nByteOffset,o=n>r?r:n,a=null;a=n>=320&&0==p.nByteOffset?t.slice(0,320):p.nByteOffset>0?t.slice(0,320-p.nByteOffset):t;if(p.pBuffer.set(a,p.nByteOffset),p.nByteOffset+=o,320==p.nByteOffset){var i=new Uint8Array(320);i.set(p.pBuffer),c.enqueue(i),p.pBuffer.fill(0),p.nByteOffset=0}S=t.slice(o),n-=o,e(S,S.length)}(s,s.length):y&&y(s)},InputPlayData:function(e){if(_){var t=null;null!==g?((t=new Float32Array(e.length+g.length)).set(g,0),t.set(e,g.length)):t=e;var n=Te(A(t,l,8e3),2),r=n[0],o=n[1];g=o;new Uint8Array(r.buffer);var a=E(r),i=new Uint8Array(a);!function e(t,n){if(n<=0)return;var r=320-d.nByteOffset,o=n>r?r:n,a=null;a=n>=320&&0==d.nByteOffset?t.slice(0,320):d.nByteOffset>0?t.slice(0,320-d.nByteOffset):t;if(d.pBuffer.set(a,d.nByteOffset),d.nByteOffset+=o,320==d.nByteOffset){var i=new Uint8Array(320);i.set(d.pBuffer),f.enqueue(i),d.pBuffer.fill(0),d.nByteOffset=0}h=t.slice(o),n-=o,e(h,h.length)}(i,i.length)}else f.clear()},StartProcess:function(){_=!0,s=setInterval((function(){!function(){if(null!==c&&0==c.size)return;if(!v&&f.size>5){v=!0;for(var e=0;e<f.size-1;e++)f.dequeue()}var t={};if(f.size>0)t=f.dequeue();else{if(!(c.size>5))return;t.buffer=new Uint8Array(320)}var r=c.dequeue(),a={};a.data=r.buffer,a.fs=8e3,a.datalen=320,a.depth=16,a.offset=2,a.channels=1,a.buflen=320;var i={};i.data=t.buffer,i.fs=8e3,i.datalen=320,i.depth=16,i.offset=2,i.channels=1,i.buflen=320;var l={},u=new ArrayBuffer(a.datalen),s=new Uint8Array(u);l.data=s,0==function(e,t,n,r,o){if(null==e)return-1;var a=Ae._malloc(232),i=Ae._malloc(116),l=Ae._malloc(t.datalen);Ae.writeArrayToMemory(t.data,l);var u=Ae._malloc(n.datalen);Ae.writeArrayToMemory(n.data,u);var s=Ae._malloc(t.datalen);Ae.HEAP32[a/4+0]=l,Ae.HEAP32[a/4+1]=t.fs,Ae.HEAP32[a/4+2]=t.datalen,Ae.HEAP32[a/4+3]=t.depth,Ae.HEAP32[a/4+4]=t.offset,Ae.HEAP32[a/4+5]=t.channels,Ae.HEAP32[a/4+6]=t.buflen,Ae.HEAP32[a/4+29]=u,Ae.HEAP32[a/4+30]=n.fs,Ae.HEAP32[a/4+31]=n.datalen,Ae.HEAP32[a/4+32]=n.depth,Ae.HEAP32[a/4+33]=n.offset,Ae.HEAP32[a/4+34]=n.channels,Ae.HEAP32[a/4+35]=n.buflen,Ae.HEAP32[i/4+0]=s;var c=Ae._Audio_Framework_Main(e,a,i,o);return 0==c&&r.data.set(Ae.HEAPU8.subarray(s,s+t.datalen)),Ae._free(a),Ae._free(i),Ae._free(l),Ae._free(u),Ae._free(s),c}(n,a,i,l,o)&&y&&y(l.data)}()}),10)},StopProcess:function(){this.DeInit(),s&&(clearInterval(s),s=null),c.clear(),f.clear(),m=null,g=null,_=!1},SetProcessedDataCallback:function(e){y=e}},new P},De=function(e,t){var n=e,r=null,o=null,a=null,i=null,l=null,u=!1,s=null,c=t,f={audio:!0,video:!1},p=null;function d(){}function m(e){var t=r.getRTPPacket(e);p(t,5)}return d.prototype={initAudioOut:function(e){if(null==o)try{window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(o=new AudioContext).onstatechange=function(){}}catch(e){return}if(n.SetProcessedDataCallback(m),null!==a&&null!==i||(a=o.createGain(),(i=o.createScriptProcessor(1024,1,1)).onaudioprocess=function(e){if(null!==s){var t=e.inputBuffer.getChannelData(0);null!==p&&!0===u&&n.InputCaptureData(t,o.sampleRate)}},a.connect(i),i.connect(o.destination),l=o.sampleRate,a.gain.value=1),void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e,t,n){var r=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return r?new Promise((function(t,n){r.call(navigator,e,t,n)})):(n(),Promise.reject(new Error("getUserMedia is not implemented in this browser")))}),navigator.mediaDevices.getUserMedia)return navigator.mediaDevices.getUserMedia(f).then((function(e){s=e,o.createMediaStreamSource(e).connect(a)})).catch((function(e){})),u=!0,r||(r=new Ce(c,e)).setSampleRate(l),l},controlVolumnOut:function(e){var t=e/20*2;a.gain.value=t<=0?0:t>=10?10:t},stopAudioOut:function(){if(null!==s&&u)try{for(var e=s.getAudioTracks(),t=0,n=e.length;t<n;t++)e[t].stop();u=!1,s=null}catch(e){}},terminate:function(){this.stopAudioOut(),o.close(),a=null,i=null,r&&(r.terminate(),r=null)},setSendAudioTalkBufferCallback:function(e){p=e},setRtpInterlevedID:function(e){c=e,r&&r.setRtpInterlevedID(e)}},new d},Ie=function(e){var t=0,n=[],r=null,o=null,a=window.H264EncModule,i=document.getElementById("video-capture"),l={audio:!1,video:e||{width:320,height:240,frameRate:15,facingMode:"user"}};window.cPlusH264EncCallBack=function(n,r,i){var l=new ArrayBuffer(r),g=new Uint8Array(l);if(g.set(a.HEAPU8.subarray(n,n+r)),o){var S=function(n,r){var o=n,a=new Uint8Array(u.length+44+o.length+8),i=0;a.set([36,t],i),i+=2,a.set(m(44+o.length+8,4,!0),i),i+=4,a.set(s,i),i+=4,1==r?(a.set([253],i),i+=1):(a.set([252],i),i+=1);a.set([0],i),i+=1,a.set([0],i),i+=1,a.set([0],i),i+=1,f>65535&&(f=240);a.set(m(f),i),i+=4,f++;var l=m(44+o.length+8);a.set(l,i),i+=4;var g=new Date,S=g.getFullYear()-2e3,h=g.getMonth()+1,y=g.getDate(),v=g.getHours(),_=g.getMinutes(),P=g.getSeconds(),E=(S<<26)+(h<<22)+(y<<17)+(v<<12)+(_<<6)+P,A=g.getTime(),b=null===d?0:A-d;d=A,(p+=b)>65535&&(p=65535-p);a.set(m(E),i),i+=4,a.set(m(p,2),i),i+=2,a.set([20],i),i+=1;var w=function(e,t){for(var n=0,r=t;r<e.length;r++)n+=e[r];return n}(a,6);a.set([w],i),i+=1;var C=[129,30,8,e.frameRate];a.set(C,i),i+=4;a.set([130,0,0,0],i),i+=4,a.set(m(e.height,2),i),i+=2,a.set(m(e.width,2),i),i+=2;var T=function(e,t){for(var n=0,r=0;r<t;r++)n+=e[r]<<r%4*8;return n}(o,o.length);return a.set([136],i),i+=1,a.set(m(T),i),i+=4,a.set([0,0,0],i),i+=3,a.set(o,i),i+=o.length,a.set(c,i),i+=4,a.set(l,i),a}(g,i);o(S,7)}};var u=[36,t,0,0,0,0],s=[68,72,65,86],c=[100,104,97,118],f=245,p=0,d=null;function m(e,t,n){var r=[],o=t||4;if(!0===n)for(var a=0;a<o;a++)r[a]=e>>>8*(o-1-a)&255;else for(var i=0;i<o;i++)r[i]=e>>>8*i&255;return r}function g(){var t=document.createElement("canvas");t.height=e.width,t.width=e.height;var n=Math.floor(1e3/e.frameRate),o=0,u=0,s=t.getContext("2d");!function e(){if(!i.paused&&!i.ended){var c,f,p,d,m,g=Date.now();if(0==o||g-o>=n+u){0!=o&&(u+=n-(g-o)),o=g,s.drawImage(i,0,0,t.width,t.height);var S=s.getImageData(0,0,t.width,t.height),h=new Uint8Array(S.data),y=(c=h,f=h.length,p=S.width,d=S.height,m=l.video.frameRate,null==r&&(r=a._malloc(f)),a.HEAPU8.set(c,r),a._h264Enc(r,f,p,d,m));if(0!=y)return void console.error("H264Encode initial failed, ret:"+y)}requestAnimationFrame(e)}}()}function S(){}return S.prototype={initVideoCapture:function(e,n){t=e,void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e,t,n){var r=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return r?new Promise((function(t,n){r.call(navigator,e,t,n)})):Promise.reject(new Error("getUserMedia is not implemented on this browser"))}),navigator.mediaDevices.getUserMedia?navigator.mediaDevices.getUserMedia(l).then((function(e){i.srcObject=e,i.currentTime=0,i.addEventListener("play",g)})).catch((function(e){console.error("Cannot get User Media"+e)})):console.error("Cannot open local media stream! :: navigator.mediaDevices.getUserMedia is not defined!")},stopVideoPlay:function(){if(null!=i){i.pause(),i.removeAttribute("src"),i.removeEventListener("play",g);var e=a._h264EncClose();0!=e&&console.error("H264EncodeClose failed, ret:"+e)}r&&(a._free(r),r=null)},writeDataOut:function(){var e=new Blob(n,{type:"video/webm"}),t=document.createElement("a");console.warn("initial downloadElem: "+t);var r=window.URL.createObjectURL(e);t.href=r,t.download="download.dav",document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(r)},terminate:function(){this.stopVideoPlay()},setBufferCallBack:function(e){o=e}},new S};function Ue(e){return(Ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function He(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ke(r.key),r)}}function ke(e){var t=function(e,t){if("object"!=Ue(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ue(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ue(t)?t:t+""}var Le=5,Fe=14,Oe=19,Be=22,Ge=1,Ve=2,xe=3,Ne=4;window.WebCodecsVideoFrameMap=new Map;var We=0,Ye=function(){return function(e,t,n){return t&&He(e.prototype,t),n&&He(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.m_VideoFrameIndex=0,this.m_WebCodecsDecoder=null,this.m_playMethod=t,this.m_imageWidth=0,this.m_imageHeight=0,this.m_nFrameRate=25,this.m_bIFrameComming=!1,this.m_WebCodecsVideoFrameScope=We++,this.m_SPSParser=new x(n),this.m_nVideoEncodeType=n,this.errCallback=r}),[{key:"decode",value:function(e,t){if(e.nWidth!==this.m_imageWidth||e.nHeight!==this.m_imageHeight){this.close(),this.init();var n;if(null===this.m_WebCodecsDecoder)return console.log("[Error]the WebCodecsDecoder Create Failed!"),!1;if(null===(n=this.GetCodecString(t)))return console.log("[Warn]GetCodecString failed!"),!1;this.m_WebCodecsDecoder.configure({codec:n,codeWidth:e.nWidth,codeHeight:e.nHeight,hardwareAcceleration:"prefer-hardware"}),this.m_imageWidth=e.nWidth,this.m_imageHeight=e.nHeight,this.m_bIFrameComming=!1}return this.m_playMethod?null!==this.m_WebCodecsDecoder?this.m_bIFrameComming||0===e.nFrameSubType?(this.m_bIFrameComming=!0,this.m_WebCodecsDecoder.decode(new EncodedVideoChunk({type:0===e.nFrameSubType?"key":"delta",timestamp:0,duration:0,data:t})),!0):(console.log("[Error]The first frame is not I in WebCodecsDecoder!"),!1):(console.log("[Error]the WebCodecsDecoder is null!"),!1):(console.log("[Error] the  m_playMethod is null!"),!1)}},{key:"flush",value:function(){this.m_WebCodecsDecoder&&this.m_WebCodecsDecoder.flush()}},{key:"close",value:function(){this.m_WebCodecsDecoder&&this.m_WebCodecsDecoder.close(),this.m_WebCodecsDecoder=null,console.log("[Warn] Decode Closed!")}},{key:"init",value:function(){this.m_WebCodecsDecoder=new VideoDecoder({output:this.OnOutput.bind(this),error:this.OnError.bind(this)})}},{key:"OnOutput",value:function(e){this.m_VideoFrameIndex++>999&&(this.m_VideoFrameIndex=0);var t=this.m_WebCodecsVideoFrameScope+this.m_VideoFrameIndex;window.WebCodecsVideoFrameMap.set(t,e),this.m_playMethod&&this.m_playMethod.drawWebCodecs(t,0,this.m_nFrameRate,e.codedWidth,e.codedHeight,e.colorSpace)}},{key:"OnError",value:function(e){console.log("[Error]HW Decode Failed!"),this.m_WebCodecsDecoder=null,this.errCallback(e)}},{key:"GetCodecString",value:function(e){for(var t=null,n=e.length,r=[],o=0;o<=n;)if(0==e[o])if(0==e[o+1])if(1==e[o+2]){if(r.push(o),o+=3,1==this.m_nVideoEncodeType){if(5==(31&e[o])||1==(31&e[o]))break}else if(2==this.m_nVideoEncodeType&&(38==(255&e[o])||2==(255&e[o])))break}else 0==e[o+2]?o++:o+=3;else o+=2;else o+=1;if(1==this.m_nVideoEncodeType){var a=!1;for(o=0;o<r.length;o++)if(t=e.subarray(r[o]+3,r[o+1]),7==(31&e[r[o]+3])){this.m_SPSParser.parse(t),a=!0;break}return a?"avc1."+this.decimalToHex(this.m_SPSParser.getSpsValue("profile_idc"))+this.decimalToHex(this.m_SPSParser.getSpsValue("profile_compatibility"))+this.decimalToHex(this.m_SPSParser.getSpsValue("level_idc")):null}if(2==this.m_nVideoEncodeType){var i=!1;for(o=0;o<r.length;o++)if(t=e.subarray(r[o]+3,r[o+1]-1),66===(255&e[r[o]+3])){var l=e.subarray(r[o]+5,r[o+1]-1);this.m_SPSParser.parse(l),i=!0;break}return i?this.m_SPSParser.getCodecInfo():null}return null}},{key:"decimalToHex",value:function(e,t){var n=Number(e).toString(16);for(t=null==t?t=2:t;n.length<t;)n="0"+n;return n}}])}(),ze=function(e,t){t=t;var n=0,r={timestamp:0,timestamp_usec:0},o=null,a=null,i=null,l=!1,u=!1,s=null,c=null,f=null,p=null,d=null,m=null,g=1,S="",h=!1,y=!1,v=null,_=0,P=0,E={id:1,samples:null,baseMediaDecodeTime:0},A=0,b=null,w=2,C=0,M=0,R=0,D=1,I=null,U=0,L=null,F=null,O=null,B=null,N=null,W=null,Y=null,z=null,q=null,j=null,K=null,X=null,J=null,Z=null,Q=null,$=null,ee=null,te=null,ne=null,re=null,oe=null,ae=0,ie=0,le=0,ue=0,se=0,de=0,me=null,ge=null,Se=null,he=null,ye=null,ve=null,_e=null,Pe=null,Ee=null,Ae=null,we=null,Ce=!1,Te=0,Me=0,Ue=null,He=0,ke=0,We=e,ze=!0,qe=!1,je=!1,Ke=!1,Xe=0,Je=0,Ze=0,Qe=!1,$e=!1,et=0,tt=null,nt=null,rt=null,ot=null,at=0,it=0,lt=-1,ut=!1,st=!1,ct="",ft=null,pt=null,dt=null,mt=null,gt=10,St=0,ht=0,yt=!1,vt=!1,_t=0,Pt=0,Et=!0,At=0,bt=0,wt=0,Ct=0,Tt=45,Mt=0,Rt=0,Dt=!1,It=!1,Ut=!1,Ht="",kt="",Lt="",Ft=!1,Ot=0,Bt=[{},{},{}],Gt=null,Vt=0,xt=null,Nt=!1,Wt=null,Yt=null;function zt(){}function qt(){return Ue&&Ue.CleanScreen(0,0,0,0),1}function jt(){null!=s&&(s.setDecodeType(ke),s.setCodecInfo(S),s.setInitSegmentFunc(Jt),s.setPlaySpeed(g),s.setFPS(_))}function Kt(){null!=s&&(s.setBeginDrawCallback(F),s.setVideoSizeCallback(Zt),s.setErrorCallback(Z),s.setAudioStartCallback(Xt),s.setMseErrorCallback(Qt),s.setCapturePicDataCallBack(X))}function Xt(e,t){}function Jt(){return v}function Zt(){null!==j&&j(!1)}function Qt(e){switch(e){case"InitError":en();break;case"Error":case"SourceError":case"SourceBufferError":Qe=!0,(++Ze>2||se)&&(Ze=0,en())}}function $t(e){t.log("error name: ".concat(e.name," detail: ").concat(e.message," code: ").concat(e.code))}function en(){if(1==ke?ze=!1:qe=!1,We)Module._PLAY_SetSupportWebMSE(n,ze,qe);else{var e={nType:"SetSupportWebMSE",bSupportH264MSE:ze,bSupportH265MSE:qe};ft&&ft.postMessage(e)}}function tn(){s&&(s.close(),s.terminate(),s=null),u=!1,v=null,c=null,f=null,h=!1,$e=!1,C=0,E={id:1,samples:null,baseMediaDecodeTime:0},A=0,D=1,R=0,U=0,M=0,1==w&&(w=2)}function nn(){var e=1;if(We)e=Module._PLAY_StopDataRecord(n);else{ft&&ft.postMessage({nType:"StopRecord"})}return(We||5!=at)&&tt&&(tt.postMessage({type:"close"}),tt=null),it=0,e}function rn(e,o){if(1!=je&&(je=!0,qt()),lt=e.nFrameID,ae==le&&ie==ue&&e.nEncodeType==He||(0!=le&&(Qe=!0),le=ae,ue=ie,He=e.nEncodeType),_!=P&&(y=!0,t.info("MSE FrameRate change, PlayPort:"+n+", m_nFrameRate:"+_+", m_nPreFrameRate:"+P),P=_),Qe&&(tn(),Qe=!1),0!=$e||0==e.nFrameSubType||18==e.nFrameSubType){if(null==c&&(c=new x(ke)),null==f&&(f=new V(ke)),function(e,t,n){for(var r=null,o=e.length,a=[],i=0;i<=o;)if(0==e[i])if(0==e[i+1])if(1==e[i+2]){if(a.push(i),i+=3,1==ke){if(5==(31&e[i])||1==(31&e[i]))break}else if(2==ke&&(38==(255&e[i])||2==(255&e[i])))break}else 0==e[i+2]?i++:i+=3;else i+=2;else i+=1;var l=0;if(1==ke){for(i=0;i<a.length;i++)switch(r=e.subarray(a[i]+3,a[i+1]),31&e[a[i]+3]){case 1:l=a[i]-1,L=e.subarray(l,e.length);break;case 5:l=a[i]-1,L=e.subarray(l,e.length),h=!1;break;case 7:c.parse(r),d=r,u=!0;break;case 8:m=r}if(!h&&u){h=!0;var g={id:1,width:ae,height:ie,type:"video",profileIdc:c.getSpsValue("profile_idc"),profileCompatibility:0,levelIdc:c.getSpsValue("level_idc"),sps:[d],pps:[m],timescale:1e3,fps:_};v=f.initSegment(g)}}else if(2==ke){for(i=0;i<a.length;i++)switch(r=e.subarray(a[i]+3,a[i+1]-1),255&e[a[i]+3]){case 2:l=a[i]-1,L=e.subarray(l,e.length);break;case 38:l=a[i]-1,L=e.subarray(l,e.length),h=!1;break;case 64:p=r;break;case 66:var y=e.subarray(a[i]+5,a[i+1]-1);c.parse(y),d=r,u=!0;break;case 68:m=r}if(!h&&u){h=!0;var P=c.getSpsValue("general_profile_space"),E=c.getSpsValue("general_tier_flag"),A=c.getSpsValue("general_profile_idc"),b=c.getSpsValue("temporalIdNested");g={id:1,width:ae,height:ie,type:"video",general_profile_flag:P<<6|E<<5|A,general_profile_compatibility_flags:c.getSpsValue("general_profile_compatibility_flags"),general_constraint_indicator_flags:c.getSpsValue("general_constraint_indicator_flags"),general_level_idc:c.getSpsValue("general_level_idc"),chroma_format_idc:c.getSpsValue("chroma_format_idc"),bitDepthLumaMinus8:c.getSpsValue("bitDepthLumaMinus8"),bitDepthChromaMinus8:c.getSpsValue("bitDepthChromaMinus8"),rate_layers_nested_length:11|(1&b)<<2,vps:[p],sps:[d],pps:[m],timescale:1e3,fps:_};v=f.initSegment(g)}}u&&(S=c.getCodecInfo(),s&&s.setCodecInfo(S))}(o,e.nFrameSubType,e.nFrameID),0==$e){null==s&&null!=a&&void 0!==a&&(s=new pe(t)).init(a);var i={decodeMode:"video"};i.width=ae,i.height=ie,[2,4,8].includes(e.nEncodeType)?i.encodeMode="H264":12===e.nEncodeType&&(i.encodeMode="H265"),O&&O(i),Kt(),jt(),$e=!0}st&&(st=!1,s&&s.capturePic([a],ct)),s&&s.setvideoTimeStamp(r),function(){if(null!=L){if(!l&&We){var e=Module._PLAY_GetPlaySpeed(n);s&&s.setPlaySpeed(e)}var t={duration:Math.round(1/_*1e3),size:L.length,frame_time_stamp:null,frameDuration:null};t.frameDuration=t.duration,null==E.samples&&(E.samples=new Array(w)),E.samples[C]=t,C++,M+=t.frameDuration,R+=t.frameDuration;var r=L.length-4;L[0]=(4278190080&r)>>>24,L[1]=(16711680&r)>>>16,L[2]=(65280&r)>>>8,L[3]=255&r;var o=new Uint8Array(L.length+A);if(0!==A&&o.set(b),o.set(L,A),A=(b=o).length,C%w==0&&0!==C){if(null!==E.samples[0].frameDuration&&(E.baseMediaDecodeTime=1===D?0:U,U=M),1==g)for(var a=E.samples.length,i=R/w,u=0;u<a;u++)E.samples[u].frameDuration=i;R=0,I=f.mediaSegment(D,E,b,E.baseMediaDecodeTime),D++,C=0,b=null,A=0,null!==s?(s.setMediaSegment(I,y),y=!1):!1===h&&jt(),2==w&&(w=1,E.samples=null,E.samples=new Array(w))}}}(),Ue&&Ue.DrawDrawIVS(e.nFrameID)}}function on(e,o,a,i,l){St>=l.nTotalStreamLength&&(ht=St-l.nTotalStreamLength);var u,s,c=Date.UTC(l.nYear,l.nMonth,l.nDay,l.nHour,l.nMinute,l.nSecond)/1e3;if(1==l.nFrameType){if(t.log("PlayFrameData, PlayPort:"+n+", nFrameID:"+l.nFrameID+", nRemainData:"+l.nRemainData),de=l.nRemainData,Ke&&0==de&&0==ht&&q(),l.bThrowFrame)return;if(8==l.nStreamType?r.timestamp=c:r.timestamp=l.nTimeStamp/1e3,r.timestamp_usec=0,2==l.nEncodeType||4==l.nEncodeType||8==l.nEncodeType?ke=1:12==l.nEncodeType&&(ke=2),Ue&&Ue.setEncodeType(ke),ae=l.nWidth,ie=l.nHeight,0==ae||0==ie)return;if(_!=l.nFrameRate&&we&&we.setFrameRate(l.nFrameRate),_=l.nFrameRate,Xe<=5&&Xe++,18==l.nFrameSubType||19==l.nFrameSubType||20==l.nFrameSubType?se=1:0==l.nFrameSubType&&(se=0),2==l.nFrameSubType&&!yt){if(yt=!0,1==ke?ze=!1:2==ke&&(qe=!1),We)Module._PLAY_SetSupportWebMSE(n,ze,qe);else{var f={nType:"SetSupportWebMSE",bSupportH264MSE:ze,bSupportH265MSE:qe};ft&&ft.postMessage(f)}return}if(Ue&&Ue.SetLifeCount(3*l.nFrameRate),(1==ke&&1==ze||2==ke&&1==qe)&&13!=l.nStreamType){if(u=l.nFrameID,0!=(s=l.nFrameSubType)&&18!=s&&20!=s&&-1!=lt&&u!=lt+1&&(t.info("checkFrame failed, PlayPort:"+n+", nFrameID:"+u+", m_nLastFrameID:"+lt),1))return;Nt||(Nt=!0,Wt=l),Yt=l,0!==Pt?(lt=l.nFrameID,Gt&&Gt.decode(l,e)):rn(l,e)}else{if(!We&&(null==e||null==o||null==a))return;if(0!=je&&(je=!1,tn(),Qe=!0,le=0,ue=0),ae!=le||ie!=ue)le=ae,ue=ie,We||Ue.resize(ae,ie),(p={decodeMode:"canvas"}).width=ae,p.height=ie,[2,4,8].includes(l.nEncodeType)?p.encodeMode="H264":12===l.nEncodeType&&(p.encodeMode="H265"),O&&O(p);if(Nt||(Nt=!0,Wt=l),Yt=l,lt=l.nFrameID,We){var p;if(!ut)t.log("Multi SW Play Start."),ut=!0,(p={decodeMode:"canvas"}).width=ae,p.height=ie,[2,4,8].includes(l.nEncodeType)?p.encodeMode="H264":12===l.nEncodeType&&(p.encodeMode="H265"),F(p);Ue&&Ue.DrawDrawIVS(l.nFrameID)}else Ue&&Ue.draw(e,o,a,l,_)}var d=l.nYear,m=Pn(l.nMonth),g=Pn(l.nDay),S=Pn(l.nHour),h=Pn(l.nMinute),y=Pn(l.nSecond);l.timeStamp=Date.UTC(d,m,g,S,h,y)/1e3,l.utcTimeStamp=new Date("".concat(d,"-").concat(m,"-").concat(g," ").concat(S,":").concat(h,":").concat(y)).getTime(),l.nSize=i,B(l)}else if(2==l.nFrameType){if(Je<=10&&Je++,!vt&&Xe<5)if(Je<=10){if(_>5||0==_)return;if(Xe<2)return}else 0==Xe&&we&&we.setPureAudioFlag(!0);else Xe>=5&&we&&we.setPureAudioFlag(!1);if(l.nSamples==Te&&l.nBits==Me||(Te=l.nSamples,Me=l.nBits,we&&we.setSampleBits(l.nSamples,l.nBits)),we&&we.bufferAudio(e,0),We&&we){var v=we.getAudioBufTime();setTimeout((function(){Module._PLAY_SetInt32(n,2057,v)}))}l.nSize=i,N(l)}}function an(e,t,n,r,o){if(-1!=o){if(n==Le)if(We||je)o=lt;else{var a=Ue&&Ue.GetCurrentFrameID();if(-1==a||void 0===a)return;o=a}if(We){var i=null,l=new ArrayBuffer(r),u=new Uint8Array(l);u.set(Module.HEAPU8.subarray(t,t+r));var s=new DataView(u.buffer);if(J&&J(e,u,n,r,o),Oe==n){var c={};c.NumberStat=s.getUint16(0,!0),c.nIntelFlowPlanNum=s.getUint16(2,!0);var f=s.getUint32(4,!0),p=new ArrayBuffer(12),d=new Uint8Array(p),m=new DataView(p);c.pIntelFlowPlan=new Array(c.nIntelFlowPlanNum);for(var g=0;g<c.nIntelFlowPlanNum;g++){d.set(Module.HEAPU8.subarray(f+12*g,f+12*g+12)),c.pIntelFlowPlan[g]={},c.pIntelFlowPlan[g].PlanId=m.getUint16(0,!0),c.pIntelFlowPlan[g].RuleType=m.getUint16(2,!0),c.pIntelFlowPlan[g].RegionNum=m.getUint16(8,!0);var S=m.getUint32(4,!0),h=new ArrayBuffer(12),y=new Uint8Array(h),v=new DataView(h);c.pIntelFlowPlan[g].pRegion=new Array(c.pIntelFlowPlan[g].RegionNum);for(var _=0;_<c.pIntelFlowPlan[g].RegionNum;_++)y.set(Module.HEAPU8.subarray(S+12*_,S+12*_+12)),c.pIntelFlowPlan[g].pRegion[_]={},c.pIntelFlowPlan[g].pRegion[_].RegionId=v.getUint16(0,!0),c.pIntelFlowPlan[g].pRegion[_].State=v.getUint16(2,!0),c.pIntelFlowPlan[g].pRegion[_].PeopleNum=v.getUint32(4,!0)}i=c}else if(Be==n){var P={};P.nId=s.getUint32(0,!0),P.wCustom=s.getUint16(4,!0),P.chState=s.getUint8(6,!0),P.chCount=s.getUint8(7,!0);var E=s.getUint32(8,!0),A=new ArrayBuffer(12),b=new Uint8Array(A),w=new DataView(A);P.pElement=new Array(P.chCount);for(var C=0;C<P.chCount;C++){b.set(Module.HEAPU8.subarray(E+12*C,E+12*C+12)),P.pElement[C]={},P.pElement[C].nStructType=w.getUint32(0,!0),P.pElement[C].nStructLength=w.getUint32(4,!0);var T=w.getUint32(8,!0),M=new ArrayBuffer(P.pElement[C].nStructLength),R=new Uint8Array(M),D=new DataView(M);if(R.set(Module.HEAPU8.subarray(T,T+P.pElement[C].nStructLength)),P.pElement[C].pStruct={},Ge==P.pElement[C].nStructType)P.pElement[C].pStruct.chType=D.getUint8(0,!0),P.pElement[C].pStruct.chWidth=D.getUint8(1,!0),P.pElement[C].pStruct.chStyle=D.getUint8(2,!0),P.pElement[C].pStruct.wRadius=D.getUint16(4,!0),P.pElement[C].pStruct.positionCircle={},P.pElement[C].pStruct.positionCircle.x=D.getUint16(8,!0),P.pElement[C].pStruct.positionCircle.y=D.getUint16(10,!0),P.pElement[C].pStruct.chLineA=D.getUint8(12,!0),P.pElement[C].pStruct.chLineR=D.getUint8(13,!0),P.pElement[C].pStruct.chLineG=D.getUint8(14,!0),P.pElement[C].pStruct.chLineB=D.getUint8(15,!0),P.pElement[C].pStruct.chRegA=D.getUint8(16,!0),P.pElement[C].pStruct.chRegR=D.getUint8(17,!0),P.pElement[C].pStruct.chRegG=D.getUint8(18,!0),P.pElement[C].pStruct.chRegB=D.getUint8(19,!0);else if(Ve==P.pElement[C].nStructType){P.pElement[C].pStruct.chType=D.getUint8(0,!0),P.pElement[C].pStruct.chCount=D.getUint8(1,!0),P.pElement[C].pStruct.chWidth=D.getUint8(2,!0),P.pElement[C].pStruct.chStyle=D.getUint8(3,!0),P.pElement[C].pStruct.chLineA=D.getUint8(4,!0),P.pElement[C].pStruct.chLineR=D.getUint8(5,!0),P.pElement[C].pStruct.chLineG=D.getUint8(6,!0),P.pElement[C].pStruct.chLineB=D.getUint8(7,!0);var I=null,U=null,H=null,k=null;P.pElement[C].pStruct.chCount>0&&(P.pElement[C].pStruct.pPoints=new Array(P.pElement[C].pStruct.chCount),I=D.getUint32(8,!0),U=new ArrayBuffer(4),H=new Uint8Array(U),k=new DataView(U));for(var L=0;L<P.pElement[C].pStruct.chCount;L++)H.set(Module.HEAPU8.subarray(I+4*L,I+4*L+4)),P.pElement[C].pStruct.pPoints[L]={},P.pElement[C].pStruct.pPoints[L].x=k.getUint16(0,!0),P.pElement[C].pStruct.pPoints[L].y=k.getUint16(2,!0)}else if(xe==P.pElement[C].nStructType){P.pElement[C].pStruct.chType=D.getUint8(0,!0),P.pElement[C].pStruct.chCount=D.getUint8(1,!0),P.pElement[C].pStruct.chWidth=D.getUint8(2,!0),P.pElement[C].pStruct.chStyle=D.getUint8(3,!0),P.pElement[C].pStruct.chLineA=D.getUint8(4,!0),P.pElement[C].pStruct.chLineR=D.getUint8(5,!0),P.pElement[C].pStruct.chLineG=D.getUint8(6,!0),P.pElement[C].pStruct.chLineB=D.getUint8(7,!0),P.pElement[C].pStruct.chRegA=D.getUint8(8,!0),P.pElement[C].pStruct.chRegR=D.getUint8(9,!0),P.pElement[C].pStruct.chRegG=D.getUint8(10,!0),P.pElement[C].pStruct.chRegB=D.getUint8(11,!0);I=null;var F=null,O=null,B=null;P.pElement[C].pStruct.chCount>0&&(P.pElement[C].pStruct.pPoints=new Array(P.pElement[C].pStruct.chCount),I=D.getUint32(12,!0),F=new ArrayBuffer(4),O=new Uint8Array(F),B=new DataView(F));for(L=0;L<P.pElement[C].pStruct.chCount;L++)O.set(Module.HEAPU8.subarray(I+4*L,I+4*L+4)),P.pElement[C].pStruct.pPoints[L]={},P.pElement[C].pStruct.pPoints[L].x=B.getUint16(0,!0),P.pElement[C].pStruct.pPoints[L].y=B.getUint16(2,!0)}else if(Ne==P.pElement[C].nStructType){P.pElement[C].pStruct.chType=D.getUint8(0,!0),P.pElement[C].pStruct.chCharset=D.getUint8(1,!0),P.pElement[C].pStruct.stringPos={},P.pElement[C].pStruct.stringPos.x=D.getUint16(4,!0),P.pElement[C].pStruct.stringPos.y=D.getUint16(6,!0),P.pElement[C].pStruct.chLineA=D.getUint8(8,!0),P.pElement[C].pStruct.chLineR=D.getUint8(9,!0),P.pElement[C].pStruct.chLineG=D.getUint8(10,!0),P.pElement[C].pStruct.chLineB=D.getUint8(11,!0),P.pElement[C].pStruct.chFontSize=D.getUint8(12,!0),P.pElement[C].pStruct.chFontAlign=D.getUint8(13,!0),P.pElement[C].pStruct.wTxtLen=D.getUint16(14,!0);var G=D.getUint32(16,!0),V=new ArrayBuffer(P.pElement[C].pStruct.wTxtLen),x=new Uint8Array(V);new DataView(V);x.set(Module.HEAPU8.subarray(G,G+P.pElement[C].pStruct.wTxtLen)),P.pElement[C].pStruct.stringDataArray=x}}if(P.nInfoLen=s.getUint16(12,!0),P.nInfoLen>0){var N=s.getUint32(16,!0),W=new ArrayBuffer(P.nInfoLen),Y=new Uint8Array(W);Y.set(Module.HEAPU8.subarray(N,N+P.nInfoLen)),P.pInfo=Y}i=P}else if(Fe==n){for(var z=new DataView(l),q=r/144,j=[],X=0;X<q;X++){var Z={},Q=144*X;Z.nIndex=z.getInt32(Q+0,!0),Z.xPoint=z.getUint16(Q+4,!0),Z.yPoint=z.getUint16(Q+6,!0);var $=new ArrayBuffer(64);$=l.slice(Q+8),Z.strName=En($),Z.enable=z.getInt8(Q+72,!0),Z.titleType=z.getInt8(Q+73,!0),Z.titleAttribute=z.getInt8(Q+74,!0),Z.sharpType=z.getInt8(Q+75,!0),Z.polygonNum=z.getInt8(Q+76,!0),Z.polygon=[];for(var ee=0;ee<2*Z.polygonNum;ee+=2)Z.polygon[ee]={x:z.getInt8(Q+79+2*ee,!0),y:z.getInt8(Q+79+2*(ee+1),!0)};j[X]=Z,$=null}K&&K(j),z=null,i=u}else i=u;Ue&&Ue.DrawIVS(i,n,r,o)}else J&&J(e,t,n,r,o),Ue&&Ue.DrawIVS(t,n,r,o)}}function ln(e){var t=e.data.msgType,r=e.data.nPort,a=e.data.msgData,i=0,u=0;switch(o&&(i=parseInt(o.width),u=parseInt(o.height)),t){case"LoadSuccess":if(0<Vt){var s={nType:"setPrintLogLevel",nLogLevel:Vt};ft.postMessage(s)}s={nType:"Init",option:{bPlayback:l,bSupportMultiThread:We,bSupportH264MSE:ze,bSupportH265MSE:qe,nCanvasWidth:i,nCanvasHeight:u}};if(ft.postMessage(s),It=!0,Ut||(Ut=!0,0!=Ht.length&&0!=kt.length&&0!=Lt.length&&GetOriginalKey(Ht,kt,Lt)),!Ft)for(var c=0;c<3;c++)void 0!==Bt[c].nFrameType&&SetWebSecurityKey(Ot,Bt[c].nFrameType,Bt[c].strKey,Bt[c].stStreamInfo);break;case"InitSuccess":$(n=r);break;case"VisibleDecCallBack":ee(),on(a.pBufY,a.pBufU,a.pBufV,a.nSize,a.stuFrameInfo);break;case"IVSDataCallBack":an(r,a.pBuf,a.nType,a.nLen,a.nReallen);break;case"RecordDataCallBack":!function(e,t,n,r){it+=t;var o=r.nYear,a=r.nMonth,i=r.nDay,l=r.nHour,u=r.nMinute,s=r.nSecond,c=Date.UTC(o,a,i,l,u,s)/1e3,f=new Date("".concat(o,"-").concat(a,"-").concat(i," ").concat(l,":").concat(u,":").concat(s)).getTime();W&&W({frameType:r.nFrameType,timeStamp:f,utcTimeStamp:c,length:it}),tt&&tt.postMessage({type:"addBuffer",buffer:e,offset:n,recordType:at})}(a.pRecordData,a.nLen,a.Offset,a.stuFrameInfo),a.Offset<et?(tt.postMessage({type:"close"}),tt=null,et=0):et=a.Offset;break;case"DecryptionResultCallBack":a.bSuccess,z(a.bSuccess);break;case"CatchPicCallBack":X&&X(a.buffer);var f=new Blob([a.buffer.buffer],{type:"image/jpg"});_n(f,ct);break;case"GetOriginalKeyCallBack":Y(a);break;case"ARTagInfoCallback":K&&K(a.tagInfo)}}zt.prototype={Init:function(e){o=e.canvasElem,a=e.videoElem,i=e.ivsCanvasElem,l=e.bPlayBack,void 0!==e.nMSEFrameCount&&(numbox=e.nMSEFrameCount);var r=1;Mt=o&&o.width,Rt=o&&o.height,qe=function(){var e=H(),t=k(e),n=!1;switch(e){case T:n=t>=104;break;default:n=0}return n}();var u=navigator.platform;H();if(0==u.indexOf("iPhone")&&(ze=!1,qe=!1),We){var c=Module._malloc(1);if(te=new Uint8Array(Module.HEAPU8.buffer,c,1),Module._PLAY_GetFreePort(te.byteOffset),n=te[0],te=null,Module._free(c),r=Module._PLAY_SetStreamOpenMode(n,l),r=Module._PLAY_OpenStream(n,0,0,10485760),r=Module._PLAY_SetCacheMode(n,1),r=Module._PLAY_SetSupportWebMSE(n,ze,qe),o){Module._PLAY_ViewResolutionChanged(n,parseInt(o.width),parseInt(o.height),0);var f=Module.allocateUTF8(o.id);r=Module._PLAY_Play(n,f),Module._free(f)}else Module._PLAY_ViewResolutionChanged(n,0,0,0),r=Module._PLAY_Play(n,0)}else(ft=new Worker("".concat(e.strDecodeFilePath,"/VideoDecodeWorker.js"))).onmessage=ln,xt=new G(200);(t.log("Init, PlayPort:"+n+", canvasElem:"+e.canvasElem+", videoElem:"+e.videoElem+", ivsCanvasElem:"+e.ivsCanvasElem+", bPlayBack:"+e.bPlayBack),t.log("Init, m_bSupportMultiThread:"+We+", m_bSupportH264MSE:"+ze+", m_bSupportH265MSE:"+qe),r)&&(We&&(ne=Module._malloc(5242880),re=new Uint8Array(Module.HEAPU8.buffer,ne,5242880)),Ue=new ce(o,i,!0===l?100:25,l,We,t),0<Vt&&Ue.SetPrintLogLevel(Vt),(qe||ze)&&a&&((s=new pe(t)).init(a),Kt()));return we=new fe,r},SetCacheMode:function(e){t.log("SetCacheMode, PlayPort:"+n+", nMode:"+e);var r=0;return We&&(r=Module._PLAY_SetCacheMode(n,e)),r},GetPlayPort:function(){return n},InputData:function(e){t.log("InputData, PlayPort:"+n+", length:"+e.length);var r=1;if(We)re&&(re.set(e),r=Module._PLAY_InputData(n,re.byteOffset,e.length));else{St+=e.length;var o={nType:"InputData",pData:e};if(It){for(;xt.size>0;){var a={nType:"InputData",pData:xt.dequeue().buffer};ft.postMessage(a)}ft.postMessage(o)}else xt.enqueue(e)}return r},Pause:function(e){t.log("Pause, PlayPort:"+n+", bPause:"+e);var r=1;if(We)r=Module._PLAY_Pause(n,e);else{var o={nType:"Pause",bPause:e};ft&&ft.postMessage(o)}return r},SetPlaySpeed:function(e){t.log("SetPlaySpeed, PlayPort:"+n+", nSpeed:"+e);var r=1;if(We)r=Module._PLAY_SetPlaySpeed(n,e);else{var o={nType:"SetPlaySpeed",nSpeed:e};ft&&ft.postMessage(o)}return Ue&&Ue.setPlaySpeed(e),s&&s.setPlaySpeed(e),g=e,r},SetSecurityKey:function(e,r,o,a,i){if(t.log("SetSecurityKey, PlayPort:"+n),We){var l=Module._malloc(49),u=new Uint8Array(Module.HEAPU8.buffer),s=0;if(1==e)r.forEach((function(e,t){u[l+s>>0]=e,s++}));else if(2==e){var c=new Uint8Array(16);if(u[l+s>>0]=1,s++,0==i){for(var f=0;f<16;f++)c[f]=0;i=16,a=c}a.forEach((function(e,t){u[l+s>>0]=e,s++})),r.forEach((function(e,t){u[l+s]=e,s++})),o=1+o+i,c=null}else 3==e&&r.split("").forEach((function(e,t){u[l+s>>0]=e.charCodeAt(0),s++}));Module._PLAY_SetSecurityKey(n,l,o),Module._free(l)}else{var p={nType:"SetSecurityKey",nEncryptType:e,szKey:r,nKeyLen:o,szKeyId:a,nKeyIdLen:i};ft&&ft.postMessage(p)}},StartRecord:function(e,r,o){t.log("StartRecord, PlayPort:"+n+", nRecordType:"+e+", nFileSize:"+r+", strRecordName:"+o);var a,i,l=1;if(tt=new be,a=o,i=parseInt(r)||500,tt.postMessage({type:"init",options:{recordName:a,singleSize:1048576*i,nameOptions:{namedBy:"date",nameFormat:["ymd_his"]},limitOptions:{limitBy:"count",count:10}}}),tt.onMessage=function(e){switch(e.type){case"pendding":break;case"download":t=e.data.name,n=e.data.buffer,r=new Blob([n]),(o=document.createElement("a")).href=URL.createObjectURL(r),o.download=t,o.click(),URL.revokeObjectURL(o.href),o=null,n=null;break;case"close":setTimeout((function(){nn()}))}var t,n,r,o},at=e,We)l=Module._PLAY_StartDataRecord(n,0,at);else{et=0;var u={nType:"StartRecord",nRecordType:at};ft&&ft.postMessage(u)}return l},StopRecord:function(){return t.log("StopRecord, PlayPort:"+n),nn()},OpenIVSDraw:function(e){t.log("OpenIVSDraw, PlayPort:"+n+", nWndIndex:"+e);var r=1;if(We)r=Module._PLAY_RenderPrivateData(n,1,0);else{ft&&ft.postMessage({nType:"OpenIVSDraw"})}return Ue&&Ue.OpenIVS(e,i.id),r},CloseIVSDraw:function(){t.log("CloseIVSDraw, PlayPort:"+n);var e=1;if(We)e=Module._PLAY_RenderPrivateData(n,0,0);else{ft&&ft.postMessage({nType:"CloseIVSDraw"})}return Ue&&Ue.CloseIVS(),e},SetIvsEnable:function(e,r){t.log("SetIvsEnable, PlayPort:"+n+", nIvsType:"+e+", bEnable:"+r),Ue&&Ue.SetIvsEnable(e,r)},SetIvsLanguageEnvi:function(e){t.log("SetIvsLanguageEnvi, PlayPort:"+n+", nLanguageType:"+e),Ue&&Ue.SetIvsLanguageEnvi(e)},SetPanoAR:function(e,r){t.log("SetPanoAR, PlayPort:"+n+", PanoARType:"+e);var o=1;if(We&&!je){var a=null;return null!==r&&9===e&&(a=Module._malloc(12),Module.HEAPF32[a/4+0]=r.VerFieldViewAngle,Module.HEAPF32[a/4+1]=r.HoriFieldViewAngle,Module.HEAPF32[a/4+2]=r.DownPressAngle),o=Module._PLAY_SetPanoVRMode(n,e,a),Pt=e,null!==a&&Module._free(a),o}return 0!=Vt&&Ue&&Ue.SetPrintLogLevel(Vt),0===Pt&&0!==e?je?(Qe=!0,Ue.resize(ae,ie),null===Gt&&(Gt=new Ye(Ue,ke,$t))):(Ue.terminate(),Ue.resize(ae,ie)):0!==Pt&&0===e&&(qt(),Ue.terminate(),je||Ue.resize(ae,ie),Gt=null),Ue.SetPanoVRMode(e,r,ae,ie),Pt=e,o},Set3DPoint:function(e){t.log("Set3DPoint, PlayPort:"+n+", b3DPoint:"+e),We&&(_t=e)},OnMouseDown:function(){0===Pt&&0===_t||(Dt=!0)},OnMouseMove:function(e,t){if(0!==Pt||0!==_t){Et&&(At=e,bt=t,0,0,Et=!1);var r=e-At,o=t-bt;if(At=e,bt=t,Dt){if(We&&!je){var a=Module._malloc(8),i=Module._malloc(8);Module._PLAY_GetDoubleRegion(n,0,2050,a),Module._PLAY_GetDoubleRegion(n,0,2051,i),wt=Module.HEAPF64[a/8],Ct=Module.HEAPF64[i/8],Module._free(a),Module._free(i)}else{if(!Ue)return void console.log("[Error]The m_playMethod is invalid in OnMouseMove!");wt=Ue.GetModelRotate().x,Ct=Ue.GetModelRotate().y}Math.abs(r)>=Math.abs(o)?Ct+=-2*r*Tt/Rt:wt+=-2*o*Tt/Rt,We&&!je?Module._PLAY_SetStereoRotate(n,0,wt,Ct,0):Ue.SetModelRotate(wt,Ct,0)}}},OnMouseUp:function(){0===Pt&&0===_t||(Dt=!1)},OnMouseWheel:function(e){if(0!==Pt||0!==_t)if(e<0?Tt>=10&&(Tt-=2):Tt<=120&&(Tt+=2),We&&!je)Module._PLAY_SetStereoPerspectiveFovy(n,0,Tt);else{if(!Ue)return void console.log("[Error]The m_playMethod is invalid in OnMouseWheel!");Ue.SetStereoPerspectiveFovy(Tt)}},GetVRCoord2DTrans:function(e,t){return function(e,t){var r;if(We&&!je){var o=Module._malloc(4),a=Module._malloc(4);Module._PLAY_GetVRCoord2DTrans(n,0,e,t,o,a),r={x:Module.HEAPF32[o/4],y:Module.HEAPF32[a/4]},Module._free(o),Module._free(a)}else{if(!Ue)return void console.log("[Error]The m_playMethod is invalid in GetVRCoord2DTrans!");r=Ue.GetVRCoord2DTrans(e,t)}return[(r.x+1)/2*i.width,(1-r.y)/2*i.height]}(e,t)},GetVRCoord3DTrans:function(e,t){return function(e,t){var r;if(We&&!je){var o=Module._malloc(4),a=Module._malloc(4);Module._PLAY_GetVRCoord3DTrans(n,0,e,t,o,a),r={x:Module.HEAP32[OutXPtr/4],y:Module.HEAP32[OutYPtr/4]},Module._free(OutXPtr),Module._free(OutYPtr)}else{if(!Ue)return void console.log("[Error]The m_playMethod is invalid in GetVRCoord3DTrans!");r=Ue.GetVRCoord3DTrans(e,t)}return[r.x,r.y]}(e,t)},SetSoundState:function(e){(t.log("SetSoundState, PlayPort:"+n+", bPlay:"+e),e&&!Ce)&&(we.audioInit(1)?Ce=!0:we.stop());we&&we.setSoundState(e)},SetVolume:function(e){t.log("SetVolume, PlayPort:"+n+", nVolume:"+e),we&&we.setVolume(e)},SetRtpInterlevedID:function(e){pt?pt.setRtpInterlevedID(e):gt=e},StartTalk:function(e){if(t.log("StartTalk, PlayPort:"+n+", nEncodeType:"+e),!pt){var r=void 0!==window.ASPLiteModule;dt=new Re(r),(pt=new De(dt,gt)).setSendAudioTalkBufferCallback(Q),pt.initAudioOut(e),we&&r&&we.setAudioProcesser(dt)}},StopTalk:function(){t.log("StopTalk, PlayPort:"+n),pt&&(pt.terminate(),pt=null),we&&we.setAudioProcesser(null),dt&&(dt.StopProcess(),dt=null)},StartVideoCapture:function(e,r,o){t.log("StartVideoCapture, PlayPort:"+n+", nEncodeType:"+r),mt||((mt=new Ie(o)).setBufferCallBack(Q),mt.initVideoCapture(0,0))},StopVideoCapture:function(){t.log("StopVideoCapture, PlayPort:"+n),mt&&(mt.terminate(),mt=null)},GetSourceBufferRemain:function(){var e=0;return e=We?Module._PLAY_GetSourceBufferRemain(n):ht,t.log("GetSourceBufferRemain, PlayPort:"+n+", nRemain:"+e),e},SetStreamOver:function(e){t.log("SetStreamOver, PlayPort:"+n+", bOver:"+e),(Ke=e)&&0==de&&0==ht&&q()},ResetBuffer:function(e){t.log("ResetBuffer, PlayPort:"+n+", nBufType:"+e),We&&Module._PLAY_ResetBuffer(n,e)},capturePic:function(e){if(t.log("capturePic, PlayPort:"+n+", strPictureName:"+e),ct=e,je)st=!0;else if(We){var r=ae*ie*3/2,o=Module._malloc(r),a=new Uint8Array(Module.HEAPU8.buffer,o,r),i=Module._malloc(4),l=new Uint8Array(Module.HEAPU8.buffer,i,4);Module._PLAY_GetPicJPEG(n,a.byteOffset,r,l.byteOffset,100);var u=(l[3]<<24)+(l[2]<<16)+(l[1]<<8)+l[0],s=new ArrayBuffer(u),c=new Uint8Array(s);c.set(Module.HEAPU8.subarray(a.byteOffset,a.byteOffset+u)),X&&X(c);var f=new Blob([c.buffer],{type:"image/jpg"});_n(f,ct),f=null,Module._free(o),Module._free(i),a=null,l=null,s=null,c=null}else{ft&&ft.postMessage({nType:"CatchPic"})}},OpenPlayGroup:function(){var e=null;return We&&(e=Module._PLAY_OpenPlayGroup()),t.log("OpenPlayGroup, pGroupHandle:"+e),e},AddToPlayGroup:function(e,n){t.log("AddToPlayGroup, pGroupHandle:"+e+", nPort:"+n);var r=0;return We&&(r=Module._PLAY_AddToPlayGroup(e,n)),r},DelFromPlayGroup:function(e,n){t.log("DelFromPlayGroup, pGroupHandle:"+e+", nPort:"+n);var r=0;return We&&(r=Module._PLAY_DelFromPlayGroup(e,n)),r},ClosePlayGroup:function(e){t.log("ClosePlayGroup, pGroupHandle:"+e);var n=0;return We&&(n=Module._PLAY_ClosePlayGroup(e)),n},PausePlayGroup:function(e,n){t.log("PausePlayGroup, pGroupHandle:"+e+", bPause:"+n);var r=0;return We&&(r=Module._PLAY_PausePlayGroup(e,n)),r},SetPlayGroupSpeed:function(e,n){t.log("SetPlayGroupSpeed, pGroupHandle:"+e+", fSpeed:"+n);var r=0;return We&&(r=Module._PLAY_SetPlayGroupSpeed(e,n)),r},SetAudioTalkFlag:function(e){t.log("SetAudioTalkFlag, PlayPort:"+n+", bAudioTalk:"+e),vt=e},SetDecodeMode:function(e,r){if(t.log("SetDecodeMode, PlayPort:"+n+", nH264DecodeMode:"+e+", nH265DecodeMode:"+r),ze=e,qe=r,We)Module._PLAY_SetSupportWebMSE(n,e,r);else{var o={nType:"SetSupportWebMSE",bSupportH264MSE:e,bSupportH265MSE:r};ft&&ft.postMessage(o)}},SetColor:function(e,t,r,o){We?Module._PLAY_SetColor(n,0,e,t,r,o):Ue.SetColor(e,t,r,o)},SetDisplayRegion:function(e,t){if(0==Mt||0==Rt)return 0;if(void 0===e||t&&null==e)return-1;var r={};if(null!=e){if(e.left<0||e.right<0||e.top<0||e.bottom<0||e.left>=e.right||e.top>=e.bottom||e.right>Mt||e.bottom>Rt)return-1;r.left=e.left/Mt*ae,r.top=e.top/Rt*ie,r.right=e.right/Mt*ae,r.bottom=e.bottom/Rt*ie}if(We){var a=Module.allocateUTF8(o.id);if(t){var i=Module._malloc(16);Module.HEAP32[i/4+0]=r.left,Module.HEAP32[i/4+1]=r.top,Module.HEAP32[i/4+2]=r.right,Module.HEAP32[i/4+3]=r.bottom,Module._PLAY_SetDisplayRegion(n,0,i,a,1),Module._free(i)}else Module._PLAY_SetDisplayRegion(n,0,null,a,1);Module._free(a)}else Ue.SetDisplayRegion(r,t);return 1},SetYUVOSDInfoEx:function(e){if(0!=Mt&&0!=Rt&&0!=e.osdCount)if(We){for(var t=e.osdCount,r=Module._malloc(18376),o=new Uint8Array(Module.HEAPU8.buffer),a=0;a<18376;a++)o[r+a]=0;Module.HEAPU8[r+0]=1;var i=0;"Font.bin".split("").forEach((function(e,t){var n=e.charCodeAt(0);o[r+i+1]=n,i++})),Module.HEAP32[r/4+65]=t;for(var l=0;l<t;l++){var u=e.osdList[l].pointX/Mt*ae,s=e.osdList[l].pointY/Rt*ie;Module.HEAP32[r/4+66+141*l]=u,Module.HEAP32[r/4+67+141*l]=s,Module.HEAP32[r/4+68+141*l]=e.osdList[l].colorR,Module.HEAP32[r/4+69+141*l]=e.osdList[l].colorG,Module.HEAP32[r/4+70+141*l]=e.osdList[l].colorB,Module.HEAP32[r/4+71+141*l]=e.osdList[l].colorA,i=0;for(var c=An(e.osdList[l].strOsdData),f=0;f<c.length;f++)o[r+i+288+564*l>>0]=c[f],i++;Module.HEAP32[r/4+200+141*l]=e.osdList[l].fontX,Module.HEAP32[r/4+201+141*l]=e.osdList[l].fontY,Module.HEAP32[r/4+202+141*l]=e.osdList[l].rotateAngle,Module.HEAPU8[r+812+564*l]=e.osdList[l].coordinate8192}Module._PLAY_SetYUVOSDInfoEx(n,r),Module._free(r)}else{var p={nType:"SetYUVOSDInfoEx",OSDInfo:e};ft&&ft.postMessage(p)}},GetOriginalKey:function(e,r,o){t.log("GetOriginalKey, PlayPort:"+n);var a=1;if(We){var i=Module.intArrayFromString(e).concat(0),l=Module._malloc(i.length);Module.HEAPU8.set(i,l);var u=Module.intArrayFromString(r).concat(0),s=Module._malloc(u.length);Module.HEAPU8.set(u,s);var c=null;if(null!==o){var f=Module.intArrayFromString(o).concat(0);c=Module._malloc(f.length),Module.HEAPU8.set(f,c)}var p=Module._malloc(256),d=Module._malloc(4);a=Module._PLAY_GetOriginalKey(n,l,s,c,p,d);var m=Module.HEAP32[d>>2],g="";if(1==a&&m<=256){var S=new ArrayBuffer(m);new Uint8Array(S).set(Module.HEAPU8.subarray(p,p+m)),g=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"utf-8",n=new TextDecoder(t);return n.decode(e)}(S)}if(Module._free(l),Module._free(s),Module._free(c),Module._free(p),Module._free(d),null===o)return g;Y({nRet:a,outKey:g})}else if(It){Ut=!0;var h={nType:"GetOriginalKeyCallBack",playToken:e,playTokenKey:r,deviceID:o};ft&&ft.postMessage(h)}else Ht=e,kt=r,Lt=o;return a},SetWebSecurityKey:function(e,r,o,a){if(t.log("SetWebSecurityKey, PlayPort:"+n),!We)if(It){21!=r&&22!=r||(r=2),Ft=!0;var i={nType:"SetWebSecurityKey",nDecryptType:e,nFrameType:r,strKey:o,stStreamInfo:a};ft&&ft.postMessage(i)}else Ot=e,1==r?(Bt[0].nFrameType=r,Bt[0].strKey=o,Bt[0].stStreamInfo=a):21==r?(Bt[1].nFrameType=2,Bt[1].strKey=o,Bt[1].stStreamInfo=a):22==r&&(Bt[2].nFrameType=2,Bt[2].strKey=o,Bt[2].stStreamInfo=a)},ResetPlayState:function(){t.log("ResetPlayState, PlayPort:"+n),Ue&&Ue.ResetPlayState(),s&&s.ResetPlayState()},Stop:function(){return function e(){var r=1;if(We){if(r=Module._PLAY_GetThreadRunningState(n),t.log("GetThreadRunningState, PlayPort:"+n+", nRet:"+r),0==r)return setTimeout((function(){e()}),10),1;if(t.log("Stop, PlayPort:"+n),0==(r=Module._PLAY_Stop(n)))return r;r=Module._PLAY_CloseStream(n),re=null,Module._free(ne)}else{ft&&(ft.postMessage({nType:"Stop"}),ft.terminate(),ft=null),It=!1,null!==xt&&(xt.clear(),xt=null)}tn(),me=null,ge=null,Se=null,ye=null,ve=null,_e=null,Pe=null,Ee=null,Ae=null,nt=null,rt=null,ot=null,et=0,we&&(we.terminate(),we.setAudioProcesser(null),we=null,Ce=!1);pt&&(pt.terminate(),pt=null);mt&&(mt.terminate(),mt=null);dt&&(dt.StopProcess(),dt=null);Ue&&(Ue.stopRendering(),Ue.CleanScreen(0,0,0,0),Ue.terminate(),Ue=null);return le=0,ue=0,He=0,v=null,S=null,Qe=!1,h=!1,Ze=0,Xe=0,Je=0,$e=!1,lt=-1,ut=!1,je=!1,vt=!1,St=0,ht=0,yt=!1,Nt=!1,Wt=null,Yt=null,r}()},FrameDataCallBack:function(e,t,n,o,a,i){var l={};ge||(me=new ArrayBuffer(292),ge=new Uint8Array(me),Se=new DataView(me)),ge.set(Module.HEAPU8.subarray(i,i+292)),l.nFrameType=Se.getInt32(0,!0),l.nFrameID=Se.getInt32(4,!0),l.nFrameSubType=Se.getInt32(56,!0),l.nYear=Se.getUint16(40,!0),l.nMonth=Se.getUint16(42,!0),l.nDay=Se.getUint16(46,!0),l.nHour=Se.getUint16(48,!0),l.nMinute=Se.getUint16(50,!0),l.nSecond=Se.getUint16(52,!0);var u=Date.UTC(l.nYear,l.nMonth,l.nDay,l.nHour,l.nMinute,l.nSecond)/1e3;if(1==l.nFrameType)if(l.nRemainData=Se.getInt32(36,!0),l.bThrowFrame=Se.getUint8(120,!0),0==l.bThrowFrame){if(l.nEncodeType=Se.getInt32(108,!0),l.nStreamType=Se.getInt32(112,!0),l.nTimeStamp=Se.getUint32(8,!0),8==l.nStreamType?r.timestamp=u:r.timestamp=l.nTimeStamp/1e3,r.timestamp_usec=0,2==l.nEncodeType||4==l.nEncodeType||8==l.nEncodeType?ke=1:12==l.nEncodeType&&(ke=2),l.nWidth=Se.getInt32(12,!0),l.nHeight=Se.getInt32(16,!0),0==l.nWidth||0==l.nHeight)return;if(l.nFrameRate=Se.getInt32(20,!0),l.nStride=Se.getInt32(116,!0),18==l.nFrameSubType||19==l.nFrameSubType||20==l.nFrameSubType?se=1:0==l.nFrameSubType&&(se=0),(1==ke&&1==ze||2==ke&&1==qe)&&13!=l.nStreamType)he=new ArrayBuffer(a),(oe=new Uint8Array(he)).set(Module.HEAPU8.subarray(t,t+a)),on(oe,null,null,a,l);else{if(0==t||0==n||0==o)return;if(!We){l.nWidth==le&&l.nHeight==ue&&null!=Pe||(ye=null,ve=null,_e=null,Pe=null,Ee=null,Ae=null,ye=new ArrayBuffer(l.nWidth*l.nHeight),Pe=new Uint8Array(ye),ve=new ArrayBuffer(l.nWidth*l.nHeight/4),Ee=new Uint8Array(ve),_e=new ArrayBuffer(l.nWidth*l.nHeight/4),Ae=new Uint8Array(_e));var s=0;for(s=0;s<l.nHeight;s++)Pe.set(Module.HEAPU8.subarray(t+s*l.nStride,t+s*l.nStride+l.nWidth),s*l.nWidth);for(s=0;s<l.nHeight/2;s++)Ee.set(Module.HEAPU8.subarray(n+s*l.nStride/2,n+s*l.nStride/2+l.nWidth/2),s*l.nWidth/2);for(s=0;s<l.nHeight/2;s++)Ae.set(Module.HEAPU8.subarray(o+s*l.nStride/2,o+s*l.nStride/2+l.nWidth/2),s*l.nWidth/2)}on(Pe,Ee,Ae,a,l)}}else on(null,null,null,0,l);else if(2==l.nFrameType){if(l.nTotalChannel=Se.getInt32(68,!0),l.nCurChannel=Se.getInt32(72,!0),l.nCurChannel>0)return;l.nBits=Se.getInt32(28,!0),l.nSamples=Se.getInt32(32,!0),l.nAudioChnNum=Se.getInt32(24,!0);var c=new ArrayBuffer(a),f=new Uint8Array(c);f.set(Module.HEAPU8.subarray(t,t+a)),on(f,null,null,a,l)}he=null,oe=null},DecryptionResultCallBack:function(e,t,n){n,z(n)},RecordDataCallBack:function(e,n,r,o,a){it+=r,rt||(nt=new ArrayBuffer(292),rt=new Uint8Array(nt),ot=new DataView(nt)),rt.set(Module.HEAPU8.subarray(a,a+292));var i=ot.getInt32(0,!0),l=ot.getInt32(4,!0);ot.getInt32(56,!0);if(1==i){t.log("RecordDataCallBack, nPort:"+e+", nFrameID:"+l);ot.getInt32(76,!0),ot.getInt32(80,!0),ot.getUint32(8,!0);var u=ot.getUint16(40,!0),s=Pn(ot.getUint16(42,!0)),c=Pn(ot.getUint16(46,!0)),f=Pn(ot.getUint16(48,!0)),p=Pn(ot.getUint16(50,!0)),d=Pn(ot.getUint16(52,!0)),m=Date.UTC(u,s,c,f,p,d)/1e3,g=new Date("".concat(u,"-").concat(s,"-").concat(c," ").concat(f,":").concat(p,":").concat(d)).getTime()}W&&W({frameType:i,timeStamp:g,utcTimeStamp:m,length:it});var S=new ArrayBuffer(r),h=new Uint8Array(S);h.set(Module.HEAPU8.subarray(n,n+r)),We&&tt.postMessage({type:"addBuffer",buffer:h,offset:o,recordType:at}),S=null,h=null},IVSDataCallBack:function(e,n,r,o,a){t.log("IVSDataCallBack, nPort:"+e+", nType:"+r+", nLen:"+o+", nFrameID:"+a),an(e,n,r,o,a)},setCallback:function(e,n){switch(t.log("setCallback, type:"+e+", func:"+n),e){case"GetPlayPort":$=n;break;case"PlayStart":Ue.setBeginDrawCallback(n),s&&s.setBeginDrawCallback(n),F=n;break;case"DecodeStart":O=n;break;case"VideoFrameInfo":B=n;break;case"AudioFrameInfo":N=n;break;case"RecordTimeStamp":W=n;break;case"GetOriginalKey":Y=n;break;case"DecryptionResult":z=n;break;case"Error":Z=n;break;case"loadingBar":j=n;break;case"audioTalk":case"videoCapture":Q=n;break;case"StreamPlayOver":q=n;break;case"ARTagInfo":K=n;break;case"CapturePicDataCallBack":X=n,s&&s.setCapturePicDataCallBack(n);break;case"IVSDrawData":J=n}},GetCurrentPlayTime:function(){return r.timestamp},GetCurrentPlayedTime:function(){return null!=Yt&&null!=Wt?3600*Yt.nDay*24+3600*Yt.nHour+60*Yt.nMinute+Yt.nSecond-(3600*Wt.nDay*24+3600*Wt.nHour+60*Wt.nMinute+Wt.nSecond):-1},SetSTFrameCallback:function(e){ee=e},setPrintLogLevel:function(e){if(Vt=e,We)Module._PLAY_SetPrintLogLevel(e);else{var t={nType:"setPrintLogLevel",nLogLevel:e};ft&&ft.postMessage(t)}Ue&&Ue.SetPrintLogLevel(e)},SetPlayMethod:function(e,t,n){Ue&&Ue.SetPlayMethod(e,t,n)}};var un,sn,cn,fn,pn,dn,mn,gn,Sn,hn,yn,vn,_n=(un=window,sn=un.document,cn=function(){return un.URL||un.webkitURL||un},fn=sn.createElementNS("http://www.w3.org/1999/xhtml","a"),pn="download"in fn,dn=/constructor/i.test(un.HTMLElement),mn=/CriOS\/[\d]+/.test(navigator.userAgent),gn=function(e){(un.setImmediate||un.setTimeout)((function(){throw e}),0)},Sn=function(e){setTimeout((function(){"string"==typeof e?cn().revokeObjectURL(e):e.remove()}),4e4)},hn=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},vn=(yn=function(e,t,n){n||(e=hn(e));var r,o=this,a="application/octet-stream"===e.type,i=function(){!function(e,t,n){for(var r=(t=[].concat(t)).length;r--;){var o=e["on"+t[r]];if("function"==typeof o)try{o.call(e,n||e)}catch(e){gn(e)}}}(o,"writestart progress write writeend".split(" "))};if(o.readyState=o.INIT,pn)return r=cn().createObjectURL(e),void setTimeout((function(){fn.href=r,fn.download=t,fn.dispatchEvent(new MouseEvent("click")),i(),Sn(r),o.readyState=o.DONE}));!function(){if((mn||a&&dn)&&un.FileReader){var t=new FileReader;return t.onloadend=function(){var e=mn?t.result:t.result.replace(/^data:[^;]*;/,"data:attachment/file;");un.open(e,"_blank")||(un.location.href=e),e=void 0,o.readyState=o.DONE,i()},t.readAsDataURL(e),void(o.readyState=o.INIT)}r||(r=cn().createObjectURL(e)),a?un.location.href=r:un.open(r,"_blank")||(un.location.href=r),o.readyState=o.DONE,i(),Sn(r)}()}).prototype,"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=hn(e)),navigator.msSaveOrOpenBlob(e,t)}:(vn.readyState=vn.INIT=0,vn.WRITING=1,vn.DONE=2,vn.error=vn.onwritestart=vn.onprogress=vn.onwrite=vn.onabort=vn.onerror=vn.onwriteend=null,function(e,t,n){return null==t||void 0===t?null:new yn(e,t||e.name||"download",n)}));function Pn(e){return e<10?"0".concat(e):e}function En(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"utf-8",n=new TextDecoder(t),r=new Uint8Array(e),o=0;o<r.length&&0!==r[o];)o++;return n.decode(e.slice(0,o))}function An(e){for(var t=[],n=0;n<e.length;n++){var r=e.charCodeAt(n);r>=0&&r<=127?(1,t.push(r)):r>=128&&r<=2047?(2,t.push(192|31&r>>6),t.push(128|63&r)):r>=2048&&r<=55295||r>=57344&&r<=65535?(3,t.push(224|15&r>>12),t.push(128|63&r>>6),t.push(128|63&r)):r>=65536&&r<=1114111&&(4,t.push(240|7&r>>18),t.push(128|63&r>>12),t.push(128|63&r>>6),t.push(128|63&r))}return t}return new zt};t.default=function(e){var t=new L,n=null,r=null,o=!1,a=!1,i=null,l=null,u=!1,s=!1,c=0,f=!1,p=1,d=e,m=0,g=!1,S="",h=0,y="",v="",P=null,E=null,A=0,b=!1,C=1,T=0,M=!1,R=!1,D=!1,I=8,U=!1,H=0,k=!1,F=0,O={Disconnect:function(){},GetPlayPort:function(){},PlayStart:function(){},DecodeStart:function(){},VideoFrameInfo:function(){},AudioFrameInfo:function(){},RecordTimeStamp:function(){},GetOriginalKey:function(){},DecryptionResult:function(){},PlayBackStreamRange:function(){},StreamPlayOver:function(){},StreamRedirect:function(){},ARTagInfo:function(){},CapturePicDataCallBack:function(){},IVSDrawData:function(){},AVTalkDataSendSuccess:function(){},Error:function(){}};function B(){}function G(e,a,u){if(t.log("startPullStream url:"+e.strRtspvUrl+", bAppletUse:"+a+", nProtocolType:"+u),function(){null!==P&&(P.onerror=null);null!==P&&P.readyState===P.OPEN&&(P.close(),P=null)}(),r.SetAudioTalkFlag(e.bTalkService),r.ResetPlayState(),null!=e.bBroadcast&&(R=e.bBroadcast),null!=a&&(U=a),d||U)null==i&&(i=new w,0<F&&i.SetLogLevel(F));else{y=e.strUserName,v=e.strPassWord,S=e.strDeviceID;var s=e.strRtspvUrl.indexOf("&encrypt=");for(var c in-1!=s&&(h=e.strRtspvUrl.slice(s+9,s+10)),null==n&&(n=new _(t)),O)n&&n.setCallback(c,O[c])}var f=e.strRtspvUri;0==(H=null==u?-1!=e.strRtspvUrl.indexOf("rtsp://")?0:1:u)?-1!=e.strRtspvUri.indexOf("?")?e.strRtspvUri+="&rtspoverwebsocket":e.strRtspvUri+="/rtspoverwebsocket":1==H&&(-1!=e.strRtspvUri.indexOf("?")?e.strRtspvUri+="&httpprivateoverwebsocket":e.strRtspvUri+="/httpprivateoverwebsocket");var p=e.nShortTimeout||3,m=e.nRtspResponseTimeout||8;if(I=m,d||U){if(1==H&&-1==e.strRtspvUrl.indexOf("http://")){s=f.indexOf("://");f=f.slice(s),e.strRtspvUrl="http"+f+e.strRtspvUrl}e.strSourceId.length>0&&(e.strRtspvUrl+="?sourceId=",e.strRtspvUrl+=e.strSourceId),l=i.StartStream(e,H),i.SetMsgWaitTimeout(p)}else{var g=!0;1==H&&(g=!1);var E={bTalkService:e.bTalkService,bBroadcast:R,nRange:e.nRange,bPlayBack:o,bRtspFlag:g,nShortTimeout:p,nRtspResponseTimeout:m};n.connect(e.strRtspvUri,e.strRtspvUrl,e.strSourceId,E),n.setUserInfo(e.strUserName,e.strPassWord),n.setRtpDataCallback(Y),n.setStreamFinishCallback(j),n.setRtpInterlevedIDCallback(z)}return r&&r.SetStreamOver(!1),l}function V(){if(t.log("stopPullStream"),d||U?l&&(m&&(clearTimeout(m),m=0),i&&(i.StopStream(),i=null,l=null),D=!1):(N("TEARDOWN"),n&&n.disconnect(),n=null),a)return a=!1,r&&r.StopRecord()}function x(e){e!=s&&(s=e,d||U?l&&i.PauseStream(e):N(e?"PAUSE":"PLAY"))}function N(e,t,r,o){var a;a={command:e,startTime:t,endTime:r,speed:o},n&&n.controlPlayer(a)}function W(){u||(setTimeout((function(){r&&(c=r.GetSourceBufferRemain())}),1),o&&(c>7340032?x(!0):c<2097152&&x(!1)))}function Y(e){g||(g=!0,function(){if(!d&&!U){var e=n&&n.GetSSRC(),t=n&&n.GetSdpInfo(),o={};if(o.sdpInfo=t,o.strUserName=y,o.strPassWord=v,0==h)return;1==h&&0==e.videoSSRC||(o.nSsrc=e.videoSSRC,r.SetWebSecurityKey(h,1,S,o)),1==h&&(0!=e.audio1SSRC&&(o.nSsrc=e.audio1SSRC,r.SetWebSecurityKey(h,21,S,o)),0!=e.audio2SSRC&&(o.nSsrc=e.audio2SSRC,r.SetWebSecurityKey(h,22,S,o)))}}()),!o&&c>7340032||r&&r.InputData(e)}function z(e){r&&r.SetRtpInterlevedID(e)}function q(){W()}function j(){r&&r.SetStreamOver(!0)}function K(e,t){d||U?l&&i.PutStream(e,t):n&&n.sendRtpData(e),k||(O.AVTalkDataSendSuccess(),k=!0)}function X(e){if(!b){var t=r.GetOriginalKey(e.data,E,null);if(t.length>0&&-1!==t.indexOf("data")){A&&(clearTimeout(A),A=0),b=!0;var n=t.indexOf('"deviceIp":'),o=t.indexOf('",',n),a=t.slice(n+12,o);n=t.indexOf('"devicePort":'),o=t.indexOf('",',n);var i=t.slice(n+14,o);n=t.indexOf('"userName":'),o=t.indexOf('"}',n);var l=t.slice(n+12,o);n=t.indexOf('"devicePassword":'),o=t.indexOf('",',n);var u=t.slice(n+18,o);n=t.indexOf('"id":'),o=t.indexOf(",",n);var s=t.slice(n+5,o),c={strRtspvUri:"wss://"+a+":"+i,strRtspvUrl:"rtsp://"+l+":"+u+"@"+a+":"+i+"/cam/realmonitor?channel="+C+"&subtype="+T+"&proto=Private3",strSourceId:"",strUserName:l||"admin",strPassWord:u||"admin123",strDeviceID:s,bTalkService:M,nRange:0,nShortTimeout:3,nRtspResponseTimeout:8};setTimeout((function(){var e=G(c);O.GetStreamClinetHandle(e)}),1)}}}return B.prototype={Init:function(e,n){return function(e,n){null!=n&&(C=n.nChannel,T=n.nSubType,M=n.bTalkService);o=e.bPlayBack,null==r&&(r=new ze(d,t),0<F&&r.setPrintLogLevel(F));var a=r.Init(e);if(a>0){var i=r.GetPlayPort();d&&O.GetPlayPort(i)}for(var l in O)r.setCallback(l,O[l]);return r.SetSTFrameCallback(q),a}(e,n)},SetCacheMode:function(e){return function(e){return r&&r.SetCacheMode(e)}(e)},StartPullStream:function(e,t,n){return G(e,t,n)},SetLoginParam:function(e){!function(e){d&&(null==i&&(i=new w),i.SetLoginParam(e))}(e)},StopPullStream:function(){V()},Pause:function(e){!function(e){u=e,x(e),r.Pause(e)}(e)},Stop:function(){!function(){r&&(r.CloseIVSDraw(),r.Stop(),r=null);g=!1,u=!1}()},SetSecurityKey:function(e,t,n,o,a){!function(e,t,n,o,a){r.SetSecurityKey(e,t,n,o,a)}(e,t,n,o,a)},SetSpeed:function(e){p=e,function(e){d||U?l&&i.PlayControl(-1,-1,e,-1):N("SCALE",-1,-1,e);r.SetPlaySpeed(e)}(e)},SetSoundState:function(e){!function(e){r.SetSoundState(e)}(e)},SetVolume:function(e){!function(e){r.SetVolume(e)}(e)},StartRecord:function(e,t,n){return function(e,t,n){return a=!0,r.StartRecord(e,t,n)}(e,t,n)},StopRecord:function(){return a=!1,r.StopRecord()},OpenIVS:function(e){return function(e){return r.OpenIVSDraw(e)}(e)},CloseIVS:function(){return r.CloseIVSDraw()},SetIvsEnable:function(e,t){return function(e,t){return r.SetIvsEnable(e,t)}(e,t)},SetIvsLanguageEnvi:function(e){!function(e){r.SetIvsLanguageEnvi(e)}(e)},SetPanoAR:function(e,t){return function(e,t){return r.SetPanoAR(e,t)}(e,t)},Set3DPoint:function(e){return function(e){return r.Set3DPoint(e)}(e)},OnMouseDown:function(){return r.OnMouseDown()},OnMouseMove:function(e,t){return function(e,t){return r.OnMouseMove(e,t)}(e,t)},OnMouseUp:function(){return r.OnMouseUp()},OnMouseWheel:function(e){return function(e){return r.OnMouseWheel(e)}(e)},GetVRCoord2DTrans:function(e,t){return function(e,t){return r.GetVRCoord2DTrans(e,t)}(e,t)},GetVRCoord3DTrans:function(e,t){return function(e,t){return r.GetVRCoord3DTrans(e,t)}(e,t)},CapturePic:function(e){!function(e){r.capturePic(e)}(e)},StartTalk:function(e){!function(e){r.setCallback("audioTalk",K),r.StartTalk(e)}(e)},StopTalk:function(){r.StopTalk(),k=!1},StartVideoCapture:function(e,t,n){!function(e,t,n){r.setCallback("audioTalk",K),r.StartVideoCapture(e,t,n)}(e,t,n)},StopVideoCapture:function(){r.StopVideoCapture()},SetSeekTime:function(e,t,n){!function(e,t,n){void 0===t&&(t=-1);d||U?l&&i.PlayControl(e,t,p,n):N("PLAY_SEEK",e,t,p);for(var o=1;o<5;o++)r.ResetBuffer(o)}(e,t,n)},ResetBuffer:function(e){!function(e){r&&r.ResetBuffer(e)}(e)},OpenPlayGroup:function(){return r.OpenPlayGroup()},AddToPlayGroup:function(e,t){return function(e,t){return r.AddToPlayGroup(e,t)}(e,t)},DelFromPlayGroup:function(e,t){return function(e,t){return r.DelFromPlayGroup(e,t)}(e,t)},ClosePlayGroup:function(e){return function(e){return r.ClosePlayGroup(e)}(e)},PausePlayGroup:function(e,t){return function(e,t){return x(t),r.PausePlayGroup(e,t)}(e,t)},SetPlayGroupSpeed:function(e,t){return p=t,function(e,t){d||U?l&&i.PlayControl(-1,-1,t,-1):N("SCALE",-1,-1,t);return r.SetPlayGroupSpeed(e,t)}(e,t)},GetSourceBufferRemain:function(){return c=r.GetSourceBufferRemain()},SetDecodeMode:function(e,t){!function(e,t){r.SetDecodeMode(e,t)}(e,t)},SetColor:function(e,t,n,o){!function(e,t,n,o){r.SetColor(e,t,n,o)}(e,t,n,o)},SetDisplayRegion:function(e,t){return function(e,t){return r.SetDisplayRegion(e,t)}(e,t)},SetYUVOSDInfoEx:function(e){!function(e){r.SetYUVOSDInfoEx(e)}(e)},GetOriginalKey:function(e){!function(e){r.GetOriginalKey(e.strPlayToken,e.strPlayTokenKey,e.strDeviceID)}(e)},GetCurrentPlayedTime:function(){return r.GetCurrentPlayedTime()},InputData:function(e){return function(e){return r&&r.InputData(e)}(e)},UserVerify:function(e,t,n,r,o){!function(e,t,n,r,o){E=o,(P=new WebSocket("wss://"+e+":"+t+"/ar/device/detail")).binaryType="arraybuffer",P.addEventListener("message",X,!1),P.onopen=function(){var e='{\n    "id":'+n+',\n    "token":"'+r+'"\n}';P.send(e),b=!1,function(){A&&(clearTimeout(A),A=0);A=setTimeout((function(){O.Error({errorCode:"408",description:"Device verify failed"})}),5e3)}()},P.onerror=function(e){O.Error({errorCode:205,description:"WebSocket Verify Error"})}}(e,t,n,r,o)},SetCallBack:function(e,t){O[e]=t},SetPrintLogLevel:function(e){!function(e){F=e,t.setPrintLogLevel(e),r&&r.setPrintLogLevel(e),d&&i&&i.SetLogLevel(e)}(e)},SetPlayMethod:function(e,t,n){!function(e,t,n){r&&r.SetPlayMethod(e,t,n)}(e,t,n)},InputDataEx:function(e,t){if(m&&(clearTimeout(m),m=0),o&&!f){f=!0;var n=i.GetPlayInfo();O.PlayBackStreamRange(n)}var a=new ArrayBuffer(t),l=new Uint8Array(a);return l.set(SCModule.HEAPU8.subarray(e,e+t)),r&&r.InputData(l)},SetFrameData:function(e,t,n,o,a,i){r&&(W(),r.FrameDataCallBack(e,t,n,o,a,i))},SetDecryptionResult:function(e,t,n){r.DecryptionResultCallBack(e,t,n)},SetRecordData:function(e,t,n,o,a){r.RecordDataCallBack(e,t,n,o,a)},SetIVSDrawData:function(e,t,n,o,a){r.IVSDataCallBack(e,t,n,o,a)},StreamMsgPlay:function(){R||D||(D=!0,m=setTimeout((function(){m&&(clearTimeout(m),m=0),O.Error({errorCode:"409",description:"Rtsp Not Response"})}),1e3*I))},StreamRedirectCallback:function(e){!function(e){V();var t=new ArrayBuffer(1e3),n=new Uint8Array(t);n.set(SCModule.HEAPU8.subarray(e,e+1e3));var r=function(e){for(var t="",n=0;n<e.length;n++){var r=String.fromCharCode(e[n]);if(t+=r,"\0"==r)break}return t}(n);O.StreamRedirect(r)}(e)},StreamDisconnectCallback:function(){O.Disconnect()},StreamFinishCallback:function(){j()},StreamFailedCallback:function(e){m&&(clearTimeout(m),m=0),285868036==e||6500424==e?O.Error({errorCode:"408",description:"Short Request Timeout"}):285868034==e&&O.Error({errorCode:"202",description:"WebSocket Error"})}},new B}}]).default}));