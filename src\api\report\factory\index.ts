import request from '@/config/axios'

/**
 * 水厂配置接口
 */
export const FactoryApi = {
  /**
   * 获取水厂列表
   * @param params 查询参数
   */
  getFactoryList: async (params: any) => {
    return await request.get({ url: '/report/factory/page', params })
  },

  /**
   * 获取水厂树
   * @param params 查询参数
   */
  getFactoryTree: async (params: any) => {
    return await request.getOriginal({ url: '/report/factory/get-tree', params })
  },
  /**
   * 获取水厂树
   * @param params 查询参数
   */
  listParentFactory: async () => {
    return await request.getOriginal({ url: '/report/factory/list-parent-factory' })
  },

  /**
   * 获取单个水厂信息
   * @param id 水厂ID
   */
  getFactory: async (id: number) => {
    return await request.get({ url: '/report/factory/get?id=' + id })
  },

  /**
   * 新增水厂
   * @param data 水厂信息
   */
  addFactory: async (data: any) => {
    return await request.post({ url: '/report/factory/create', data })
  },

  /**
   * 更新水厂信息
   * @param data 水厂信息
   */
  updateFactory: async (data: any) => {
    return await request.put({ url: '/report/factory/update', data })
  },

  /**
   * 删除水厂
   * @param id 水厂ID
   */
  deleteFactory: async (id: number) => {
    return await request.delete({ url: '/report/factory/delete?id=' + id })
  },

  /**
   * 导出水厂Excel
   * @param params 查询参数
   */
  exportExcel: async (params: any) => {
    return await request.get({
      url: '/report/factory/export-excel',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 根据当前登陆人查询有权限的水厂 -- 填报界面
   * @param params 查询参数
   */
  queryFactoryListByUser: async (params: any) => {
    return await request.getOriginal({
      url: '/report/factory/queryFactoryListByUser',
      params
    })
  },

  /**
   * 根据当前登陆人查询有权限的水厂 -- 审核界面
   * @param params 查询参数
   */
  queryFactoryTreeByCurrentReviewer: async () => {
    return await request.getOriginal({
      url: '/report/factory/queryFactoryTreeByCurrentReviewerId',
    })
  },

  /**
   * 汇总界面查询所有水厂
   * @param params 查询参数
   */
  queryAllFactoryTreeAndLevelOne: async () => {
    return await request.getOriginal({
      url: '/report/factory/queryAllFactoryTreeAndLevelOne'})
  },

  /**
   * 分页查询厂站详情
   * @param params 查询参数
   */
  getFactoryDetailPage: async (params: any) => {
    return await request.getOriginal({
      url: '/report/factory-detail/page',
      params
    })
  },

  /**
   * 根据ID查询厂站详情
   * @param id 厂站ID
   */
  getFactoryDetail: async (id: number) => {
    return await request.getOriginal({ url: `/report/factory-detail/getById/${id}` })
  },

  /**
   * 新增厂站详情
   * @param data 厂站详情信息
   */
  addFactoryDetail: async (data: any) => {
    // 确保过滤掉不需要的字段
    const { createTime, updateTime, creator, updater, deleted, ...submitData } = data;
    return await request.post({ url: '/report/factory-detail/create', data: submitData })
  },

  /**
   * 更新厂站详情
   * @param data 厂站详情信息
   */
  updateFactoryDetail: async (data: any) => {
    // 确保过滤掉不需要的字段
    const { createTime, updateTime, creator, updater, deleted, ...submitData } = data;
    return await request.put({ url: '/report/factory-detail/update', data: submitData })
  },

  /**
   * 删除厂站信息
   * @param id 厂站ID
   */
  deleteFactoryDetail: async (id: number) => {
    try {
      // 使用原始delete方法发送请求
      const response = await request.delete({
        url: '/report/factory-detail/delete',
        params: { id }
      });

      console.log('删除响应:', response);
      return response;
    } catch (error) {
      console.error('删除请求出错:', error);
      return { code: -1, msg: '请求出错', error };
    }
  },

  /**
   * 获取水厂统计数据
   * @returns 水厂统计数据
   */
  getFactoryStats: async () => {
    try {
      // 这里是接口定义，当后端接口实现后可以直接调用
      return await request.getOriginal({ url: '/report/factory-detail/stats' });
    } catch (error) {
      console.error('获取统计数据出错:', error);
      // 返回默认数据
      return {
        code: 0,
        data: {
          totalFactories: 32,
          runningFactories: 28,
          warningFactories: 3,
          averageCapacity: 2500
        },
        msg: '获取成功'
      };
    }
  }
}
