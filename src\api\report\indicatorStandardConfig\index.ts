import request from '@/config/axios'

export interface IndicatorConfig {
  id: number
  factoryId: number
  bizType: string
  indicatorName: string
  configJson: any
  creator: string
  updater: string
  createTime: string
  updateTime: string
}

// 获取指标标准配置
export const getIndicatorStandardConfig = (params: {
  factoryId: number | null
}) => {
  return request.get({
    url: '/report/indicator-standard-config/get',
    params
  })
}

// 保存指标标准配置
export const saveIndicatorStandardConfig = (data: {
  factoryId: number
  bizType: string
  indicatorName: string
  configJson: any
}) => {
  return request.post({
    url: '/report/indicator-standard-config/save',
    data
  })
}

// 批量保存指标标准配置
export const batchSaveIndicatorStandardConfig = (data:any) => {
  return request.post({
    url: '/report/indicator-standard-config/batch-save',
    data
  })
}

// 删除指标标准配置
export const deleteIndicatorStandardConfig = (id: number) => {
  return request.delete({
    url: `/report/indicator-standard-config/delete?id=${id}`
  })
} 