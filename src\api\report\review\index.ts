import request from '@/config/axios'

// 统计类型
export type StatisticsType = 'consumption' | 'quality'

// 获取审核统计数据
export const getStatistics = (type: StatisticsType, date: string) => {
  return request.post({
    url: '/report/indicator/stats/query',
    data: { 
      bizType: type,
      date: date
    }
  })
}

// 获取待审核指标列表
export const getReviewList = (params: any) => {
  return request.get({
    url: '/report/indicator-data/reviewListByCondition',
    params
  })
}

// 审核通过单个指标
export const approveIndicator = (data: { id: number | string; comment?: string }) => {
  return request.post({
    url: '/report/review/approve',
    data
  })
}

// 审核退回单个指标
export const rejectIndicator = (data: { id: number | string; comment: string }) => {
  return request.post({
    url: '/report/review/reject',
    data
  })
}

// 批量审核通过指标
export const batchApproveIndicator = (data: { 
  reporterId: number; 
  bizType: string;
  comment?: string;
  factoryId: number;
  reportDate: string;
}) => {
  return request.post({
    url: '/report/indicator-data/batch-approve',
    data
  })
}

// 批量退回指标
export const batchRejectIndicator = (data: { 
  reporterId: number; 
  bizType: string;
  comment?: string;
  factoryId: number;
  reportDate: string;
}) => {
  return request.post({
    url: '/report/indicator-data/batch-reject',
    data
  })
}

// 计算指标值与标准值的偏差
export const getReviewFlowPage = (data: {
  factoryId: number;
  reportDate?: string;
  bizType: string
}) => {
  return request.get({
    url: '/report/indicator-review-flow/list',
    params: data
  })
} 

