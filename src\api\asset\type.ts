import request from '@/config/axios'

// 资产类型 VO
export interface AssetTypeVO {
  id: number // 主键ID
  typeName: string // 资产类型名称(如生产类/非生产类)
  typeCode: string // 资产类型编码
  description: string // 资产类型描述
  type: boolean // 生产类型：1 非生产类型：2
  sewagePlantId: number // 污水处理厂ID
  deleteMark: boolean // 逻辑删除标记(0=正常,1=删除)
  createById: number // 创建人ID
  updateById: number // 修改人ID
}

// 资产类型 API
export const AssetTypeApi = {
  // 查询资产类型分页
  getAssetTypePage: async (params: any) => {
    return await request.get({ url: `/asset/type/page`, params })
  },

  // 查询资产类型分页
  getAssetTypeList: async (params: any) => {
    return await request.get({ url: `/asset/type/list`, params })
  },

  // 查询资产类型详情
  getAssetType: async (id: number) => {
    return await request.get({ url: `/asset/type/get?id=` + id })
  },

  // 新增资产类型
  createAssetType: async (data: AssetTypeVO) => {
    return await request.post({ url: `/asset/type/create`, data })
  },

  // 修改资产类型
  updateAssetType: async (data: AssetTypeVO) => {
    return await request.put({ url: `/asset/type/update`, data })
  },

  // 删除资产类型
  deleteAssetType: async (id: number) => {
    return await request.delete({ url: `/asset/type/delete?id=` + id })
  },

  // 导出资产类型 Excel
  exportAssetType: async (params) => {
    return await request.download({ url: `/asset/type/export-excel`, params })
  },
}
