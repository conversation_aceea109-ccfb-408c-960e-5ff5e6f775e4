import request from '@/config/axios'

export interface AssetStatusQuery {
  pageNum?: number
  pageSize?: number
  name?: string
  code?: string
}

export interface AssetStatusVO {
  id: number
  statusName: string
  code: string
  description?: string
  createTime?: string
  updateTime?: string
}

export const AssetStatusApi = {
  // 查询资产状态列表
  getAssetStatusList: async (params: AssetStatusQuery) => {
    return await request.get({ url: '/asset/status/list', params })
  },

  // 查询资产状态详情
  getAssetStatus: async (id: number) => {
    return await request.get({ url: `/asset/status/get?id=${id}` })
  },

  // 新增资产状态
  createAssetStatus: async (data: AssetStatusVO) => {
    return await request.post({ url: '/asset/status/create', data })
  },

  // 修改资产状态
  updateAssetStatus: async (data: AssetStatusVO) => {
    return await request.put({ url: '/asset/status/update', data })
  },

  // 删除资产状态
  deleteAssetStatus: async (id: number) => {
    return await request.delete({ url: `/asset/status/delete?id=${id}` })
  },

  // 导出资产状态
  exportAssetStatus: async (params: AssetStatusQuery) => {
    return await request.download({ url: '/asset/status/export', params })
  }
}
