import request from '@/config/axios'

// 报表信息 VO
export interface ReportVO {
  id: number // 主键
  reportName: string // 名称
  reportCode: string // 报表编码
  reportGroup: string // 分组
  reportType: string // 报表类型
  reportImage: string // 报表缩略图
  reportDesc: string // 报表描述
  reportAuthor: string // 报表作者
  downloadCount: number // 报表下载次数
  enableFlag: number // 0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG
}

// 报表信息 API
export const ReportApi = {
  // 查询报表信息分页
  getReportPage: async (params: any) => {
    return await request.get({ url: `/report/report/page`, params })
  },

  // 查询报表信息详情
  getReport: async (id: number) => {
    return await request.get({ url: `/report/report/get?id=` + id })
  },

  // 根据报表code查询报表信息详情
  getReportByReportCode: async (reportCode: string) => {
    return await request.get({ url: `/report/report/getByReportCode?reportCode=` + reportCode })
  },

  // 新增报表信息
  createReport: async (data: ReportVO) => {
    return await request.post({ url: `/report/report/create`, data })
  },

  // 修改报表信息
  updateReport: async (data: ReportVO) => {
    return await request.put({ url: `/report/report/update`, data })
  },

  // 删除报表信息 根据ID
  deleteReport: async (id: number) => {
    return await request.delete({ url: `/report/report/delete?id=` + id })
  },


  // 删除报表信息 根据COde
  deleteReportByReportCode: async (reportCode: string) => {
    return await request.delete({ url: `/report/report/deleteByReportCode?reportCode=` + reportCode })
  },

  // 导出报表信息 Excel
  exportReport: async (params) => {
    return await request.download({ url: `/report/report/export-excel`, params })
  },

  // 获取报表设计数据
  getReportDesignByCode: async (reportCode: string, loadType:number) => {
    return await request.getOriginal({
      url: `/report/report/report-design-view/${reportCode}?loadType=`+loadType
    })
  },

  // 报表设计保存
  saveExcelByDesign: async (data: any) => {
    return await request.postOriginal({ url: `/report/excel/saveExcelByDesign`, data })
  },


  // 报表设计更新
  updateExcelByDesign: async (data: any) => {
    return await request.postOriginal({ url: `/report/excel/updateExcelByDesign`, data })
  },

  // 获取报表预览数据
  getReportPreviewByCode: async (reportCode: string, loadType:number) => {
    return await request.getOriginal({
      url: `/report/report/report-preview-view/${reportCode}?loadType=`+loadType
    })
  },

  // 获取报表填充初始数据
  getReportFillByCode: async (reportCode: string, loadType:number, data:any) => {
    return await request.postOriginal({
      url: `/report/report/report-fill-view/${reportCode}?loadType=`+loadType,
      data
    })
  },

  // 报表设计保存
  saveReportFillDataByCode: async (data: any) => {
    return await request.postOriginal({ url: `/report/excel/updateExcelByFill`, data })
  }
}
