import request from '@/config/axios'

// 工艺段保存请求类型
interface ProcessSaveReqVO {
  id?: number; // 创建时可选，更新时必须
  processCode: string;
  processName: string;
  factoryCode: string;
  devices?: any[]; // 可选设备列表
}

// 设备保存请求类型
interface DeviceSaveReqVO {
  id?: number; // 创建时可选，更新时必须
  deviceCode: string;
  deviceName: string;
  processCode: string;
  indicators?: any[]; // 可选指标列表
}

// 指标保存请求类型
interface IndicatorSaveReqVO {
  id?: number; // 创建时可选，更新时必须
  indicatorCode: string;
  indicatorName: string;
  unit?: string; // 单位可选
  deviceCode: string;
}

// 工艺树DTO
export interface ProcessTreeDTO {
  processId: number;
  processCode: string;
  processName: string;
  factoryCode: string;
  children: DeviceDTO[]; // 设备列表
}

// 设备DTO
export interface DeviceDTO {
  deviceId: number;
  deviceCode: string;
  deviceName: string;
  processCode: string;
}

export const OnlineMonitorAPI = {

  // 一次性获取工艺和设备的树形结构
  queryProcessDeviceTree: async (factoryCode: string) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/process-device-tree`,
      params: { factoryCode }
    })
  },

  // 根据水厂ID查询水厂所有工艺画面数据列表
  queryProcessScreenByFactory: async (factoryCode: string) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/process-screen/query-process-screen-by-factory`,
      params: { factoryCode }
    })
  },

  // 获取所有设备列表
  listDevices: async () => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/device/list`
    })
  },

  queryDeviceListByProcess: async (processCode: string) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/device/query-device-by-process`,
      params: { processCode }
    })
  },

  queryIndicatorListByDevice: async (deviceCode: string) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/indicator/query-indicator-by-device`,
      params: { deviceCode }
    })
  },

  queryIndicatorDataListByDevice: async (data: any) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/query-indicator-data-by-device`,
      data
    })
  },

  // 创建工艺段
  createProcess: async (data: ProcessSaveReqVO) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/process-screen/create`,
      data
    })
  },

  // 更新工艺段
  updateProcess: async (data: ProcessSaveReqVO) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/process-screen/update`,
      method: 'PUT', // PUT方法
      data
    })
  },

  // 删除工艺段
  deleteProcess: async (id: number) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/process-screen/delete`,
      method: 'DELETE',
      params: { id }
    })
  },

  // 创建设备
  createDevice: async (data: DeviceSaveReqVO) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/device/create`,
      data
    })
  },

  // 更新设备
  updateDevice: async (data: DeviceSaveReqVO) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/device/update`,
      method: 'PUT',
      data
    })
  },

  // 删除设备
  deleteDevice: async (id: number) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/device/delete`,
      method: 'DELETE',
      params: { id }
    })
  },

  // 创建指标
  createIndicator: async (data: IndicatorSaveReqVO) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/indicator/create`,
      data
    })
  },

  // 更新指标
  updateIndicator: async (data: IndicatorSaveReqVO) => {
    return await request.postOriginal({
      url: `/monitor/onlineMonitor/indicator/update`,
      method: 'PUT',
      data
    })
  },

  // 删除指标
  deleteIndicator: async (id: number) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/indicator/delete`,
      method: 'DELETE',
      params: { id }
    })
  },

  // 获取所有可用的指标
  getAllIndicators: async (deviceCode: string) => {
    return await request.getOriginal({
      url: `/monitor/onlineMonitor/get-all-indicators`,
      params: { deviceCode }
    })
  },

  // 根据水厂获取所有可用的指标
  getIndicatorsByFactory: async (factoryCode: string) => {
    return await request.getOriginal({
      url: `/monitor/ods/pointName`,
      params: { factoryId: factoryCode }
    })
  }
}
